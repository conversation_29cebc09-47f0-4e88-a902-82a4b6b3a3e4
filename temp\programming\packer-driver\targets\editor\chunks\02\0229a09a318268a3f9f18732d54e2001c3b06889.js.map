{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathPointEditor.ts"], "names": ["_decorator", "Component", "Graphics", "Color", "Vec2", "CCFloat", "EDITOR", "PathPoint", "ccclass", "property", "executeInEditMode", "disallowMultiple", "menu", "requireComponent", "PathPointEditor", "type", "displayName", "_graphics", "selected", "graphics", "node", "getComponent", "addComponent", "onFocusInEditor", "onLostFocusInEditor", "pathPoint", "_pathPoint", "position", "x", "y", "value", "setPosition", "updateDisplay", "onLoad", "clear", "color", "YELLOW", "WHITE", "fillColor", "strokeColor", "BLACK", "lineWidth", "circle", "pointSize", "fill", "stroke", "smoothness", "GREEN", "radius", "speed", "BLUE", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "moveTo", "lineTo", "update", "_dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;;AACpDC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA,IAA1D;AAAgEC,QAAAA;AAAhE,O,GAAqFb,U;;iCAO9Ec,e,WALZN,OAAO,CAAC,iBAAD,C,UACPI,IAAI,CAAC,YAAD,C,UACJC,gBAAgB,CAACX,QAAD,C,UAChBQ,iBAAiB,CAAC,IAAD,C,UACjBC,gBAAgB,CAAC,IAAD,C,UAUZF,QAAQ,CAAC;AAAEM,QAAAA,IAAI;AAAA;AAAA,kCAAN;AAAmBC,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAGRP,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEV,OAAR;AAAiBW,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,uFAjBb,MAKaF,eALb,SAKqCb,SALrC,CAK+C;AAAA;AAAA;AAAA,eACnCgB,SADmC,GACN,IADM;;AAAA;;AAAA;;AAAA,eAenCC,QAfmC,GAef,KAfe;AAAA;;AAExB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKF,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKG,IAAL,CAAUC,YAAV,CAAuBnB,QAAvB,KAAoC,KAAKkB,IAAL,CAAUE,YAAV,CAAuBpB,QAAvB,CAArD;AACH;;AACD,iBAAO,KAAKe,SAAZ;AACH;;AASMM,QAAAA,eAAe,GAAS;AAC3B,eAAKL,QAAL,GAAgB,IAAhB;AACH;;AACMM,QAAAA,mBAAmB,GAAS;AAC/B,eAAKN,QAAL,GAAgB,KAAhB;AACH;;AAEmB,YAATO,SAAS,GAAc;AAC9B;AACA,eAAKC,UAAL,CAAgBC,QAAhB,GAA2B,IAAIvB,IAAJ,CAAS,KAAKgB,IAAL,CAAUO,QAAV,CAAmBC,CAA5B,EAA+B,KAAKR,IAAL,CAAUO,QAAV,CAAmBE,CAAlD,CAA3B;AACA,iBAAO,KAAKH,UAAZ;AACH;;AAEmB,YAATD,SAAS,CAACK,KAAD,EAAmB;AACnC,eAAKJ,UAAL,GAAkBI,KAAlB,CADmC,CAEnC;;AACA,eAAKV,IAAL,CAAUW,WAAV,CAAsB,KAAKL,UAAL,CAAgBC,QAAhB,CAAyBC,CAA/C,EAAkD,KAAKF,UAAL,CAAgBC,QAAhB,CAAyBE,CAA3E,EAA8E,CAA9E;AACA,eAAKG,aAAL;AACH;;AAESC,QAAAA,MAAM,GAAG;AACf,eAAKD,aAAL;AACH;;AAEMA,QAAAA,aAAa,GAAG;AACnB,gBAAMb,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACe,KAAT,GAFmB,CAInB;;AACA,gBAAMC,KAAK,GAAG,KAAKjB,QAAL,GAAgBf,KAAK,CAACiC,MAAtB,GAA+BjC,KAAK,CAACkC,KAAnD;AACAlB,UAAAA,QAAQ,CAACmB,SAAT,GAAqBH,KAArB;AACAhB,UAAAA,QAAQ,CAACoB,WAAT,GAAuBpC,KAAK,CAACqC,KAA7B;AACArB,UAAAA,QAAQ,CAACsB,SAAT,GAAqB,CAArB;AAEAtB,UAAAA,QAAQ,CAACuB,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsB,KAAKC,SAA3B;AACAxB,UAAAA,QAAQ,CAACyB,IAAT;AACAzB,UAAAA,QAAQ,CAAC0B,MAAT,GAZmB,CAcnB;;AACA,cAAI,KAAKnB,UAAL,CAAgBoB,UAAhB,GAA6B,CAAjC,EAAoC;AAChC3B,YAAAA,QAAQ,CAACoB,WAAT,GAAuBpC,KAAK,CAAC4C,KAA7B;AACA5B,YAAAA,QAAQ,CAACsB,SAAT,GAAqB,CAArB;AACA,kBAAMO,MAAM,GAAG,KAAKL,SAAL,GAAiB,CAAjB,GAAqB,KAAKjB,UAAL,CAAgBoB,UAAhB,GAA6B,EAAjE;AACA3B,YAAAA,QAAQ,CAACuB,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsBM,MAAtB;AACA7B,YAAAA,QAAQ,CAAC0B,MAAT;AACH,WArBkB,CAuBnB;;;AACA,cAAI,KAAKnB,UAAL,CAAgBuB,KAAhB,GAAwB,CAA5B,EAA+B;AAC3B9B,YAAAA,QAAQ,CAACoB,WAAT,GAAuBpC,KAAK,CAAC+C,IAA7B;AACA/B,YAAAA,QAAQ,CAACsB,SAAT,GAAqB,CAArB;AACA,kBAAMU,WAAW,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAK3B,UAAL,CAAgBuB,KAAhB,GAAwB,EAAjC,EAAqC,GAArC,CAApB;AAEA9B,YAAAA,QAAQ,CAACmC,MAAT,CAAgB,CAAhB,EAAmB,CAAnB;AACAnC,YAAAA,QAAQ,CAACoC,MAAT,CAAgBJ,WAAhB,EAA6B,CAA7B;AACAhC,YAAAA,QAAQ,CAACmC,MAAT,CAAgBH,WAAW,GAAG,CAA9B,EAAiC,CAAC,CAAlC;AACAhC,YAAAA,QAAQ,CAACoC,MAAT,CAAgBJ,WAAhB,EAA6B,CAA7B;AACAhC,YAAAA,QAAQ,CAACoC,MAAT,CAAgBJ,WAAW,GAAG,CAA9B,EAAiC,CAAjC;AACAhC,YAAAA,QAAQ,CAAC0B,MAAT;AACH;AACJ;;AAEMW,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB,cAAInD,MAAJ,EAAY;AACR,iBAAK0B,aAAL;AACH;AACJ;;AAlF0C,O;;;;;iBAUX;AAAA;AAAA,uC;;;;;;;iBAGL,E", "sourcesContent": ["import { _decorator, Component, Graphics, Color, Vec3, Vec2, CCFloat, input, Input, EventMouse, Camera, find } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\r\n\r\nconst { ccclass, property, executeInEditMode, disallowMultiple, menu, requireComponent } = _decorator;\r\n\r\n@ccclass('PathPointEditor')\r\n@menu(\"怪物/编辑器/路径点\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathPointEditor extends Component {\r\n    private _graphics: Graphics | null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n        return this._graphics;\r\n    }\r\n\r\n    @property({ type: PathPoint, displayName: \"路径点数据\" })\r\n    private _pathPoint: PathPoint = new PathPoint();\r\n\r\n    @property({ type: CCFloat, displayName: \"点大小\" })\r\n    public pointSize: number = 20;\r\n\r\n    private selected: boolean = false;\r\n    public onFocusInEditor(): void {\r\n        this.selected = true;\r\n    }\r\n    public onLostFocusInEditor(): void {\r\n        this.selected = false;\r\n    }\r\n\r\n    public get pathPoint(): PathPoint {\r\n        // 同步节点位置到路径点数据\r\n        this._pathPoint.position = new Vec2(this.node.position.x, this.node.position.y);\r\n        return this._pathPoint;\r\n    }\r\n\r\n    public set pathPoint(value: PathPoint) {\r\n        this._pathPoint = value;\r\n        // 同步路径点数据到节点位置\r\n        this.node.setPosition(this._pathPoint.position.x, this._pathPoint.position.y, 0);\r\n        this.updateDisplay();\r\n    }\r\n\r\n    protected onLoad() {\r\n        this.updateDisplay();\r\n    }\r\n\r\n    public updateDisplay() {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n\r\n        // 绘制点\r\n        const color = this.selected ? Color.YELLOW : Color.WHITE;\r\n        graphics.fillColor = color;\r\n        graphics.strokeColor = Color.BLACK;\r\n        graphics.lineWidth = 5;\r\n\r\n        graphics.circle(0, 0, this.pointSize);\r\n        graphics.fill();\r\n        graphics.stroke();\r\n\r\n        // 绘制平滑程度指示器\r\n        if (this._pathPoint.smoothness > 0) {\r\n            graphics.strokeColor = Color.GREEN;\r\n            graphics.lineWidth = 5;\r\n            const radius = this.pointSize + 5 + this._pathPoint.smoothness * 10;\r\n            graphics.circle(0, 0, radius);\r\n            graphics.stroke();\r\n        }\r\n\r\n        // 绘制速度指示器（箭头）\r\n        if (this._pathPoint.speed > 0) {\r\n            graphics.strokeColor = Color.BLUE;\r\n            graphics.lineWidth = 3;\r\n            const arrowLength = Math.min(this._pathPoint.speed / 10, 100);\r\n\r\n            graphics.moveTo(0, 0);\r\n            graphics.lineTo(arrowLength, 0);\r\n            graphics.moveTo(arrowLength - 5, -5);\r\n            graphics.lineTo(arrowLength, 0);\r\n            graphics.lineTo(arrowLength - 5, 5);\r\n            graphics.stroke();\r\n        }\r\n    }\r\n\r\n    public update(_dt: number) {\r\n        if (EDITOR) {\r\n            this.updateDisplay();\r\n        }\r\n    }\r\n}"]}