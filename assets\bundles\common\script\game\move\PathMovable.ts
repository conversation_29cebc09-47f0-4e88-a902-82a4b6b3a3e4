import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';
import { BulletSystem } from '../bullet/BulletSystem';
import { IMovable } from './IMovable';
import Entity from '../ui/base/Entity';
import { PathData } from '../data/PathData';
import { eSpriteDefaultFacing, eMoveEvent } from './Movable';

const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property } = _decorator;

@ccclass('PathMovable')
export class PathMovable extends Component implements IMovable {
    public speedAngle: number = 0;
    public accelerationAngle: number = 0;

    @property({ type: JsonAsset, displayName: "路径数据" })
    public pathAsset: JsonAsset | null = null;

    @property({ type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向' })
    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;

    @property({ displayName: "是否朝向移动方向" })
    public isFacingMoveDir: boolean = true;

    @property({ displayName: "移动速度", tooltip: "沿路径移动的速度(像素/秒)" })
    public speed: number = 100;

    @property({ displayName: "加速度", tooltip: "速度变化率(像素/秒²)" })
    public acceleration: number = 0;

    @property({ displayName: "循环移动" })
    public loop: boolean = false;

    @property({ displayName: "反向移动" })
    public reverse: boolean = false;

    @property({ displayName: "振荡偏移速度", tooltip: "控制倾斜振荡的频率" })
    public tiltSpeed: number = 0;

    @property({ displayName: "振荡偏移幅度", tooltip: "控制倾斜振荡的幅度" })
    public tiltOffset: number = 0;

    // 路径相关数据
    private _pathData: PathData | null = null;
    private _curvePoints: Vec2[] = [];
    private _totalDistance: number = 0;
    private _distances: number[] = [];

    // 移动状态
    private _currentDistance: number = 0;
    private _isMovable: boolean = true;
    private _tiltTime: number = 0;

    // 可见性检查
    private _selfSize: Vec2 = new Vec2();
    private _wasVisible: boolean = false;
    private _isVisible: boolean = false;
    private _visibilityCheckCounter: number = 0;
    private static readonly VISIBILITY_CHECK_INTERVAL = 5;

    // 事件系统
    private _eventListeners: Map<eMoveEvent, Array<() => void>> = new Map();

    public get isVisible() { return this._isVisible; }
    public get isMovable() { return this._isMovable; }

    onLoad() {
        const uiTransform = this.node.getComponent(UITransform);
        const self_size = uiTransform ? uiTransform.contentSize : { width: 0, height: 0 };
        this._selfSize.set(self_size.width / 2, self_size.height / 2);

        this.loadPathData();
    }

    onDestroy() {
        this._eventListeners.clear();
    }

    /**
     * 加载路径数据（使用PathData内置缓存）
     */
    private loadPathData() {
        if (!this.pathAsset) return;

        // 创建PathData实例并加载数据
        this._pathData = PathData.fromJSON(this.pathAsset.json);

        if (this._pathData.points.length < 2) return;

        // 使用PathData的内置缓存获取曲线点和距离信息
        this._curvePoints = this._pathData.generateCurvePoints();
        this._totalDistance = this._pathData.getTotalDistance();
        this._distances = this._pathData.getDistances();
    }



    /**
     * 主要的移动更新逻辑
     */
    public tick(dt: number): void {
        if (!this._isMovable || this._curvePoints.length < 2) return;

        // 应用加速度
        if (this.acceleration !== 0) {
            this.speed += this.acceleration * dt;
            this.speed = Math.max(0, this.speed); // 确保速度不为负
        }

        // 更新沿路径的距离
        const deltaDistance = this.speed * dt;
        
        if (this.reverse) {
            this._currentDistance -= deltaDistance;
            if (this._currentDistance < 0) {
                if (this.loop) {
                    this._currentDistance = this._totalDistance + this._currentDistance;
                } else {
                    this._currentDistance = 0;
                }
            }
        } else {
            this._currentDistance += deltaDistance;
            if (this._currentDistance > this._totalDistance) {
                if (this.loop) {
                    this._currentDistance = this._currentDistance - this._totalDistance;
                } else {
                    this._currentDistance = this._totalDistance;
                }
            }
        }

        this.updatePosition(dt);
    }

    /**
     * 更新节点位置和朝向
     */
    private updatePosition(dt: number) {
        const position = this.getPositionAtDistance(this._currentDistance);
        
        // 应用倾斜偏移
        if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
            this._tiltTime += dt;
            
            const direction = this.getDirectionAtDistance(this._currentDistance);
            if (direction.lengthSqr() > 0.001) {
                // 计算垂直于移动方向的向量
                const perpX = -direction.y;
                const perpY = direction.x;
                
                // 计算倾斜偏移
                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;
                
                position.x += perpX * tiltAmount;
                position.y += perpY * tiltAmount;
            }
        }

        this.node.setPosition(position.x, position.y, 0);

        // 更新朝向
        if (this.isFacingMoveDir) {
            const direction = this.getDirectionAtDistance(this._currentDistance);
            if (direction.lengthSqr() > 0.001) {
                const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
                const finalAngle = angle + this.defaultFacing;
                this.node.setRotationFromEuler(0, 0, finalAngle);
            }
        }

        // 可见性检查
        if (++this._visibilityCheckCounter >= PathMovable.VISIBILITY_CHECK_INTERVAL) {
            this._visibilityCheckCounter = 0;
            this.checkVisibility();
        }
    }

    /**
     * 根据距离获取位置
     */
    private getPositionAtDistance(distance: number): Vec2 {
        if (distance <= 0) return this._curvePoints[0].clone();
        if (distance >= this._totalDistance) return this._curvePoints[this._curvePoints.length - 1].clone();

        for (let i = 1; i < this._distances.length; i++) {
            if (distance <= this._distances[i]) {
                const segmentStart = this._distances[i - 1];
                const segmentEnd = this._distances[i];
                const segmentLength = segmentEnd - segmentStart;

                if (segmentLength === 0) return this._curvePoints[i - 1].clone();

                const t = (distance - segmentStart) / segmentLength;
                return Vec2.lerp(new Vec2(), this._curvePoints[i - 1], this._curvePoints[i], t);
            }
        }

        return this._curvePoints[this._curvePoints.length - 1].clone();
    }

    /**
     * 根据距离获取移动方向
     */
    private getDirectionAtDistance(distance: number): Vec2 {
        const epsilon = 1;
        const pos1 = this.getPositionAtDistance(distance);
        const pos2 = this.getPositionAtDistance(distance + epsilon);

        return Vec2.subtract(new Vec2(), pos2, pos1).normalize();
    }

    /**
     * 可见性检查
     */
    public checkVisibility(): void {
        const visibleSize = BulletSystem.worldBounds;
        const position = this.node.worldPosition;
        const isVisible = (position.x + this._selfSize.x) >= visibleSize.xMin &&
            (position.x - this._selfSize.x) <= visibleSize.xMax &&
            (position.y - this._selfSize.y) <= visibleSize.yMax &&
            (position.y + this._selfSize.y) >= visibleSize.yMin;

        this.setVisible(isVisible);
    }

    private setVisible(visible: boolean) {
        if (visible) {
            if (!this._wasVisible)
                this.emit(eMoveEvent.onBecomeVisible);
        } else {
            if (this._wasVisible)
                this.emit(eMoveEvent.onBecomeInvisible);
        }
        this._wasVisible = this._isVisible;
        this._isVisible = visible;
    }

    // 事件系统方法
    public on(event: eMoveEvent, listener: () => void): void {
        if (!this._eventListeners.has(event)) {
            this._eventListeners.set(event, []);
        }
        const listeners = this._eventListeners.get(event)!;
        if (!listeners.includes(listener)) {
            listeners.push(listener);
        }
    }

    public off(event: eMoveEvent, listener: () => void): void {
        const listeners = this._eventListeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(listener);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }

    public removeAllListeners(): void {
        this._eventListeners.clear();
    }

    private emit(event: eMoveEvent): void {
        const listeners = this._eventListeners.get(event);
        if (listeners && listeners.length > 0) {
            listeners.forEach(listener => listener());
        }
    }

    // 公共API方法
    public setMovable(movable: boolean): PathMovable {
        this._isMovable = movable;
        return this;
    }

    /**
     * 设置路径进度 [0-1]
     */
    public setProgress(progress: number): PathMovable {
        this._currentDistance = Math.max(0, Math.min(1, progress)) * this._totalDistance;
        return this;
    }

    /**
     * 获取当前进度 [0-1]
     */
    public getProgress(): number {
        return this._totalDistance > 0 ? this._currentDistance / this._totalDistance : 0;
    }

    /**
     * 重置到路径起点
     */
    public resetToStart(): PathMovable {
        this._currentDistance = 0;
        return this;
    }

    /**
     * 移动到路径终点
     */
    public moveToEnd(): PathMovable {
        this._currentDistance = this._totalDistance;
        return this;
    }

    /**
     * 获取当前位置对应的路径点数据
     */
    public getCurrentPathPointData() {
        if (!this._pathData || this._pathData.points.length === 0) return null;

        const progress = this.getProgress();
        const pointIndex = Math.floor(progress * (this._pathData.points.length - 1));
        const clampedIndex = Math.max(0, Math.min(this._pathData.points.length - 1, pointIndex));
        
        return this._pathData.points[clampedIndex];
    }


}
