# PathData 内置缓存系统

## 设计理念

将缓存逻辑直接集成到PathData类中，而不是使用独立的缓存管理器。这种设计更符合面向对象的原则，数据和行为封装在一起。

## 核心优势

### 🎯 面向对象设计
- **数据封装**: 缓存逻辑与数据紧密结合
- **职责单一**: PathData负责自己的缓存管理
- **接口简洁**: 外部使用者无需关心缓存细节

### 🚀 性能优化
- **智能缓存**: 只有数据变化时才重新计算
- **版本控制**: 基于数据内容的哈希值检测变化
- **延迟计算**: 只在需要时生成曲线点

### 💾 序列化控制
- **自定义序列化**: 排除缓存数据，只保存核心数据
- **自动清理**: 反序列化时自动清除缓存
- **数据一致性**: 确保缓存与实际数据同步

## 实现细节

### 缓存字段
```typescript
// 缓存的路径数据（不参与序列化）
private _cachedCurvePoints: Vec2[] | null = null;
private _cachedTotalDistance: number = 0;
private _cachedDistances: number[] = [];
private _cacheVersion: number = 0; // 用于检测数据变化
```

### 版本控制机制
```typescript
private getCurrentDataVersion(): number {
    let hash = 0;
    
    // 基于关键属性计算哈希
    hash = this.hashString(this.name, hash);
    hash = this.hashNumber(this.segments, hash);
    hash = this.hashBoolean(this.closed, hash);
    
    // 基于所有路径点计算哈希
    for (const point of this.points) {
        hash = this.hashNumber(point.position.x, hash);
        hash = this.hashNumber(point.position.y, hash);
        hash = this.hashNumber(point.smoothness, hash);
        // ... 其他属性
    }
    
    return hash;
}
```

### 智能缓存检查
```typescript
public generateCurvePoints(): Vec2[] {
    // 检查缓存是否有效
    if (this._cachedCurvePoints && this.isCacheValid()) {
        return this._cachedCurvePoints;
    }

    // 生成新的曲线点并缓存
    const curvePoints = this.generateCurvePointsInternal();
    this._cachedCurvePoints = curvePoints;
    this.calculateAndCacheDistances();
    this.updateCacheVersion();

    return curvePoints;
}
```

## 序列化控制

### 自定义序列化
```typescript
public toJSON(): any {
    return {
        name: this.name,
        startIdx: this.startIdx,
        endIdx: this.endIdx,
        points: this.points,
        segments: this.segments,
        closed: this.closed
        // 注意：缓存字段被排除
    };
}
```

### 自定义反序列化
```typescript
public fromJSON(data: any): void {
    this.name = data.name || "";
    this.startIdx = data.startIdx || 0;
    this.endIdx = data.endIdx || -1;
    this.points = data.points || [];
    this.segments = data.segments || 20;
    this.closed = data.closed || false;
    
    // 清除缓存，确保使用新数据重新计算
    this.clearCache();
}
```

## 使用方式

### PathMovable中的使用
```typescript
private loadPathData() {
    if (!this.pathAsset) return;

    // 创建PathData实例并加载数据
    this._pathData = PathData.fromJSON(this.pathAsset.json);
    
    // 使用PathData的内置缓存获取数据
    this._curvePoints = this._pathData.generateCurvePoints();
    this._totalDistance = this._pathData.getTotalDistance();
    this._distances = this._pathData.getDistances();
}
```

### 多个组件共享同一路径
```typescript
// 第一个组件：生成并缓存数据
const pathData1 = PathData.fromJSON(pathAsset.json);
const curvePoints1 = pathData1.generateCurvePoints(); // 计算并缓存

// 第二个组件：使用相同的PathData实例
const pathData2 = PathData.fromJSON(pathAsset.json);
const curvePoints2 = pathData2.generateCurvePoints(); // 直接使用缓存
```

## 缓存生命周期

### 1. 缓存创建
- 首次调用`generateCurvePoints()`时创建
- 计算曲线点、总距离、距离数组
- 记录当前数据版本

### 2. 缓存验证
- 每次访问时检查数据版本
- 如果数据未变化，直接返回缓存
- 如果数据已变化，重新计算

### 3. 缓存失效
- 数据修改时自动失效
- 调用`clearCache()`手动清除
- 反序列化时自动清除

## 性能特性

### 内存效率
- **按需分配**: 只有使用时才分配缓存内存
- **自动清理**: 数据变化时自动清理旧缓存
- **版本控制**: 避免不必要的重新计算

### CPU效率
- **哈希检查**: 快速检测数据变化
- **延迟计算**: 只在需要时计算
- **批量缓存**: 一次计算，多次使用

### 对比独立缓存管理器

| 特性 | 独立缓存管理器 | PathData内置缓存 |
|------|---------------|------------------|
| **设计复杂度** | 高（需要额外的管理类） | 低（集成在数据类中） |
| **内存管理** | 需要手动清理 | 自动管理 |
| **数据一致性** | 可能不同步 | 始终同步 |
| **序列化控制** | 复杂 | 简单 |
| **使用便利性** | 需要了解缓存API | 透明使用 |

## 最佳实践

### 1. 数据修改后清理缓存
```typescript
// 修改路径数据后
pathData.points.push(newPoint);
pathData.clearCache(); // 确保缓存失效
```

### 2. 批量修改优化
```typescript
// 批量修改时，最后统一清理缓存
pathData.points = newPoints;
pathData.segments = newSegments;
pathData.closed = newClosed;
pathData.clearCache(); // 一次性清理
```

### 3. 共享PathData实例
```typescript
// 推荐：多个组件共享同一个PathData实例
const sharedPathData = PathData.fromJSON(pathAsset.json);

enemies.forEach(enemy => {
    const pathMovable = enemy.getComponent(PathMovable);
    pathMovable.setPathData(sharedPathData); // 共享实例
});
```

## 调试技巧

### 缓存命中率监控
```typescript
// 在generateCurvePoints中添加日志
public generateCurvePoints(): Vec2[] {
    if (this._cachedCurvePoints && this.isCacheValid()) {
        console.log("Cache hit for path:", this.name);
        return this._cachedCurvePoints;
    }
    
    console.log("Cache miss for path:", this.name);
    // ... 重新计算
}
```

### 版本变化追踪
```typescript
// 监控数据版本变化
const oldVersion = this._cacheVersion;
const newVersion = this.getCurrentDataVersion();
if (oldVersion !== newVersion) {
    console.log(`Path data changed: ${oldVersion} -> ${newVersion}`);
}
```

这种设计让PathData成为一个自包含的、高效的数据类，既保持了性能优化，又简化了使用方式。
