import { _decorator, Component, Graphics, Color, Vec3, CCFloat, input, Input, EventMouse, Camera, find } from 'cc';
import { EDITOR } from 'cc/env';
import { PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';

const { ccclass, property, executeInEditMode, disallowMultiple, menu, requireComponent } = _decorator;

@ccclass('PathPointEditor')
@menu("怪物/编辑器/路径点")
@requireComponent(Graphics)
@executeInEditMode(true)
@disallowMultiple(true)
export class PathPointEditor extends Component {
    private _graphics: Graphics | null = null;
    public get graphics(): Graphics {
        if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
        }
        return this._graphics;
    }

    @property({ type: PathPoint, displayName: "路径点数据" })
    private _pathPoint: PathPoint = new PathPoint();

    @property({ type: CCFloat, displayName: "点大小" })
    public pointSize: number = 20;

    @property({ displayName: "是否选中" })
    public selected: boolean = false;

    private _isDragging: boolean = false;
    private _dragOffset: Vec3 = new Vec3();

    public get pathPoint(): PathPoint {
        // 同步节点位置到路径点数据
        this._pathPoint.position = this.node.position;
        return this._pathPoint;
    }

    public set pathPoint(value: PathPoint) {
        this._pathPoint = value;
        // 同步路径点数据到节点位置
        this.node.position = this._pathPoint.position;
        this.updateDisplay();
    }

    protected onLoad() {
        if (EDITOR) {
            this.setupEditorEvents();
        }
        this.updateDisplay();
    }

    protected onDestroy() {
        if (EDITOR) {
            this.removeEditorEvents();
        }
    }

    private setupEditorEvents() {
        if (EDITOR) {
            input.on(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);
            input.on(Input.EventType.MOUSE_MOVE, this.onMouseMove, this);
            input.on(Input.EventType.MOUSE_UP, this.onMouseUp, this);
        }
    }

    private removeEditorEvents() {
        if (EDITOR) {
            input.off(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);
            input.off(Input.EventType.MOUSE_MOVE, this.onMouseMove, this);
            input.off(Input.EventType.MOUSE_UP, this.onMouseUp, this);
        }
    }

    private onMouseDown(event: EventMouse) {
        if (!EDITOR) return;

        const screenPos = event.getLocation();
        const worldPos = this.screenToWorld(new Vec3(screenPos.x, screenPos.y, 0));
        const distance = Vec3.distance(worldPos, this.node.worldPosition);

        if (distance <= this.pointSize) {
            this._isDragging = true;
            this._dragOffset = Vec3.subtract(new Vec3(), this.node.worldPosition, worldPos);
            this.selected = true;
            this.updateDisplay();

            // 通知父编辑器选中了这个点
            const pathEditor = this.node.parent?.getComponent('PathEditor');
            if (pathEditor) {
                // @ts-ignore
                pathEditor.selectPoint(this);
            }
        }
    }

    private onMouseMove(event: EventMouse) {
        if (!EDITOR || !this._isDragging) return;

        const screenPos = event.getLocation();
        const worldPos = this.screenToWorld(new Vec3(screenPos.x, screenPos.y, 0));
        const newPos = Vec3.add(new Vec3(), worldPos, this._dragOffset);

        // 转换为本地坐标
        if (this.node.parent) {
            this.node.parent.inverseTransformPoint(newPos, newPos);
        }

        this.node.position = newPos;
        this._pathPoint.position = newPos;

        // 通知父编辑器更新曲线
        const pathEditor = this.node.parent?.getComponent('PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.updateCurve();
        }
    }

    private onMouseUp(_event: EventMouse) {
        if (!EDITOR) return;
        this._isDragging = false;
    }

    private screenToWorld(screenPos: Vec3): Vec3 {
        const cameraNode = find('Main Camera');
        const camera = cameraNode?.getComponent(Camera);
        if (!camera) return new Vec3();

        const worldPos = new Vec3();
        camera.screenToWorld(screenPos, worldPos);
        return worldPos;
    }

    public updateDisplay() {
        const graphics = this.graphics;
        graphics.clear();

        // 绘制点
        const color = this.selected ? Color.YELLOW : Color.WHITE;
        graphics.fillColor = color;
        graphics.strokeColor = Color.BLACK;
        graphics.lineWidth = 2;

        graphics.circle(0, 0, this.pointSize);
        graphics.fill();
        graphics.stroke();

        // 绘制平滑程度指示器
        if (this._pathPoint.smoothness > 0) {
            graphics.strokeColor = Color.GREEN;
            graphics.lineWidth = 1;
            const radius = this.pointSize + 5 + this._pathPoint.smoothness * 10;
            graphics.circle(0, 0, radius);
            graphics.stroke();
        }

        // 绘制速度指示器（箭头）
        if (this._pathPoint.speed > 0) {
            graphics.strokeColor = Color.BLUE;
            graphics.lineWidth = 2;
            const arrowLength = Math.min(this._pathPoint.speed / 10, 50);

            graphics.moveTo(0, 0);
            graphics.lineTo(arrowLength, 0);
            graphics.moveTo(arrowLength - 5, -3);
            graphics.lineTo(arrowLength, 0);
            graphics.lineTo(arrowLength - 5, 3);
            graphics.stroke();
        }
    }

    public update(_dt: number) {
        if (EDITOR) {
            this.updateDisplay();
        }
    }
}