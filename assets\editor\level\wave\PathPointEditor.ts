import { _decorator, Component, Graphics, Color, CCInteger, Vec2, CCFloat, Enum } from 'cc';
import { EDITOR } from 'cc/env';
import { PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';
import { eOrientationType } from 'db://assets/bundles/common/script/game/data/WaveData';

const { ccclass, property, executeInEditMode, disallowMultiple, menu, requireComponent } = _decorator;

@ccclass('PathPointEditor')
@menu("怪物/编辑器/路径点")
@requireComponent(Graphics)
@executeInEditMode(true)
@disallowMultiple(true)
export class PathPointEditor extends Component {
    private _graphics: Graphics | null = null;
    public get graphics(): Graphics {
        if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
        }
        return this._graphics;
    }

    // @property({ type: CCFloat, displayName: "点大小" })
    public pointSize: number = 20;

    @property({ type: CCFloat, displayName: "平滑程度", range: [0, 1], step:0.1, slide: true, tooltip: "0=尖锐转角,1=最大平滑" })
    public get smoothness(): number {
        return this._pathPoint.smoothness;
    }
    public set smoothness(value: number) {
        this._pathPoint.smoothness = value;
    }
    @property({ type: CCInteger, displayName: "速度", tooltip: "飞机在此点的速度" })
    public get speed(): number {
        return this._pathPoint.speed;
    }
    public set speed(value: number) {
        this._pathPoint.speed = value;
    }

    @property({ type: Enum(eOrientationType), displayName: "朝向类型", tooltip: "飞机在此点的朝向" })
    public get orientationType(): number {
        return this._pathPoint.orientationType;
    }
    public set orientationType(value: number) {
        this._pathPoint.orientationType = value;
    }

    @property({ type: CCInteger, displayName: "朝向参数", tooltip: "根据朝向类型不同而不同" })
    public get orientationParam(): number {
        return this._pathPoint.orientationParam;
    }
    public set orientationParam(value: number) {
        this._pathPoint.orientationParam = value;
    }

    private _pathPoint: PathPoint = new PathPoint();
    private selected: boolean = false;
    public onFocusInEditor(): void {
        this.selected = true;
    }
    public onLostFocusInEditor(): void {
        this.selected = false;
    }

    public get pathPoint(): PathPoint {
        // 同步节点位置到路径点数据
        this._pathPoint.position = new Vec2(this.node.position.x, this.node.position.y);
        return this._pathPoint;
    }

    public set pathPoint(value: PathPoint) {
        this._pathPoint = value;
        // 同步路径点数据到节点位置
        this.node.setPosition(this._pathPoint.position.x, this._pathPoint.position.y, 0);
        this.updateDisplay();
    }

    protected onLoad() {
        this.updateDisplay();
    }

    public updateDisplay() {
        const graphics = this.graphics;
        graphics.clear();

        // 绘制点
        const color = this.selected ? Color.YELLOW : Color.WHITE;
        graphics.fillColor = color;
        graphics.strokeColor = Color.BLACK;
        graphics.lineWidth = 5;

        graphics.circle(0, 0, this.pointSize);
        graphics.fill();
        graphics.stroke();

        // 绘制平滑程度指示器
        if (this._pathPoint.smoothness > 0) {
            graphics.strokeColor = Color.GREEN;
            graphics.lineWidth = 5;
            const radius = this.pointSize + 5 + this._pathPoint.smoothness * 10;
            graphics.circle(0, 0, radius);
            graphics.stroke();
        }

        // 绘制速度指示器（箭头）
        if (this._pathPoint.speed > 0) {
            graphics.strokeColor = Color.BLUE;
            graphics.lineWidth = 3;
            const arrowLength = Math.min(this._pathPoint.speed / 10, 100);

            graphics.moveTo(0, 0);
            graphics.lineTo(arrowLength, 0);
            graphics.moveTo(arrowLength - 5, -5);
            graphics.lineTo(arrowLength, 0);
            graphics.lineTo(arrowLength - 5, 5);
            graphics.stroke();
        }
    }

    public update(_dt: number) {
        if (EDITOR) {
            this.updateDisplay();

            const siblingIndex = this.node.getSiblingIndex();
            if (siblingIndex !== this._cachedIndex) {
                this._cachedIndex = siblingIndex;
                this.node.name = `Point_${siblingIndex}`;
            }
        }
    }
}