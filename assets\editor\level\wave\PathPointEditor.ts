import { _decorator, Component, Graphics, Color, CCInteger, Vec2, CCFloat, Enum } from 'cc';
import { EDITOR } from 'cc/env';
import { PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';
import { eOrientationType } from 'db://assets/bundles/common/script/game/data/WaveData';

const { ccclass, property, executeInEditMode, disallowMultiple, menu, requireComponent } = _decorator;

@ccclass('PathPointEditor')
@menu("怪物/编辑器/路径点")
@requireComponent(Graphics)
@executeInEditMode(true)
@disallowMultiple(true)
export class PathPointEditor extends Component {
    private _graphics: Graphics | null = null;
    public get graphics(): Graphics {
        if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
        }
        return this._graphics;
    }

    // @property({ type: CCFloat, displayName: "点大小" })
    public pointSize: number = 20;

    @property({ type: CCFloat, displayName: "平滑程度", range: [0, 2], step: 0.1, slide: true, tooltip: "0=尖锐转角,1=最大平滑" })
    public get smoothness(): number {
        return this._pathPoint.smoothness;
    }
    public set smoothness(value: number) {
        this._pathPoint.smoothness = value;
    }
    @property({ type: CCInteger, displayName: "速度", tooltip: "飞机在此点的速度" })
    public get speed(): number {
        return this._pathPoint.speed;
    }
    public set speed(value: number) {
        this._pathPoint.speed = value;
    }

    @property({ type: Enum(eOrientationType), displayName: "朝向类型", tooltip: "飞机在此点的朝向" })
    public get orientationType(): number {
        return this._pathPoint.orientationType;
    }
    public set orientationType(value: number) {
        this._pathPoint.orientationType = value;
    }

    @property({ type: CCInteger, displayName: "朝向参数", tooltip: "固定朝向时：角度值(0-360度)；其他类型暂未使用" })
    public get orientationParam(): number {
        return this._pathPoint.orientationParam;
    }
    public set orientationParam(value: number) {
        this._pathPoint.orientationParam = value;
    }

    private _pathPoint: PathPoint = new PathPoint();
    private _cachedIndex: number = -1;    
    private selected: boolean = false;
    public onFocusInEditor(): void {
        this.selected = true;
    }
    public onLostFocusInEditor(): void {
        this.selected = false;
    }

    public get pathPoint(): PathPoint {
        // 同步节点位置到路径点数据
        this._pathPoint.position = new Vec2(this.node.position.x, this.node.position.y);
        return this._pathPoint;
    }

    public set pathPoint(value: PathPoint) {
        this._pathPoint = value;
        // 同步路径点数据到节点位置
        this.node.setPosition(this._pathPoint.position.x, this._pathPoint.position.y, 0);
        this.updateDisplay();
    }
    
    public updateDisplay() {
        const graphics = this.graphics;
        graphics.clear();

        // 绘制点
        const color = this.selected ? Color.YELLOW : this.getDefaultColorByIndex(this._cachedIndex, this.node.parent!.children.length);
        graphics.fillColor = color;
        graphics.strokeColor = Color.BLACK;
        graphics.lineWidth = 5;

        graphics.circle(0, 0, this.pointSize);
        graphics.fill();
        graphics.stroke();

        // 绘制平滑程度指示器
        if (this._pathPoint.smoothness > 0) {
            graphics.strokeColor = Color.GREEN;
            graphics.lineWidth = 5;
            const radius = this.pointSize + 5 + this._pathPoint.smoothness * 10;
            graphics.circle(0, 0, radius);
            graphics.stroke();
        }

        // 绘制朝向指示器（箭头）
        if (this._pathPoint.speed > 0) {
            this.drawOrientationArrow(graphics);
        }
    }

    public update(_dt: number) {
        this.updateDisplay();

        const siblingIndex = this.node.getSiblingIndex();
        if (siblingIndex !== this._cachedIndex) {
            this._cachedIndex = siblingIndex;
            this.node.name = `Point_${siblingIndex}`;
        }
    }

    getDefaultColorByIndex(index: number, count: number) {
        // 起点
        if (index === 0) return Color.GREEN;
        // 终点
        else if (index === count - 1) return Color.RED;
        // 中间点
        else return Color.WHITE;
    }

    /**
     * 绘制朝向箭头
     */
    private drawOrientationArrow(graphics: Graphics) {
        const arrowLength = Math.min(this._pathPoint.speed / 10, 100);
        const arrowAngle = this.calculateArrowAngle();

        // 根据朝向类型设置不同颜色
        graphics.strokeColor = this.getArrowColorByOrientationType();
        graphics.lineWidth = 3;

        // 计算箭头终点
        const endX = Math.cos(arrowAngle) * arrowLength;
        const endY = Math.sin(arrowAngle) * arrowLength;

        // 绘制箭头主线
        graphics.moveTo(0, 0);
        graphics.lineTo(endX, endY);

        // 绘制箭头头部
        const arrowHeadLength = 8;
        const arrowHeadAngle = Math.PI / 6; // 30度

        const leftX = endX - Math.cos(arrowAngle - arrowHeadAngle) * arrowHeadLength;
        const leftY = endY - Math.sin(arrowAngle - arrowHeadAngle) * arrowHeadLength;
        const rightX = endX - Math.cos(arrowAngle + arrowHeadAngle) * arrowHeadLength;
        const rightY = endY - Math.sin(arrowAngle + arrowHeadAngle) * arrowHeadLength;

        graphics.moveTo(leftX, leftY);
        graphics.lineTo(endX, endY);
        graphics.lineTo(rightX, rightY);

        graphics.stroke();
    }

    /**
     * 根据朝向类型计算箭头角度
     */
    private calculateArrowAngle(): number {
        switch (this._pathPoint.orientationType) {
            case eOrientationType.FacingMoveDir:
                return this.calculateMovementDirection();

            case eOrientationType.FacingPlayer:
                return this.calculatePlayerDirection();

            case eOrientationType.Fixed:
                // orientationParam作为固定角度（度）
                return (this._pathPoint.orientationParam * Math.PI) / 180;

            default:
                return 0; // 默认向右
        }
    }

    /**
     * 计算移动方向
     */
    private calculateMovementDirection(): number {
        if (!this.node.parent) return 0;

        const siblings = this.node.parent.children;
        const currentIndex = this.node.getSiblingIndex();

        // 如果是第一个点，使用到下一个点的方向
        if (currentIndex === 0 && siblings.length > 1) {
            const nextPoint = siblings[1].position;
            const currentPoint = this.node.position;
            return Math.atan2(nextPoint.y - currentPoint.y, nextPoint.x - currentPoint.x);
        }
        // 如果是最后一个点，使用从上一个点的方向
        else if (currentIndex === siblings.length - 1 && siblings.length > 1) {
            const prevPoint = siblings[currentIndex - 1].position;
            const currentPoint = this.node.position;
            return Math.atan2(currentPoint.y - prevPoint.y, currentPoint.x - prevPoint.x);
        }
        // 中间点，使用前后两点的平均方向
        else if (currentIndex > 0 && currentIndex < siblings.length - 1) {
            const prevPoint = siblings[currentIndex - 1].position;
            const nextPoint = siblings[currentIndex + 1].position;
            return Math.atan2(nextPoint.y - prevPoint.y, nextPoint.x - prevPoint.x);
        }

        return 0; // 默认向右
    }

    /**
     * 计算朝向玩家的方向
     */
    private calculatePlayerDirection(): number {
        // 假设玩家在屏幕底部中央 (0, -400)
        // 在实际游戏中，这应该从游戏状态获取玩家位置
        const playerX = 0;
        const playerY = -400;

        const currentPoint = this.node.position;
        return Math.atan2(playerY - currentPoint.y, playerX - currentPoint.x);
    }

    /**
     * 根据朝向类型获取箭头颜色
     */
    private getArrowColorByOrientationType(): Color {
        switch (this._pathPoint.orientationType) {
            case eOrientationType.FacingMoveDir:
                return Color.BLUE;      // 蓝色：跟随移动方向

            case eOrientationType.FacingPlayer:
                return Color.MAGENTA;   // 紫色：朝向玩家

            case eOrientationType.Fixed:
                return new Color(255, 165, 0, 255);  // 橙色：固定朝向

            default:
                return Color.BLUE;      // 默认蓝色
        }
    }
}