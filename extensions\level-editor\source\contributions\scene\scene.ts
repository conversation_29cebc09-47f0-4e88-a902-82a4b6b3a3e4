import { join } from 'path';

// @ts-ignore
module.paths.push(join(Editor.App.path, 'node_modules'));

// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
import { Prefab, Node, director, instantiate, assetManager, Vec2, Vec3, Sprite, SpriteFrame, SpriteAtlas, UITransform, Size, Component } from 'cc';
const { _utils } = Prefab;

declare const cce: any;

export function load() {};
export function unload() {};

export const methods = {
    saveLevel() {
        console.log('saveLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if(levelEditorUI){
            levelEditorUI.save = true;
        }
    },
    playLevel() {
        console.log('playLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if(levelEditorUI){
            var play = levelEditorUI.play;
            levelEditorUI.play = !play;
        }
    },
    levelStart() {
        console.log('levelStart in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if(levelEditorUI){
            levelEditorUI.progress = 0;
        }
    },
    levelEnd() {
        console.log('levelEnd in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if(levelEditorUI){
            levelEditorUI.progress = 1;
        }
    },

    instantiatePrefab(component_uuid:string, prefabUuid: string) {
        // console.log('instantiatePrefab:', component_uuid, prefabUuid);

        let targetNode = director.getScene()?.getChildByUuid(component_uuid);
        if (!targetNode) {
            targetNode = director.getScene()?.getChildByName('Canvas');
        }

        if (targetNode) {
            // console.log("Canvas node found: ", targetNode.getComponent("EmitterEditor"));
            // @ts-ignore
            Editor.Message.request('scene', 'execute-component-method', {
                uuid: targetNode.getComponent("EmitterEditor")?.uuid,
                name: 'instantiatePrefab',
                args: [prefabUuid]
            });
        }
    },

    // async saveToPrefab(component_uuid:string, nodeName: string, prefabUuid: string) {
    //     // console.log('saveToPrefab:', component_uuid, nodeName, prefabUuid);

    //     const scene = director.getScene();
    //     const target = scene!.getChildByPath(`Canvas/${nodeName}`);
    //     if (!target) {
    //         console.error("node not found:", nodeName);
    //         return;
    //     }

    //     cce.Prefab.createPrefabAssetFromNode(target.uuid, prefabUuid);
    // },

    async createBulletPrefab(sourceAssetUuid: string) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo) return;

        // 需要判断是spriteList还是单个sprite
        // TODO: 当sourceAssetUuid是一个目录的时候，下面这里的路径可能存在bug。还没有验证。
        // TODO: 这个目录后续可能调整到bundle目录
        const targetPrefabDir = 'db://assets/resources/game/prefabs/bullet/';

        if (sourceAssetInfo.importer === 'sprite-atlas') {
            assetManager.loadAny({uuid: sourceAssetUuid}, (err, asset) => {
                if (err) {
                console.error(err);
                return;
                }
                
                const spriteList = asset as SpriteAtlas;
                for (let spriteFrame of spriteList.getSpriteFrames()) {
                    const targetPrefabName = spriteFrame.name;
                    const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                    createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, spriteList);
                }
            });
        }
        else if (sourceAssetInfo.importer === 'sprite-frame') {
            assetManager.loadAny({uuid: sourceAssetUuid}, (err, asset) => {
                if (err) {
                console.error(err);
                return;
                }
                
                const spriteFrame = asset as SpriteFrame;
                const targetPrefabName = spriteFrame.name;
                const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, null);
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },

    async createLevelPrefab(sourceAssetUuid: string) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo) return;
        
        // remove /x/x/*.* -> /x/x/
        let targetPrefabDir = sourceAssetInfo.path.replace('/Texture/', '/Prefab/');
        const targetPrefabPath = targetPrefabDir.substring(0, targetPrefabDir.lastIndexOf('/')) + '.prefab';
        const targetPrefabName = targetPrefabDir.substring(targetPrefabDir.lastIndexOf('/') + 1);
        
        if (sourceAssetInfo.importer === 'sprite-frame') {
            assetManager.loadAny({uuid: sourceAssetUuid}, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                
                // console.log(`Level Prefab will be created at path ${targetPrefabPath}`);
                const spriteFrame = asset as SpriteFrame;
                if (spriteFrame) {
                    createNewLevelObject(targetPrefabName, targetPrefabPath, spriteFrame); // 这个是创建prefab，不是prefab里面的节点，所以不需要parent
                } else {
                    console.warn('Asset is not a sprite-frame:', sourceAssetInfo);
                }
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },

    // 阵型相关: addFormationPoint & saveFormationGroup
    addFormationPoint(formationGroupUuid: string) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            formationEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    async saveFormationGroup(formationGroupUuid: string) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            const jsonData = formationEditor.formationData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: 'db://assets/resources/game/level/wave/formation/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }

                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, formationEditor.save()).then((res) => { 
                    if (res) {
                        assetManager.loadAny({uuid: res.uuid}, (err, asset) => {
                            if (err) {
                                console.error(err);
                            } else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, formationEditor.save()).then((res) => {
                    if (res) {
                        assetManager.loadAny({uuid: res.uuid}, (err, asset) => {
                            if (err) {
                                console.error(err);
                            } else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
};

function findComponentByUuid(node: Node, uuid:string, className:string): Component|null {
    if (!node) return null;

    // 检查当前节点是否有指定的组件，如果有且UUID匹配则返回
    const component = node.getComponent(className);
    if (component) {
        // console.log(`${node.name} has component ${className}, uuid: ${component.uuid}, target: ${uuid}`);
        if (component.uuid === uuid) {
            return component;
        }
    }

    // 无论当前节点是否有组件，都要搜索子节点
    for (let i = 0; i < node.children.length; i++) {
        let found = findComponentByUuid(node.children[i], uuid, className);
        if (found) {
            return found;
        }
    }

    return null;
}

async function createNewBullet(prefabName: string, prefabPath: string, sourceSprite: SpriteFrame, sourceSpriteList: SpriteAtlas) {
    const scene = director.getScene();
    const target = scene!.getChildByName('Canvas');
    if (!target) {
        console.error("Canvas node not found");
        return;
    }

    let node = new Node(prefabName);
    node.parent = target;
    node.setPosition(new Vec3(0, 0, 0));
    let uiTransform = node.addComponent(UITransform); // Ensure it has a transform component
    node.addComponent('FBoxCollider');
    let movable = node.addComponent('Movable');
    node.addComponent('Bullet');
    node.addComponent('cc.BoxCollider2D');
    let sprite = node.addComponent(Sprite);
    if (sourceSpriteList) {
        sprite.spriteFrame = sourceSprite;
        sprite.spriteAtlas = sourceSpriteList;
    }

    if (sourceSprite) {
        uiTransform.contentSize = new Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }

    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Bullet prefab created: ${prefabPath}`);

        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    } catch (e) {
        console.error('Failed to create bullet prefab:', e);
    }
}

async function createNewLevelObject(prefabName: string, prefabPath: string, sourceSprite: SpriteFrame) {
    const scene = director.getScene();

    let node = new Node(prefabName);
    scene.addChild(node);

    let uiTransform = node.addComponent(UITransform); // Ensure it has a transform component
    let sprite = node.addComponent(Sprite);
    if (sourceSprite) {
        sprite.spriteFrame = sourceSprite;
        uiTransform.contentSize = new Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }

    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Level prefab created: ${prefabPath}`);

        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    } catch (e) {
        console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
    }
}