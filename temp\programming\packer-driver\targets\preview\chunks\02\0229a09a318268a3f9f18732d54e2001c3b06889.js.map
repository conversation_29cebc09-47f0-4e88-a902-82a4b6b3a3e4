{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathPointEditor.ts"], "names": ["_decorator", "Component", "Graphics", "Color", "CCInteger", "Vec2", "CCFloat", "Enum", "PathPoint", "eOrientationType", "PathEditor", "ccclass", "property", "executeInEditMode", "disallowMultiple", "menu", "requireComponent", "PathPointEditor", "type", "displayName", "range", "step", "slide", "tooltip", "pointSize", "_pathEditor", "_pathPoint", "_cachedIndex", "selected", "smoothness", "value", "speed", "orientationType", "orientationParam", "pathEditor", "node", "parent", "getComponent", "onFocusInEditor", "onLostFocusInEditor", "pathPoint", "position", "x", "y", "setPosition", "updateDisplay", "graphics", "addComponent", "clear", "color", "YELLOW", "getDefaultColorByIndex", "children", "length", "fillColor", "strokeColor", "BLACK", "lineWidth", "circle", "fill", "stroke", "GREEN", "radius", "drawOrientationArrow", "update", "_dt", "currentPosition", "siblingIndex", "getSiblingIndex", "name", "index", "count", "startIdx", "endIdx", "RED", "WHITE", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "arrowAngle", "calculateArrowAngle", "getArrowColorByOrientationType", "endX", "cos", "endY", "sin", "moveTo", "lineTo", "arrowHeadLength", "arrowHeadAngle", "PI", "leftX", "leftY", "rightX", "rightY", "FacingMoveDir", "calculateMovementDirection", "FacingPlayer", "calculatePlayerDirection", "Fixed", "siblings", "currentIndex", "nextPoint", "currentPoint", "atan2", "prevPoint", "playerX", "playerY", "BLUE", "MAGENTA"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,I,OAAAA,I;;AAElEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA,IAA1D;AAAgEC,QAAAA;AAAhE,O,GAAqFhB,U;;iCAO9EiB,e,WALZN,OAAO,CAAC,iBAAD,C,UACPI,IAAI,CAAC,YAAD,C,UACJC,gBAAgB,CAACd,QAAD,C,UAChBW,iBAAiB,CAAC,IAAD,C,UACjBC,gBAAgB,CAAC,IAAD,C,UAKZF,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEZ,OAAR;AAAiBa,QAAAA,WAAW,EAAE,MAA9B;AAAsCC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAA7C;AAAqDC,QAAAA,IAAI,EAAE,GAA3D;AAAgEC,QAAAA,KAAK,EAAE,IAAvE;AAA6EC,QAAAA,OAAO,EAAE;AAAtF,OAAD,C,UAORX,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEd,SAAR;AAAmBe,QAAAA,WAAW,EAAE,IAAhC;AAAsCI,QAAAA,OAAO,EAAE;AAA/C,OAAD,C,UAQRX,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEX,IAAI;AAAA;AAAA,iDAAZ;AAAgCY,QAAAA,WAAW,EAAE,MAA7C;AAAqDI,QAAAA,OAAO,EAAE;AAA9D,OAAD,C,UAQRX,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEd,SAAR;AAAmBe,QAAAA,WAAW,EAAE,MAAhC;AAAwCI,QAAAA,OAAO,EAAE;AAAjD,OAAD,C,uFAhCb,MAKaN,eALb,SAKqChB,SALrC,CAK+C;AAAA;AAAA;AAC3C;AAD2C,eAEpCuB,SAFoC,GAEhB,EAFgB;AAAA,eAmCnCC,WAnCmC,GAmCJ,IAnCI;AAAA,eA2CnCC,UA3CmC,GA2CX;AAAA;AAAA,uCA3CW;AAAA,eA4CnCC,YA5CmC,GA4CZ,CAAC,CA5CW;AAAA,eA6CnCC,QA7CmC,GA6Cf,KA7Ce;AAAA;;AAKtB,YAAVC,UAAU,GAAW;AAC5B,iBAAO,KAAKH,UAAL,CAAgBG,UAAvB;AACH;;AACoB,YAAVA,UAAU,CAACC,KAAD,EAAgB;AACjC,eAAKJ,UAAL,CAAgBG,UAAhB,GAA6BC,KAA7B;AACH;;AAEe,YAALC,KAAK,GAAW;AACvB,iBAAO,KAAKL,UAAL,CAAgBK,KAAvB;AACH;;AACe,YAALA,KAAK,CAACD,KAAD,EAAgB;AAC5B,eAAKJ,UAAL,CAAgBK,KAAhB,GAAwBD,KAAxB;AACH;;AAGyB,YAAfE,eAAe,GAAW;AACjC,iBAAO,KAAKN,UAAL,CAAgBM,eAAvB;AACH;;AACyB,YAAfA,eAAe,CAACF,KAAD,EAAgB;AACtC,eAAKJ,UAAL,CAAgBM,eAAhB,GAAkCF,KAAlC;AACH;;AAG0B,YAAhBG,gBAAgB,GAAW;AAClC,iBAAO,KAAKP,UAAL,CAAgBO,gBAAvB;AACH;;AAC0B,YAAhBA,gBAAgB,CAACH,KAAD,EAAgB;AACvC,eAAKJ,UAAL,CAAgBO,gBAAhB,GAAmCH,KAAnC;AACH;;AAGoB,YAAVI,UAAU,GAAoB;AACrC,cAAI,KAAKT,WAAL,KAAqB,IAArB,IAA6B,KAAKU,IAAL,CAAUC,MAA3C,EAAmD;AAC/C,iBAAKX,WAAL,GAAmB,KAAKU,IAAL,CAAUC,MAAV,CAAiBC,YAAjB;AAAA;AAAA,yCAAnB;AACH;;AACD,iBAAO,KAAKZ,WAAZ;AACH;;AAKMa,QAAAA,eAAe,GAAS;AAC3B,eAAKV,QAAL,GAAgB,IAAhB;AACH;;AACMW,QAAAA,mBAAmB,GAAS;AAC/B,eAAKX,QAAL,GAAgB,KAAhB;AACH;;AAEmB,YAATY,SAAS,GAAc;AAC9B;AACA,eAAKd,UAAL,CAAgBe,QAAhB,GAA2B,IAAIpC,IAAJ,CAAS,KAAK8B,IAAL,CAAUM,QAAV,CAAmBC,CAA5B,EAA+B,KAAKP,IAAL,CAAUM,QAAV,CAAmBE,CAAlD,CAA3B;AACA,iBAAO,KAAKjB,UAAZ;AACH;;AAEmB,YAATc,SAAS,CAACV,KAAD,EAAmB;AACnC,eAAKJ,UAAL,GAAkBI,KAAlB,CADmC,CAEnC;;AACA,eAAKK,IAAL,CAAUS,WAAV,CAAsB,KAAKlB,UAAL,CAAgBgB,CAAtC,EAAyC,KAAKhB,UAAL,CAAgBiB,CAAzD,EAA4D,CAA5D;AACH;;AAEME,QAAAA,aAAa,GAAG;AACnB,cAAIC,QAAQ,GAAG,KAAKT,YAAL,CAAkBnC,QAAlB,KAA+B,KAAK6C,YAAL,CAAkB7C,QAAlB,CAA9C;AACA,cAAI4C,QAAQ,KAAK,IAAjB,EAAuB;AAEvBA,UAAAA,QAAQ,CAACE,KAAT,GAJmB,CAMnB;;AACA,cAAMC,KAAK,GAAG,KAAKrB,QAAL,GAAgBzB,KAAK,CAAC+C,MAAtB,GAA+B,KAAKC,sBAAL,CAA4B,KAAKxB,YAAjC,EAA+C,KAAKQ,IAAL,CAAUC,MAAV,CAAkBgB,QAAlB,CAA2BC,MAA1E,CAA7C;AACAP,UAAAA,QAAQ,CAACQ,SAAT,GAAqBL,KAArB;AACAH,UAAAA,QAAQ,CAACS,WAAT,GAAuBpD,KAAK,CAACqD,KAA7B;AACAV,UAAAA,QAAQ,CAACW,SAAT,GAAqB,CAArB;AAEAX,UAAAA,QAAQ,CAACY,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsB,KAAKlC,SAA3B;AACAsB,UAAAA,QAAQ,CAACa,IAAT;AACAb,UAAAA,QAAQ,CAACc,MAAT,GAdmB,CAgBnB;;AACA,cAAI,KAAKlC,UAAL,CAAgBG,UAAhB,GAA6B,CAAjC,EAAoC;AAChCiB,YAAAA,QAAQ,CAACS,WAAT,GAAuBpD,KAAK,CAAC0D,KAA7B;AACAf,YAAAA,QAAQ,CAACW,SAAT,GAAqB,CAArB;AACA,gBAAMK,MAAM,GAAG,KAAKtC,SAAL,GAAiB,CAAjB,GAAqB,KAAKE,UAAL,CAAgBG,UAAhB,GAA6B,EAAjE;AACAiB,YAAAA,QAAQ,CAACY,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsBI,MAAtB;AACAhB,YAAAA,QAAQ,CAACc,MAAT;AACH,WAvBkB,CAyBnB;;;AACA,cAAI,KAAKlC,UAAL,CAAgBK,KAAhB,GAAwB,CAA5B,EAA+B;AAC3B,iBAAKgC,oBAAL,CAA0BjB,QAA1B;AACH;AACJ;;AAEMkB,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB;AACA,cAAMC,eAAe,GAAG,IAAI7D,IAAJ,CAAS,KAAK8B,IAAL,CAAUM,QAAV,CAAmBC,CAA5B,EAA+B,KAAKP,IAAL,CAAUM,QAAV,CAAmBE,CAAlD,CAAxB;AACA,cAAMwB,YAAY,GAAG,KAAKhC,IAAL,CAAUiC,eAAV,EAArB,CAHuB,CAKvB;;AACA,cAAID,YAAY,KAAK,KAAKxC,YAA1B,EAAwC;AACpC,iBAAKA,YAAL,GAAoBwC,YAApB;AACA,iBAAKhC,IAAL,CAAUkC,IAAV,cAA0BF,YAA1B;AACH;;AAED,eAAKtB,aAAL;AACH;;AAEDM,QAAAA,sBAAsB,CAACmB,KAAD,EAAgBC,KAAhB,EAA+B;AAAA;;AACjD,cAAMC,QAAQ,GAAG,0BAAKtC,UAAL,sCAAiBsC,QAAjB,KAA6B,CAA9C;AACA,cAAIC,MAAM,GAAG,2BAAKvC,UAAL,uCAAiBuC,MAAjB,KAA2BF,KAAK,GAAG,CAAhD;AACA,cAAIE,MAAM,KAAK,CAAC,CAAhB,EAAmBA,MAAM,GAAGF,KAAK,GAAG,CAAjB,CAH8B,CAKjD;;AACA,cAAID,KAAK,KAAKE,QAAd,EAAwB,OAAOrE,KAAK,CAAC0D,KAAb,CAAxB,CACA;AADA,eAEK,IAAIS,KAAK,KAAKG,MAAd,EAAsB,OAAOtE,KAAK,CAACuE,GAAb,CAAtB,CACL;AADK,eAEA,OAAOvE,KAAK,CAACwE,KAAb;AACR;AAED;AACJ;AACA;;;AACYZ,QAAAA,oBAAoB,CAACjB,QAAD,EAAqB;AAC7C,cAAM8B,WAAW,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKpD,UAAL,CAAgBK,KAAhB,GAAwB,EAAjC,EAAqC,GAArC,CAApB;AACA,cAAMgD,UAAU,GAAG,KAAKC,mBAAL,EAAnB,CAF6C,CAI7C;;AACAlC,UAAAA,QAAQ,CAACS,WAAT,GAAuB,KAAK0B,8BAAL,EAAvB;AACAnC,UAAAA,QAAQ,CAACW,SAAT,GAAqB,CAArB,CAN6C,CAQ7C;;AACA,cAAMyB,IAAI,GAAGL,IAAI,CAACM,GAAL,CAASJ,UAAT,IAAuBH,WAApC;AACA,cAAMQ,IAAI,GAAGP,IAAI,CAACQ,GAAL,CAASN,UAAT,IAAuBH,WAApC,CAV6C,CAY7C;;AACA9B,UAAAA,QAAQ,CAACwC,MAAT,CAAgB,CAAhB,EAAmB,CAAnB;AACAxC,UAAAA,QAAQ,CAACyC,MAAT,CAAgBL,IAAhB,EAAsBE,IAAtB,EAd6C,CAgB7C;;AACA,cAAMI,eAAe,GAAG,CAAxB;AACA,cAAMC,cAAc,GAAGZ,IAAI,CAACa,EAAL,GAAU,CAAjC,CAlB6C,CAkBT;;AAEpC,cAAMC,KAAK,GAAGT,IAAI,GAAGL,IAAI,CAACM,GAAL,CAASJ,UAAU,GAAGU,cAAtB,IAAwCD,eAA7D;AACA,cAAMI,KAAK,GAAGR,IAAI,GAAGP,IAAI,CAACQ,GAAL,CAASN,UAAU,GAAGU,cAAtB,IAAwCD,eAA7D;AACA,cAAMK,MAAM,GAAGX,IAAI,GAAGL,IAAI,CAACM,GAAL,CAASJ,UAAU,GAAGU,cAAtB,IAAwCD,eAA9D;AACA,cAAMM,MAAM,GAAGV,IAAI,GAAGP,IAAI,CAACQ,GAAL,CAASN,UAAU,GAAGU,cAAtB,IAAwCD,eAA9D;AAEA1C,UAAAA,QAAQ,CAACwC,MAAT,CAAgBK,KAAhB,EAAuBC,KAAvB;AACA9C,UAAAA,QAAQ,CAACyC,MAAT,CAAgBL,IAAhB,EAAsBE,IAAtB;AACAtC,UAAAA,QAAQ,CAACyC,MAAT,CAAgBM,MAAhB,EAAwBC,MAAxB;AAEAhD,UAAAA,QAAQ,CAACc,MAAT;AACH;AAED;AACJ;AACA;;;AACYoB,QAAAA,mBAAmB,GAAW;AAClC,kBAAQ,KAAKtD,UAAL,CAAgBM,eAAxB;AACI,iBAAK;AAAA;AAAA,sDAAiB+D,aAAtB;AACI,qBAAO,KAAKC,0BAAL,EAAP;;AAEJ,iBAAK;AAAA;AAAA,sDAAiBC,YAAtB;AACI,qBAAO,KAAKC,wBAAL,EAAP;;AAEJ,iBAAK;AAAA;AAAA,sDAAiBC,KAAtB;AACI;AACA,qBAAQ,KAAKzE,UAAL,CAAgBO,gBAAhB,GAAmC4C,IAAI,CAACa,EAAzC,GAA+C,GAAtD;;AAEJ;AACI,qBAAO,CAAP;AAAU;AAZlB;AAcH;AAED;AACJ;AACA;;;AACYM,QAAAA,0BAA0B,GAAW;AACzC,cAAI,CAAC,KAAK7D,IAAL,CAAUC,MAAf,EAAuB,OAAO,CAAP;AAEvB,cAAMgE,QAAQ,GAAG,KAAKjE,IAAL,CAAUC,MAAV,CAAiBgB,QAAlC;AACA,cAAMiD,YAAY,GAAG,KAAKlE,IAAL,CAAUiC,eAAV,EAArB,CAJyC,CAMzC;;AACA,cAAIiC,YAAY,KAAK,CAAjB,IAAsBD,QAAQ,CAAC/C,MAAT,GAAkB,CAA5C,EAA+C;AAC3C,gBAAMiD,SAAS,GAAGF,QAAQ,CAAC,CAAD,CAAR,CAAY3D,QAA9B;AACA,gBAAM8D,YAAY,GAAG,KAAKpE,IAAL,CAAUM,QAA/B;AACA,mBAAOoC,IAAI,CAAC2B,KAAL,CAAWF,SAAS,CAAC3D,CAAV,GAAc4D,YAAY,CAAC5D,CAAtC,EAAyC2D,SAAS,CAAC5D,CAAV,GAAc6D,YAAY,CAAC7D,CAApE,CAAP;AACH,WAJD,CAKA;AALA,eAMK,IAAI2D,YAAY,KAAKD,QAAQ,CAAC/C,MAAT,GAAkB,CAAnC,IAAwC+C,QAAQ,CAAC/C,MAAT,GAAkB,CAA9D,EAAiE;AAClE,gBAAMoD,SAAS,GAAGL,QAAQ,CAACC,YAAY,GAAG,CAAhB,CAAR,CAA2B5D,QAA7C;AACA,gBAAM8D,aAAY,GAAG,KAAKpE,IAAL,CAAUM,QAA/B;AACA,mBAAOoC,IAAI,CAAC2B,KAAL,CAAWD,aAAY,CAAC5D,CAAb,GAAiB8D,SAAS,CAAC9D,CAAtC,EAAyC4D,aAAY,CAAC7D,CAAb,GAAiB+D,SAAS,CAAC/D,CAApE,CAAP;AACH,WAJI,CAKL;AALK,eAMA,IAAI2D,YAAY,GAAG,CAAf,IAAoBA,YAAY,GAAGD,QAAQ,CAAC/C,MAAT,GAAkB,CAAzD,EAA4D;AAC7D,gBAAMoD,UAAS,GAAGL,QAAQ,CAACC,YAAY,GAAG,CAAhB,CAAR,CAA2B5D,QAA7C;AACA,gBAAM6D,UAAS,GAAGF,QAAQ,CAACC,YAAY,GAAG,CAAhB,CAAR,CAA2B5D,QAA7C;AACA,mBAAOoC,IAAI,CAAC2B,KAAL,CAAWF,UAAS,CAAC3D,CAAV,GAAc8D,UAAS,CAAC9D,CAAnC,EAAsC2D,UAAS,CAAC5D,CAAV,GAAc+D,UAAS,CAAC/D,CAA9D,CAAP;AACH;;AAED,iBAAO,CAAP,CAzByC,CAyB/B;AACb;AAED;AACJ;AACA;;;AACYwD,QAAAA,wBAAwB,GAAW;AACvC;AACA;AACA,cAAMQ,OAAO,GAAG,CAAhB;AACA,cAAMC,OAAO,GAAG,CAAC,GAAjB;AAEA,cAAMJ,YAAY,GAAG,KAAKpE,IAAL,CAAUM,QAA/B;AACA,iBAAOoC,IAAI,CAAC2B,KAAL,CAAWG,OAAO,GAAGJ,YAAY,CAAC5D,CAAlC,EAAqC+D,OAAO,GAAGH,YAAY,CAAC7D,CAA5D,CAAP;AACH;AAED;AACJ;AACA;;;AACYuC,QAAAA,8BAA8B,GAAU;AAC5C,kBAAQ,KAAKvD,UAAL,CAAgBM,eAAxB;AACI,iBAAK;AAAA;AAAA,sDAAiB+D,aAAtB;AACI,qBAAO5F,KAAK,CAACyG,IAAb;AAAwB;;AAE5B,iBAAK;AAAA;AAAA,sDAAiBX,YAAtB;AACI,qBAAO9F,KAAK,CAAC0G,OAAb;AAAwB;;AAE5B,iBAAK;AAAA;AAAA,sDAAiBV,KAAtB;AACI,qBAAO,IAAIhG,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,CAApB,EAAuB,GAAvB,CAAP;AAAqC;;AAEzC;AACI,qBAAOA,KAAK,CAACyG,IAAb;AAAwB;AAXhC;AAaH;;AA/O0C,O", "sourcesContent": ["import { _decorator, Component, Graphics, Color, CCInteger, Vec2, CCFloat, Enum } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { eOrientationType } from 'db://assets/bundles/common/script/game/data/WaveData';\r\nimport { PathEditor } from './PathEditor';\r\n\r\nconst { ccclass, property, executeInEditMode, disallowMultiple, menu, requireComponent } = _decorator;\r\n\r\n@ccclass('PathPointEditor')\r\n@menu(\"怪物/编辑器/路径点\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathPointEditor extends Component {\r\n    // @property({ type: CCFloat, displayName: \"点大小\" })\r\n    public pointSize: number = 20;\r\n\r\n    @property({ type: CCFloat, displayName: \"平滑程度\", range: [0, 1], step: 0.1, slide: true, tooltip: \"0=尖锐转角,1=最大平滑\" })\r\n    public get smoothness(): number {\r\n        return this._pathPoint.smoothness;\r\n    }\r\n    public set smoothness(value: number) {\r\n        this._pathPoint.smoothness = value;\r\n    }\r\n    @property({ type: CCInteger, displayName: \"速度\", tooltip: \"飞机在此点的速度\" })\r\n    public get speed(): number {\r\n        return this._pathPoint.speed;\r\n    }\r\n    public set speed(value: number) {\r\n        this._pathPoint.speed = value;\r\n    }\r\n\r\n    @property({ type: Enum(eOrientationType), displayName: \"朝向类型\", tooltip: \"飞机在此点的朝向\" })\r\n    public get orientationType(): number {\r\n        return this._pathPoint.orientationType;\r\n    }\r\n    public set orientationType(value: number) {\r\n        this._pathPoint.orientationType = value;\r\n    }\r\n\r\n    @property({ type: CCInteger, displayName: \"朝向参数\", tooltip: \"固定朝向时：角度值(0-360度)；其他类型暂未使用\" })\r\n    public get orientationParam(): number {\r\n        return this._pathPoint.orientationParam;\r\n    }\r\n    public set orientationParam(value: number) {\r\n        this._pathPoint.orientationParam = value;\r\n    }\r\n\r\n    private _pathEditor: PathEditor|null = null;\r\n    public get pathEditor(): PathEditor|null {\r\n        if (this._pathEditor === null && this.node.parent) {\r\n            this._pathEditor = this.node.parent.getComponent(PathEditor);\r\n        }\r\n        return this._pathEditor;\r\n    }\r\n\r\n    private _pathPoint: PathPoint = new PathPoint();\r\n    private _cachedIndex: number = -1;\r\n    private selected: boolean = false;\r\n    public onFocusInEditor(): void {\r\n        this.selected = true;\r\n    }\r\n    public onLostFocusInEditor(): void {\r\n        this.selected = false;\r\n    }\r\n\r\n    public get pathPoint(): PathPoint {\r\n        // 同步节点位置到路径点数据\r\n        this._pathPoint.position = new Vec2(this.node.position.x, this.node.position.y);\r\n        return this._pathPoint;\r\n    }\r\n\r\n    public set pathPoint(value: PathPoint) {\r\n        this._pathPoint = value;\r\n        // 同步路径点数据到节点位置\r\n        this.node.setPosition(this._pathPoint.x, this._pathPoint.y, 0);\r\n    }\r\n    \r\n    public updateDisplay() {\r\n        let graphics = this.getComponent(Graphics) || this.addComponent(Graphics);\r\n        if (graphics === null) return;\r\n\r\n        graphics.clear();\r\n\r\n        // 绘制点\r\n        const color = this.selected ? Color.YELLOW : this.getDefaultColorByIndex(this._cachedIndex, this.node.parent!.children.length);\r\n        graphics.fillColor = color;\r\n        graphics.strokeColor = Color.BLACK;\r\n        graphics.lineWidth = 5;\r\n\r\n        graphics.circle(0, 0, this.pointSize);\r\n        graphics.fill();\r\n        graphics.stroke();\r\n\r\n        // 绘制平滑程度指示器\r\n        if (this._pathPoint.smoothness > 0) {\r\n            graphics.strokeColor = Color.GREEN;\r\n            graphics.lineWidth = 5;\r\n            const radius = this.pointSize + 5 + this._pathPoint.smoothness * 10;\r\n            graphics.circle(0, 0, radius);\r\n            graphics.stroke();\r\n        }\r\n\r\n        // 绘制朝向指示器（箭头）\r\n        if (this._pathPoint.speed > 0) {\r\n            this.drawOrientationArrow(graphics);\r\n        }\r\n    }\r\n\r\n    public update(_dt: number) {\r\n        // 检查是否需要重绘\r\n        const currentPosition = new Vec2(this.node.position.x, this.node.position.y);\r\n        const siblingIndex = this.node.getSiblingIndex();\r\n\r\n        // 检查索引是否改变\r\n        if (siblingIndex !== this._cachedIndex) {\r\n            this._cachedIndex = siblingIndex;\r\n            this.node.name = `Point_${siblingIndex}`;\r\n        }\r\n\r\n        this.updateDisplay();\r\n    }\r\n\r\n    getDefaultColorByIndex(index: number, count: number) {\r\n        const startIdx = this.pathEditor?.startIdx || 0;\r\n        let endIdx = this.pathEditor?.endIdx || count - 1;\r\n        if (endIdx === -1) endIdx = count - 1;\r\n        \r\n        // 起点\r\n        if (index === startIdx) return Color.GREEN;\r\n        // 终点\r\n        else if (index === endIdx) return Color.RED;\r\n        // 中间点\r\n        else return Color.WHITE;\r\n    }\r\n\r\n    /**\r\n     * 绘制朝向箭头\r\n     */\r\n    private drawOrientationArrow(graphics: Graphics) {\r\n        const arrowLength = Math.min(this._pathPoint.speed / 10, 100);\r\n        const arrowAngle = this.calculateArrowAngle();\r\n\r\n        // 根据朝向类型设置不同颜色\r\n        graphics.strokeColor = this.getArrowColorByOrientationType();\r\n        graphics.lineWidth = 3;\r\n\r\n        // 计算箭头终点\r\n        const endX = Math.cos(arrowAngle) * arrowLength;\r\n        const endY = Math.sin(arrowAngle) * arrowLength;\r\n\r\n        // 绘制箭头主线\r\n        graphics.moveTo(0, 0);\r\n        graphics.lineTo(endX, endY);\r\n\r\n        // 绘制箭头头部\r\n        const arrowHeadLength = 8;\r\n        const arrowHeadAngle = Math.PI / 6; // 30度\r\n\r\n        const leftX = endX - Math.cos(arrowAngle - arrowHeadAngle) * arrowHeadLength;\r\n        const leftY = endY - Math.sin(arrowAngle - arrowHeadAngle) * arrowHeadLength;\r\n        const rightX = endX - Math.cos(arrowAngle + arrowHeadAngle) * arrowHeadLength;\r\n        const rightY = endY - Math.sin(arrowAngle + arrowHeadAngle) * arrowHeadLength;\r\n\r\n        graphics.moveTo(leftX, leftY);\r\n        graphics.lineTo(endX, endY);\r\n        graphics.lineTo(rightX, rightY);\r\n\r\n        graphics.stroke();\r\n    }\r\n\r\n    /**\r\n     * 根据朝向类型计算箭头角度\r\n     */\r\n    private calculateArrowAngle(): number {\r\n        switch (this._pathPoint.orientationType) {\r\n            case eOrientationType.FacingMoveDir:\r\n                return this.calculateMovementDirection();\r\n\r\n            case eOrientationType.FacingPlayer:\r\n                return this.calculatePlayerDirection();\r\n\r\n            case eOrientationType.Fixed:\r\n                // orientationParam作为固定角度（度）\r\n                return (this._pathPoint.orientationParam * Math.PI) / 180;\r\n\r\n            default:\r\n                return 0; // 默认向右\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 计算移动方向\r\n     */\r\n    private calculateMovementDirection(): number {\r\n        if (!this.node.parent) return 0;\r\n\r\n        const siblings = this.node.parent.children;\r\n        const currentIndex = this.node.getSiblingIndex();\r\n\r\n        // 如果是第一个点，使用到下一个点的方向\r\n        if (currentIndex === 0 && siblings.length > 1) {\r\n            const nextPoint = siblings[1].position;\r\n            const currentPoint = this.node.position;\r\n            return Math.atan2(nextPoint.y - currentPoint.y, nextPoint.x - currentPoint.x);\r\n        }\r\n        // 如果是最后一个点，使用从上一个点的方向\r\n        else if (currentIndex === siblings.length - 1 && siblings.length > 1) {\r\n            const prevPoint = siblings[currentIndex - 1].position;\r\n            const currentPoint = this.node.position;\r\n            return Math.atan2(currentPoint.y - prevPoint.y, currentPoint.x - prevPoint.x);\r\n        }\r\n        // 中间点，使用前后两点的平均方向\r\n        else if (currentIndex > 0 && currentIndex < siblings.length - 1) {\r\n            const prevPoint = siblings[currentIndex - 1].position;\r\n            const nextPoint = siblings[currentIndex + 1].position;\r\n            return Math.atan2(nextPoint.y - prevPoint.y, nextPoint.x - prevPoint.x);\r\n        }\r\n\r\n        return 0; // 默认向右\r\n    }\r\n\r\n    /**\r\n     * 计算朝向玩家的方向\r\n     */\r\n    private calculatePlayerDirection(): number {\r\n        // 假设玩家在屏幕底部中央 (0, -400)\r\n        // 在实际游戏中，这应该从游戏状态获取玩家位置\r\n        const playerX = 0;\r\n        const playerY = -400;\r\n\r\n        const currentPoint = this.node.position;\r\n        return Math.atan2(playerY - currentPoint.y, playerX - currentPoint.x);\r\n    }\r\n\r\n    /**\r\n     * 根据朝向类型获取箭头颜色\r\n     */\r\n    private getArrowColorByOrientationType(): Color {\r\n        switch (this._pathPoint.orientationType) {\r\n            case eOrientationType.FacingMoveDir:\r\n                return Color.BLUE;      // 蓝色：跟随移动方向\r\n\r\n            case eOrientationType.FacingPlayer:\r\n                return Color.MAGENTA;   // 紫色：朝向玩家\r\n\r\n            case eOrientationType.Fixed:\r\n                return new Color(255, 165, 0, 255);  // 橙色：固定朝向\r\n\r\n            default:\r\n                return Color.BLUE;      // 默认蓝色\r\n        }\r\n    }\r\n}"]}