{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathPointEditor.ts"], "names": ["_decorator", "Component", "Graphics", "Color", "CCInteger", "Vec2", "CCFloat", "Enum", "EDITOR", "PathPoint", "eOrientationType", "ccclass", "property", "executeInEditMode", "disallowMultiple", "menu", "requireComponent", "PathPointEditor", "type", "displayName", "range", "step", "slide", "tooltip", "_graphics", "pointSize", "_pathPoint", "_cachedIndex", "selected", "graphics", "node", "getComponent", "addComponent", "smoothness", "value", "speed", "orientationType", "orientationParam", "onFocusInEditor", "onLostFocusInEditor", "pathPoint", "position", "x", "y", "setPosition", "updateDisplay", "onLoad", "clear", "color", "YELLOW", "WHITE", "fillColor", "strokeColor", "BLACK", "lineWidth", "circle", "fill", "stroke", "GREEN", "radius", "BLUE", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "moveTo", "lineTo", "update", "_dt", "siblingIndex", "getSiblingIndex", "name"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,I,OAAAA,I;;AAClEC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA,IAA1D;AAAgEC,QAAAA;AAAhE,O,GAAqFhB,U;;iCAO9EiB,e,WALZN,OAAO,CAAC,iBAAD,C,UACPI,IAAI,CAAC,YAAD,C,UACJC,gBAAgB,CAACd,QAAD,C,UAChBW,iBAAiB,CAAC,IAAD,C,UACjBC,gBAAgB,CAAC,IAAD,C,UAaZF,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEZ,OAAR;AAAiBa,QAAAA,WAAW,EAAE,MAA9B;AAAsCC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAA7C;AAAqDC,QAAAA,IAAI,EAAC,GAA1D;AAA+DC,QAAAA,KAAK,EAAE,IAAtE;AAA4EC,QAAAA,OAAO,EAAE;AAArF,OAAD,C,UAORX,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEd,SAAR;AAAmBe,QAAAA,WAAW,EAAE,IAAhC;AAAsCI,QAAAA,OAAO,EAAE;AAA/C,OAAD,C,UAQRX,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEX,IAAI;AAAA;AAAA,iDAAZ;AAAgCY,QAAAA,WAAW,EAAE,MAA7C;AAAqDI,QAAAA,OAAO,EAAE;AAA9D,OAAD,C,UAQRX,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEd,SAAR;AAAmBe,QAAAA,WAAW,EAAE,MAAhC;AAAwCI,QAAAA,OAAO,EAAE;AAAjD,OAAD,C,uFAxCb,MAKaN,eALb,SAKqChB,SALrC,CAK+C;AAAA;AAAA;AAAA,eACnCuB,SADmC,GACN,IADM;AAS3C;AAT2C,eAUpCC,SAVoC,GAUhB,EAVgB;AAAA,eA2CnCC,UA3CmC,GA2CX;AAAA;AAAA,uCA3CW;AAAA,eA4CnCC,YA5CmC,GA4CZ,CAAC,CA5CW;AAAA,eA6CnCC,QA7CmC,GA6Cf,KA7Ce;AAAA;;AAExB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKL,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKM,IAAL,CAAUC,YAAV,CAAuB7B,QAAvB,KAAoC,KAAK4B,IAAL,CAAUE,YAAV,CAAuB9B,QAAvB,CAArD;AACH;;AACD,iBAAO,KAAKsB,SAAZ;AACH;;AAMoB,YAAVS,UAAU,GAAW;AAC5B,iBAAO,KAAKP,UAAL,CAAgBO,UAAvB;AACH;;AACoB,YAAVA,UAAU,CAACC,KAAD,EAAgB;AACjC,eAAKR,UAAL,CAAgBO,UAAhB,GAA6BC,KAA7B;AACH;;AAEe,YAALC,KAAK,GAAW;AACvB,iBAAO,KAAKT,UAAL,CAAgBS,KAAvB;AACH;;AACe,YAALA,KAAK,CAACD,KAAD,EAAgB;AAC5B,eAAKR,UAAL,CAAgBS,KAAhB,GAAwBD,KAAxB;AACH;;AAGyB,YAAfE,eAAe,GAAW;AACjC,iBAAO,KAAKV,UAAL,CAAgBU,eAAvB;AACH;;AACyB,YAAfA,eAAe,CAACF,KAAD,EAAgB;AACtC,eAAKR,UAAL,CAAgBU,eAAhB,GAAkCF,KAAlC;AACH;;AAG0B,YAAhBG,gBAAgB,GAAW;AAClC,iBAAO,KAAKX,UAAL,CAAgBW,gBAAvB;AACH;;AAC0B,YAAhBA,gBAAgB,CAACH,KAAD,EAAgB;AACvC,eAAKR,UAAL,CAAgBW,gBAAhB,GAAmCH,KAAnC;AACH;;AAKMI,QAAAA,eAAe,GAAS;AAC3B,eAAKV,QAAL,GAAgB,IAAhB;AACH;;AACMW,QAAAA,mBAAmB,GAAS;AAC/B,eAAKX,QAAL,GAAgB,KAAhB;AACH;;AAEmB,YAATY,SAAS,GAAc;AAC9B;AACA,eAAKd,UAAL,CAAgBe,QAAhB,GAA2B,IAAIpC,IAAJ,CAAS,KAAKyB,IAAL,CAAUW,QAAV,CAAmBC,CAA5B,EAA+B,KAAKZ,IAAL,CAAUW,QAAV,CAAmBE,CAAlD,CAA3B;AACA,iBAAO,KAAKjB,UAAZ;AACH;;AAEmB,YAATc,SAAS,CAACN,KAAD,EAAmB;AACnC,eAAKR,UAAL,GAAkBQ,KAAlB,CADmC,CAEnC;;AACA,eAAKJ,IAAL,CAAUc,WAAV,CAAsB,KAAKlB,UAAL,CAAgBe,QAAhB,CAAyBC,CAA/C,EAAkD,KAAKhB,UAAL,CAAgBe,QAAhB,CAAyBE,CAA3E,EAA8E,CAA9E;AACA,eAAKE,aAAL;AACH;;AAESC,QAAAA,MAAM,GAAG;AACf,eAAKD,aAAL;AACH;;AAEMA,QAAAA,aAAa,GAAG;AACnB,cAAMhB,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACkB,KAAT,GAFmB,CAInB;;AACA,cAAMC,KAAK,GAAG,KAAKpB,QAAL,GAAgBzB,KAAK,CAAC8C,MAAtB,GAA+B9C,KAAK,CAAC+C,KAAnD;AACArB,UAAAA,QAAQ,CAACsB,SAAT,GAAqBH,KAArB;AACAnB,UAAAA,QAAQ,CAACuB,WAAT,GAAuBjD,KAAK,CAACkD,KAA7B;AACAxB,UAAAA,QAAQ,CAACyB,SAAT,GAAqB,CAArB;AAEAzB,UAAAA,QAAQ,CAAC0B,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsB,KAAK9B,SAA3B;AACAI,UAAAA,QAAQ,CAAC2B,IAAT;AACA3B,UAAAA,QAAQ,CAAC4B,MAAT,GAZmB,CAcnB;;AACA,cAAI,KAAK/B,UAAL,CAAgBO,UAAhB,GAA6B,CAAjC,EAAoC;AAChCJ,YAAAA,QAAQ,CAACuB,WAAT,GAAuBjD,KAAK,CAACuD,KAA7B;AACA7B,YAAAA,QAAQ,CAACyB,SAAT,GAAqB,CAArB;AACA,gBAAMK,MAAM,GAAG,KAAKlC,SAAL,GAAiB,CAAjB,GAAqB,KAAKC,UAAL,CAAgBO,UAAhB,GAA6B,EAAjE;AACAJ,YAAAA,QAAQ,CAAC0B,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsBI,MAAtB;AACA9B,YAAAA,QAAQ,CAAC4B,MAAT;AACH,WArBkB,CAuBnB;;;AACA,cAAI,KAAK/B,UAAL,CAAgBS,KAAhB,GAAwB,CAA5B,EAA+B;AAC3BN,YAAAA,QAAQ,CAACuB,WAAT,GAAuBjD,KAAK,CAACyD,IAA7B;AACA/B,YAAAA,QAAQ,CAACyB,SAAT,GAAqB,CAArB;AACA,gBAAMO,WAAW,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKrC,UAAL,CAAgBS,KAAhB,GAAwB,EAAjC,EAAqC,GAArC,CAApB;AAEAN,YAAAA,QAAQ,CAACmC,MAAT,CAAgB,CAAhB,EAAmB,CAAnB;AACAnC,YAAAA,QAAQ,CAACoC,MAAT,CAAgBJ,WAAhB,EAA6B,CAA7B;AACAhC,YAAAA,QAAQ,CAACmC,MAAT,CAAgBH,WAAW,GAAG,CAA9B,EAAiC,CAAC,CAAlC;AACAhC,YAAAA,QAAQ,CAACoC,MAAT,CAAgBJ,WAAhB,EAA6B,CAA7B;AACAhC,YAAAA,QAAQ,CAACoC,MAAT,CAAgBJ,WAAW,GAAG,CAA9B,EAAiC,CAAjC;AACAhC,YAAAA,QAAQ,CAAC4B,MAAT;AACH;AACJ;;AAEMS,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB,cAAI3D,MAAJ,EAAY;AACR,iBAAKqC,aAAL;AAEA,gBAAMuB,YAAY,GAAG,KAAKtC,IAAL,CAAUuC,eAAV,EAArB;;AACA,gBAAID,YAAY,KAAK,KAAKzC,YAA1B,EAAwC;AACpC,mBAAKA,YAAL,GAAoByC,YAApB;AACA,mBAAKtC,IAAL,CAAUwC,IAAV,cAA0BF,YAA1B;AACH;AACJ;AACJ;;AAtH0C,O", "sourcesContent": ["import { _decorator, Component, Graphics, Color, CCInteger, Vec2, CCFloat, Enum } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { eOrientationType } from 'db://assets/bundles/common/script/game/data/WaveData';\r\n\r\nconst { ccclass, property, executeInEditMode, disallowMultiple, menu, requireComponent } = _decorator;\r\n\r\n@ccclass('PathPointEditor')\r\n@menu(\"怪物/编辑器/路径点\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathPointEditor extends Component {\r\n    private _graphics: Graphics | null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n        return this._graphics;\r\n    }\r\n\r\n    // @property({ type: CCFloat, displayName: \"点大小\" })\r\n    public pointSize: number = 20;\r\n\r\n    @property({ type: CCFloat, displayName: \"平滑程度\", range: [0, 1], step:0.1, slide: true, tooltip: \"0=尖锐转角,1=最大平滑\" })\r\n    public get smoothness(): number {\r\n        return this._pathPoint.smoothness;\r\n    }\r\n    public set smoothness(value: number) {\r\n        this._pathPoint.smoothness = value;\r\n    }\r\n    @property({ type: CCInteger, displayName: \"速度\", tooltip: \"飞机在此点的速度\" })\r\n    public get speed(): number {\r\n        return this._pathPoint.speed;\r\n    }\r\n    public set speed(value: number) {\r\n        this._pathPoint.speed = value;\r\n    }\r\n\r\n    @property({ type: Enum(eOrientationType), displayName: \"朝向类型\", tooltip: \"飞机在此点的朝向\" })\r\n    public get orientationType(): number {\r\n        return this._pathPoint.orientationType;\r\n    }\r\n    public set orientationType(value: number) {\r\n        this._pathPoint.orientationType = value;\r\n    }\r\n\r\n    @property({ type: CCInteger, displayName: \"朝向参数\", tooltip: \"根据朝向类型不同而不同\" })\r\n    public get orientationParam(): number {\r\n        return this._pathPoint.orientationParam;\r\n    }\r\n    public set orientationParam(value: number) {\r\n        this._pathPoint.orientationParam = value;\r\n    }\r\n\r\n    private _pathPoint: PathPoint = new PathPoint();\r\n    private _cachedIndex: number = -1;    \r\n    private selected: boolean = false;\r\n    public onFocusInEditor(): void {\r\n        this.selected = true;\r\n    }\r\n    public onLostFocusInEditor(): void {\r\n        this.selected = false;\r\n    }\r\n\r\n    public get pathPoint(): PathPoint {\r\n        // 同步节点位置到路径点数据\r\n        this._pathPoint.position = new Vec2(this.node.position.x, this.node.position.y);\r\n        return this._pathPoint;\r\n    }\r\n\r\n    public set pathPoint(value: PathPoint) {\r\n        this._pathPoint = value;\r\n        // 同步路径点数据到节点位置\r\n        this.node.setPosition(this._pathPoint.position.x, this._pathPoint.position.y, 0);\r\n        this.updateDisplay();\r\n    }\r\n\r\n    protected onLoad() {\r\n        this.updateDisplay();\r\n    }\r\n\r\n    public updateDisplay() {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n\r\n        // 绘制点\r\n        const color = this.selected ? Color.YELLOW : Color.WHITE;\r\n        graphics.fillColor = color;\r\n        graphics.strokeColor = Color.BLACK;\r\n        graphics.lineWidth = 5;\r\n\r\n        graphics.circle(0, 0, this.pointSize);\r\n        graphics.fill();\r\n        graphics.stroke();\r\n\r\n        // 绘制平滑程度指示器\r\n        if (this._pathPoint.smoothness > 0) {\r\n            graphics.strokeColor = Color.GREEN;\r\n            graphics.lineWidth = 5;\r\n            const radius = this.pointSize + 5 + this._pathPoint.smoothness * 10;\r\n            graphics.circle(0, 0, radius);\r\n            graphics.stroke();\r\n        }\r\n\r\n        // 绘制速度指示器（箭头）\r\n        if (this._pathPoint.speed > 0) {\r\n            graphics.strokeColor = Color.BLUE;\r\n            graphics.lineWidth = 3;\r\n            const arrowLength = Math.min(this._pathPoint.speed / 10, 100);\r\n\r\n            graphics.moveTo(0, 0);\r\n            graphics.lineTo(arrowLength, 0);\r\n            graphics.moveTo(arrowLength - 5, -5);\r\n            graphics.lineTo(arrowLength, 0);\r\n            graphics.lineTo(arrowLength - 5, 5);\r\n            graphics.stroke();\r\n        }\r\n    }\r\n\r\n    public update(_dt: number) {\r\n        if (EDITOR) {\r\n            this.updateDisplay();\r\n\r\n            const siblingIndex = this.node.getSiblingIndex();\r\n            if (siblingIndex !== this._cachedIndex) {\r\n                this._cachedIndex = siblingIndex;\r\n                this.node.name = `Point_${siblingIndex}`;\r\n            }\r\n        }\r\n    }\r\n}"]}