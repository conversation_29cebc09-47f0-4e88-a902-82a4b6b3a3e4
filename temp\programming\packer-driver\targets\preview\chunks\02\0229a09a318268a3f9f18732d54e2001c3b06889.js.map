{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathPointEditor.ts"], "names": ["_decorator", "Component", "Graphics", "Color", "Vec3", "CCFloat", "input", "Input", "Camera", "find", "EDITOR", "PathPoint", "ccclass", "property", "executeInEditMode", "disallowMultiple", "menu", "requireComponent", "PathPointEditor", "type", "displayName", "_graphics", "_isDragging", "_dragOffset", "graphics", "node", "getComponent", "addComponent", "pathPoint", "_pathPoint", "position", "value", "updateDisplay", "onLoad", "setupEditorEvents", "onDestroy", "removeEditorEvents", "on", "EventType", "MOUSE_DOWN", "onMouseDown", "MOUSE_MOVE", "onMouseMove", "MOUSE_UP", "onMouseUp", "off", "event", "screenPos", "getLocation", "worldPos", "screenToWorld", "x", "y", "distance", "worldPosition", "pointSize", "subtract", "selected", "pathEditor", "parent", "selectPoint", "newPos", "add", "inverseTransformPoint", "updateCurve", "_event", "cameraNode", "camera", "clear", "color", "YELLOW", "WHITE", "fillColor", "strokeColor", "BLACK", "lineWidth", "circle", "fill", "stroke", "smoothness", "GREEN", "radius", "speed", "BLUE", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "moveTo", "lineTo", "update", "_dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAmBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;;AACzFC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA,IAA1D;AAAgEC,QAAAA;AAAhE,O,GAAqFjB,U;;iCAO9EkB,e,WALZN,OAAO,CAAC,iBAAD,C,UACPI,IAAI,CAAC,YAAD,C,UACJC,gBAAgB,CAACf,QAAD,C,UAChBY,iBAAiB,CAAC,IAAD,C,UACjBC,gBAAgB,CAAC,IAAD,C,UAUZF,QAAQ,CAAC;AAAEM,QAAAA,IAAI;AAAA;AAAA,kCAAN;AAAmBC,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAGRP,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEd,OAAR;AAAiBe,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRP,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,uFApBb,MAKaF,eALb,SAKqCjB,SALrC,CAK+C;AAAA;AAAA;AAAA,eACnCoB,SADmC,GACN,IADM;;AAAA;;AAAA;;AAAA;;AAAA,eAkBnCC,WAlBmC,GAkBZ,KAlBY;AAAA,eAmBnCC,WAnBmC,GAmBf,IAAInB,IAAJ,EAnBe;AAAA;;AAExB,YAARoB,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKH,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKI,IAAL,CAAUC,YAAV,CAAuBxB,QAAvB,KAAoC,KAAKuB,IAAL,CAAUE,YAAV,CAAuBzB,QAAvB,CAArD;AACH;;AACD,iBAAO,KAAKmB,SAAZ;AACH;;AAcmB,YAATO,SAAS,GAAc;AAC9B;AACA,eAAKC,UAAL,CAAgBC,QAAhB,GAA2B,KAAKL,IAAL,CAAUK,QAArC;AACA,iBAAO,KAAKD,UAAZ;AACH;;AAEmB,YAATD,SAAS,CAACG,KAAD,EAAmB;AACnC,eAAKF,UAAL,GAAkBE,KAAlB,CADmC,CAEnC;;AACA,eAAKN,IAAL,CAAUK,QAAV,GAAqB,KAAKD,UAAL,CAAgBC,QAArC;AACA,eAAKE,aAAL;AACH;;AAESC,QAAAA,MAAM,GAAG;AACf,cAAIvB,MAAJ,EAAY;AACR,iBAAKwB,iBAAL;AACH;;AACD,eAAKF,aAAL;AACH;;AAESG,QAAAA,SAAS,GAAG;AAClB,cAAIzB,MAAJ,EAAY;AACR,iBAAK0B,kBAAL;AACH;AACJ;;AAEOF,QAAAA,iBAAiB,GAAG;AACxB,cAAIxB,MAAJ,EAAY;AACRJ,YAAAA,KAAK,CAAC+B,EAAN,CAAS9B,KAAK,CAAC+B,SAAN,CAAgBC,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACAlC,YAAAA,KAAK,CAAC+B,EAAN,CAAS9B,KAAK,CAAC+B,SAAN,CAAgBG,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACApC,YAAAA,KAAK,CAAC+B,EAAN,CAAS9B,KAAK,CAAC+B,SAAN,CAAgBK,QAAzB,EAAmC,KAAKC,SAAxC,EAAmD,IAAnD;AACH;AACJ;;AAEOR,QAAAA,kBAAkB,GAAG;AACzB,cAAI1B,MAAJ,EAAY;AACRJ,YAAAA,KAAK,CAACuC,GAAN,CAAUtC,KAAK,CAAC+B,SAAN,CAAgBC,UAA1B,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACAlC,YAAAA,KAAK,CAACuC,GAAN,CAAUtC,KAAK,CAAC+B,SAAN,CAAgBG,UAA1B,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACApC,YAAAA,KAAK,CAACuC,GAAN,CAAUtC,KAAK,CAAC+B,SAAN,CAAgBK,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACH;AACJ;;AAEOJ,QAAAA,WAAW,CAACM,KAAD,EAAoB;AACnC,cAAI,CAACpC,MAAL,EAAa;AAEb,cAAMqC,SAAS,GAAGD,KAAK,CAACE,WAAN,EAAlB;AACA,cAAMC,QAAQ,GAAG,KAAKC,aAAL,CAAmB,IAAI9C,IAAJ,CAAS2C,SAAS,CAACI,CAAnB,EAAsBJ,SAAS,CAACK,CAAhC,EAAmC,CAAnC,CAAnB,CAAjB;AACA,cAAMC,QAAQ,GAAGjD,IAAI,CAACiD,QAAL,CAAcJ,QAAd,EAAwB,KAAKxB,IAAL,CAAU6B,aAAlC,CAAjB;;AAEA,cAAID,QAAQ,IAAI,KAAKE,SAArB,EAAgC;AAAA;;AAC5B,iBAAKjC,WAAL,GAAmB,IAAnB;AACA,iBAAKC,WAAL,GAAmBnB,IAAI,CAACoD,QAAL,CAAc,IAAIpD,IAAJ,EAAd,EAA0B,KAAKqB,IAAL,CAAU6B,aAApC,EAAmDL,QAAnD,CAAnB;AACA,iBAAKQ,QAAL,GAAgB,IAAhB;AACA,iBAAKzB,aAAL,GAJ4B,CAM5B;;AACA,gBAAM0B,UAAU,wBAAG,KAAKjC,IAAL,CAAUkC,MAAb,qBAAG,kBAAkBjC,YAAlB,CAA+B,YAA/B,CAAnB;;AACA,gBAAIgC,UAAJ,EAAgB;AACZ;AACAA,cAAAA,UAAU,CAACE,WAAX,CAAuB,IAAvB;AACH;AACJ;AACJ;;AAEOlB,QAAAA,WAAW,CAACI,KAAD,EAAoB;AAAA;;AACnC,cAAI,CAACpC,MAAD,IAAW,CAAC,KAAKY,WAArB,EAAkC;AAElC,cAAMyB,SAAS,GAAGD,KAAK,CAACE,WAAN,EAAlB;AACA,cAAMC,QAAQ,GAAG,KAAKC,aAAL,CAAmB,IAAI9C,IAAJ,CAAS2C,SAAS,CAACI,CAAnB,EAAsBJ,SAAS,CAACK,CAAhC,EAAmC,CAAnC,CAAnB,CAAjB;AACA,cAAMS,MAAM,GAAGzD,IAAI,CAAC0D,GAAL,CAAS,IAAI1D,IAAJ,EAAT,EAAqB6C,QAArB,EAA+B,KAAK1B,WAApC,CAAf,CALmC,CAOnC;;AACA,cAAI,KAAKE,IAAL,CAAUkC,MAAd,EAAsB;AAClB,iBAAKlC,IAAL,CAAUkC,MAAV,CAAiBI,qBAAjB,CAAuCF,MAAvC,EAA+CA,MAA/C;AACH;;AAED,eAAKpC,IAAL,CAAUK,QAAV,GAAqB+B,MAArB;AACA,eAAKhC,UAAL,CAAgBC,QAAhB,GAA2B+B,MAA3B,CAbmC,CAenC;;AACA,cAAMH,UAAU,yBAAG,KAAKjC,IAAL,CAAUkC,MAAb,qBAAG,mBAAkBjC,YAAlB,CAA+B,YAA/B,CAAnB;;AACA,cAAIgC,UAAJ,EAAgB;AACZ;AACAA,YAAAA,UAAU,CAACM,WAAX;AACH;AACJ;;AAEOpB,QAAAA,SAAS,CAACqB,MAAD,EAAqB;AAClC,cAAI,CAACvD,MAAL,EAAa;AACb,eAAKY,WAAL,GAAmB,KAAnB;AACH;;AAEO4B,QAAAA,aAAa,CAACH,SAAD,EAAwB;AACzC,cAAMmB,UAAU,GAAGzD,IAAI,CAAC,aAAD,CAAvB;AACA,cAAM0D,MAAM,GAAGD,UAAH,oBAAGA,UAAU,CAAExC,YAAZ,CAAyBlB,MAAzB,CAAf;AACA,cAAI,CAAC2D,MAAL,EAAa,OAAO,IAAI/D,IAAJ,EAAP;AAEb,cAAM6C,QAAQ,GAAG,IAAI7C,IAAJ,EAAjB;AACA+D,UAAAA,MAAM,CAACjB,aAAP,CAAqBH,SAArB,EAAgCE,QAAhC;AACA,iBAAOA,QAAP;AACH;;AAEMjB,QAAAA,aAAa,GAAG;AACnB,cAAMR,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAAC4C,KAAT,GAFmB,CAInB;;AACA,cAAMC,KAAK,GAAG,KAAKZ,QAAL,GAAgBtD,KAAK,CAACmE,MAAtB,GAA+BnE,KAAK,CAACoE,KAAnD;AACA/C,UAAAA,QAAQ,CAACgD,SAAT,GAAqBH,KAArB;AACA7C,UAAAA,QAAQ,CAACiD,WAAT,GAAuBtE,KAAK,CAACuE,KAA7B;AACAlD,UAAAA,QAAQ,CAACmD,SAAT,GAAqB,CAArB;AAEAnD,UAAAA,QAAQ,CAACoD,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsB,KAAKrB,SAA3B;AACA/B,UAAAA,QAAQ,CAACqD,IAAT;AACArD,UAAAA,QAAQ,CAACsD,MAAT,GAZmB,CAcnB;;AACA,cAAI,KAAKjD,UAAL,CAAgBkD,UAAhB,GAA6B,CAAjC,EAAoC;AAChCvD,YAAAA,QAAQ,CAACiD,WAAT,GAAuBtE,KAAK,CAAC6E,KAA7B;AACAxD,YAAAA,QAAQ,CAACmD,SAAT,GAAqB,CAArB;AACA,gBAAMM,MAAM,GAAG,KAAK1B,SAAL,GAAiB,CAAjB,GAAqB,KAAK1B,UAAL,CAAgBkD,UAAhB,GAA6B,EAAjE;AACAvD,YAAAA,QAAQ,CAACoD,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsBK,MAAtB;AACAzD,YAAAA,QAAQ,CAACsD,MAAT;AACH,WArBkB,CAuBnB;;;AACA,cAAI,KAAKjD,UAAL,CAAgBqD,KAAhB,GAAwB,CAA5B,EAA+B;AAC3B1D,YAAAA,QAAQ,CAACiD,WAAT,GAAuBtE,KAAK,CAACgF,IAA7B;AACA3D,YAAAA,QAAQ,CAACmD,SAAT,GAAqB,CAArB;AACA,gBAAMS,WAAW,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKzD,UAAL,CAAgBqD,KAAhB,GAAwB,EAAjC,EAAqC,EAArC,CAApB;AAEA1D,YAAAA,QAAQ,CAAC+D,MAAT,CAAgB,CAAhB,EAAmB,CAAnB;AACA/D,YAAAA,QAAQ,CAACgE,MAAT,CAAgBJ,WAAhB,EAA6B,CAA7B;AACA5D,YAAAA,QAAQ,CAAC+D,MAAT,CAAgBH,WAAW,GAAG,CAA9B,EAAiC,CAAC,CAAlC;AACA5D,YAAAA,QAAQ,CAACgE,MAAT,CAAgBJ,WAAhB,EAA6B,CAA7B;AACA5D,YAAAA,QAAQ,CAACgE,MAAT,CAAgBJ,WAAW,GAAG,CAA9B,EAAiC,CAAjC;AACA5D,YAAAA,QAAQ,CAACsD,MAAT;AACH;AACJ;;AAEMW,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB,cAAIhF,MAAJ,EAAY;AACR,iBAAKsB,aAAL;AACH;AACJ;;AArK0C,O;;;;;iBAUX;AAAA;AAAA,uC;;;;;;;iBAGL,E;;;;;;;iBAGA,K", "sourcesContent": ["import { _decorator, Component, Graphics, Color, Vec3, CCFloat, input, Input, EventMouse, Camera, find } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\r\n\r\nconst { ccclass, property, executeInEditMode, disallowMultiple, menu, requireComponent } = _decorator;\r\n\r\n@ccclass('PathPointEditor')\r\n@menu(\"怪物/编辑器/路径点\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathPointEditor extends Component {\r\n    private _graphics: Graphics | null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n        return this._graphics;\r\n    }\r\n\r\n    @property({ type: PathPoint, displayName: \"路径点数据\" })\r\n    private _pathPoint: PathPoint = new PathPoint();\r\n\r\n    @property({ type: CCFloat, displayName: \"点大小\" })\r\n    public pointSize: number = 20;\r\n\r\n    @property({ displayName: \"是否选中\" })\r\n    public selected: boolean = false;\r\n\r\n    private _isDragging: boolean = false;\r\n    private _dragOffset: Vec3 = new Vec3();\r\n\r\n    public get pathPoint(): PathPoint {\r\n        // 同步节点位置到路径点数据\r\n        this._pathPoint.position = this.node.position;\r\n        return this._pathPoint;\r\n    }\r\n\r\n    public set pathPoint(value: PathPoint) {\r\n        this._pathPoint = value;\r\n        // 同步路径点数据到节点位置\r\n        this.node.position = this._pathPoint.position;\r\n        this.updateDisplay();\r\n    }\r\n\r\n    protected onLoad() {\r\n        if (EDITOR) {\r\n            this.setupEditorEvents();\r\n        }\r\n        this.updateDisplay();\r\n    }\r\n\r\n    protected onDestroy() {\r\n        if (EDITOR) {\r\n            this.removeEditorEvents();\r\n        }\r\n    }\r\n\r\n    private setupEditorEvents() {\r\n        if (EDITOR) {\r\n            input.on(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);\r\n            input.on(Input.EventType.MOUSE_MOVE, this.onMouseMove, this);\r\n            input.on(Input.EventType.MOUSE_UP, this.onMouseUp, this);\r\n        }\r\n    }\r\n\r\n    private removeEditorEvents() {\r\n        if (EDITOR) {\r\n            input.off(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);\r\n            input.off(Input.EventType.MOUSE_MOVE, this.onMouseMove, this);\r\n            input.off(Input.EventType.MOUSE_UP, this.onMouseUp, this);\r\n        }\r\n    }\r\n\r\n    private onMouseDown(event: EventMouse) {\r\n        if (!EDITOR) return;\r\n\r\n        const screenPos = event.getLocation();\r\n        const worldPos = this.screenToWorld(new Vec3(screenPos.x, screenPos.y, 0));\r\n        const distance = Vec3.distance(worldPos, this.node.worldPosition);\r\n\r\n        if (distance <= this.pointSize) {\r\n            this._isDragging = true;\r\n            this._dragOffset = Vec3.subtract(new Vec3(), this.node.worldPosition, worldPos);\r\n            this.selected = true;\r\n            this.updateDisplay();\r\n\r\n            // 通知父编辑器选中了这个点\r\n            const pathEditor = this.node.parent?.getComponent('PathEditor');\r\n            if (pathEditor) {\r\n                // @ts-ignore\r\n                pathEditor.selectPoint(this);\r\n            }\r\n        }\r\n    }\r\n\r\n    private onMouseMove(event: EventMouse) {\r\n        if (!EDITOR || !this._isDragging) return;\r\n\r\n        const screenPos = event.getLocation();\r\n        const worldPos = this.screenToWorld(new Vec3(screenPos.x, screenPos.y, 0));\r\n        const newPos = Vec3.add(new Vec3(), worldPos, this._dragOffset);\r\n\r\n        // 转换为本地坐标\r\n        if (this.node.parent) {\r\n            this.node.parent.inverseTransformPoint(newPos, newPos);\r\n        }\r\n\r\n        this.node.position = newPos;\r\n        this._pathPoint.position = newPos;\r\n\r\n        // 通知父编辑器更新曲线\r\n        const pathEditor = this.node.parent?.getComponent('PathEditor');\r\n        if (pathEditor) {\r\n            // @ts-ignore\r\n            pathEditor.updateCurve();\r\n        }\r\n    }\r\n\r\n    private onMouseUp(_event: EventMouse) {\r\n        if (!EDITOR) return;\r\n        this._isDragging = false;\r\n    }\r\n\r\n    private screenToWorld(screenPos: Vec3): Vec3 {\r\n        const cameraNode = find('Main Camera');\r\n        const camera = cameraNode?.getComponent(Camera);\r\n        if (!camera) return new Vec3();\r\n\r\n        const worldPos = new Vec3();\r\n        camera.screenToWorld(screenPos, worldPos);\r\n        return worldPos;\r\n    }\r\n\r\n    public updateDisplay() {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n\r\n        // 绘制点\r\n        const color = this.selected ? Color.YELLOW : Color.WHITE;\r\n        graphics.fillColor = color;\r\n        graphics.strokeColor = Color.BLACK;\r\n        graphics.lineWidth = 2;\r\n\r\n        graphics.circle(0, 0, this.pointSize);\r\n        graphics.fill();\r\n        graphics.stroke();\r\n\r\n        // 绘制平滑程度指示器\r\n        if (this._pathPoint.smoothness > 0) {\r\n            graphics.strokeColor = Color.GREEN;\r\n            graphics.lineWidth = 1;\r\n            const radius = this.pointSize + 5 + this._pathPoint.smoothness * 10;\r\n            graphics.circle(0, 0, radius);\r\n            graphics.stroke();\r\n        }\r\n\r\n        // 绘制速度指示器（箭头）\r\n        if (this._pathPoint.speed > 0) {\r\n            graphics.strokeColor = Color.BLUE;\r\n            graphics.lineWidth = 2;\r\n            const arrowLength = Math.min(this._pathPoint.speed / 10, 50);\r\n\r\n            graphics.moveTo(0, 0);\r\n            graphics.lineTo(arrowLength, 0);\r\n            graphics.moveTo(arrowLength - 5, -3);\r\n            graphics.lineTo(arrowLength, 0);\r\n            graphics.lineTo(arrowLength - 5, 3);\r\n            graphics.stroke();\r\n        }\r\n    }\r\n\r\n    public update(_dt: number) {\r\n        if (EDITOR) {\r\n            this.updateDisplay();\r\n        }\r\n    }\r\n}"]}