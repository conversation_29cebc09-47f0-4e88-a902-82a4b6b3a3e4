System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec2, EDITOR, PathData, PathPoint, _dec, _dec2, _dec3, _class, _crd, ccclass, executeInEditMode, menu, SmoothnessTester;

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec2 = _cc.Vec2;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
      PathPoint = _unresolved_2.PathPoint;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6f991KS7bJFGag9Czp33zJQ", "SmoothnessTester", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Vec2']);

      ({
        ccclass,
        executeInEditMode,
        menu
      } = _decorator);
      /**
       * 平滑度测试器 - 验证smoothness=0时是否为直线
       */

      _export("SmoothnessTester", SmoothnessTester = (_dec = ccclass('SmoothnessTester'), _dec2 = menu("怪物/编辑器/平滑度测试器"), _dec3 = executeInEditMode(true), _dec(_class = _dec2(_class = _dec3(_class = class SmoothnessTester extends Component {
        onLoad() {
          if (EDITOR) {
            this.testSmoothness();
          }
        }

        testSmoothness() {
          console.log("=== 平滑度测试开始 ==="); // 测试1: smoothness = 0 应该产生直线

          this.testLinearBehavior(); // 测试2: smoothness = 1 应该产生平滑曲线

          this.testSmoothBehavior(); // 测试3: 混合平滑度测试

          this.testMixedSmoothness();
          console.log("=== 平滑度测试结束 ===");
        }

        testLinearBehavior() {
          console.log("测试1: smoothness = 0 (直线)");
          var points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 100), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0)]; // 设置所有点的平滑度为0

          points.forEach(p => p.smoothness = 0);
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          pathData.points = points;
          pathData.segments = 10;
          var curvePoints = pathData.generateCurvePoints(); // 验证是否为直线：检查中间点是否在两端点的连线上

          this.validateLinearSegments(curvePoints, points);
        }

        testSmoothBehavior() {
          console.log("测试2: smoothness = 1 (最平滑)");
          var points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 100), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0)]; // 设置所有点的平滑度为1

          points.forEach(p => p.smoothness = 1);
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          pathData.points = points;
          pathData.segments = 10;
          var curvePoints = pathData.generateCurvePoints();
          console.log("\u751F\u6210\u4E86 " + curvePoints.length + " \u4E2A\u66F2\u7EBF\u70B9");
          console.log("第一个点:", curvePoints[0]);
          console.log("中间点:", curvePoints[Math.floor(curvePoints.length / 2)]);
          console.log("最后一个点:", curvePoints[curvePoints.length - 1]);
        }

        testMixedSmoothness() {
          console.log("测试3: 混合平滑度");
          var points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-150, 0), // smoothness = 0.5
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-50, 100), // smoothness = 0 (直线)
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(50, -100), // smoothness = 0 (直线)
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(150, 0) // smoothness = 0.5
          ];
          points[0].smoothness = 0.5;
          points[1].smoothness = 0; // 这个点应该形成直线连接

          points[2].smoothness = 0; // 这个点应该形成直线连接

          points[3].smoothness = 0.5;
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          pathData.points = points;
          pathData.segments = 20;
          var curvePoints = pathData.generateCurvePoints();
          console.log("\u6DF7\u5408\u5E73\u6ED1\u5EA6\u6D4B\u8BD5\u751F\u6210\u4E86 " + curvePoints.length + " \u4E2A\u70B9");
        }

        validateLinearSegments(curvePoints, originalPoints) {
          var tolerance = 0.1; // 允许的误差

          var isLinear = true; // 检查每一段是否为直线

          for (var i = 0; i < originalPoints.length - 1; i++) {
            var startPoint = originalPoints[i].position;
            var endPoint = originalPoints[i + 1].position; // 找到这一段对应的曲线点

            var segmentStart = i * 10; // 假设每段10个点

            var segmentEnd = Math.min((i + 1) * 10, curvePoints.length - 1);

            for (var j = segmentStart; j <= segmentEnd; j++) {
              var curvePoint = curvePoints[j];
              var t = (j - segmentStart) / (segmentEnd - segmentStart); // 计算理论上的直线点

              var expectedLinearPoint = Vec2.lerp(new Vec2(), startPoint, endPoint, t); // 检查距离

              var distance = Vec2.distance(curvePoint, expectedLinearPoint);

              if (distance > tolerance) {
                console.warn("\u70B9 " + j + " \u504F\u79BB\u76F4\u7EBF\uFF0C\u8DDD\u79BB: " + distance.toFixed(3));
                isLinear = false;
              }
            }
          }

          if (isLinear) {
            console.log("✅ 验证通过：smoothness=0时确实产生直线");
          } else {
            console.log("❌ 验证失败：smoothness=0时没有产生直线");
          }

          return isLinear;
        }
        /**
         * 手动测试单个插值点
         */


        testSinglePoint() {
          var p0 = new Vec2(-100, 0);
          var p1 = new Vec2(0, 100);
          var p2 = new Vec2(100, 0);
          var p3 = new Vec2(200, 100);
          console.log("=== 单点插值测试 ==="); // 测试t=0.5时的不同smoothness值

          for (var smoothness = 0; smoothness <= 1; smoothness += 0.25) {
            var result = (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
              error: Error()
            }), PathData) : PathData).catmullRomPoint(0.5, p0, p1, p2, p3, smoothness);
            console.log("smoothness=" + smoothness + ": (" + result.x.toFixed(2) + ", " + result.y.toFixed(2) + ")");
          } // 验证smoothness=0时是否为线性插值


          var linearResult = Vec2.lerp(new Vec2(), p1, p2, 0.5);
          var smoothness0Result = (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData).catmullRomPoint(0.5, p0, p1, p2, p3, 0);
          var distance = Vec2.distance(linearResult, smoothness0Result);
          console.log("\u7EBF\u6027\u63D2\u503C\u7ED3\u679C: (" + linearResult.x + ", " + linearResult.y + ")");
          console.log("smoothness=0\u7ED3\u679C: (" + smoothness0Result.x + ", " + smoothness0Result.y + ")");
          console.log("\u8DDD\u79BB\u5DEE: " + distance);

          if (distance < 0.001) {
            console.log("✅ smoothness=0确实等于线性插值");
          } else {
            console.log("❌ smoothness=0不等于线性插值");
          }
        }

      }) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=91ebcc4a94536e71b9f6d11623164ccd3539b531.js.map