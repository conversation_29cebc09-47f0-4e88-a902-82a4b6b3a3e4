"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const path_1 = require("path");
// @ts-ignore
module.paths.push((0, path_1.join)(Editor.App.path, 'node_modules'));
// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
const cc_1 = require("cc");
const { _utils } = cc_1.Prefab;
function load() { }
;
function unload() { }
;
exports.methods = {
    saveLevel() {
        console.log('saveLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.save = true;
        }
    },
    playLevel() {
        console.log('playLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            var play = levelEditorUI.play;
            levelEditorUI.play = !play;
        }
    },
    levelStart() {
        console.log('levelStart in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 0;
        }
    },
    levelEnd() {
        console.log('levelEnd in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 1;
        }
    },
    instantiatePrefab(component_uuid, prefabUuid) {
        // console.log('instantiatePrefab:', component_uuid, prefabUuid);
        var _a, _b, _c;
        let targetNode = (_a = cc_1.director.getScene()) === null || _a === void 0 ? void 0 : _a.getChildByUuid(component_uuid);
        if (!targetNode) {
            targetNode = (_b = cc_1.director.getScene()) === null || _b === void 0 ? void 0 : _b.getChildByName('Canvas');
        }
        if (targetNode) {
            // console.log("Canvas node found: ", targetNode.getComponent("EmitterEditor"));
            // @ts-ignore
            Editor.Message.request('scene', 'execute-component-method', {
                uuid: (_c = targetNode.getComponent("EmitterEditor")) === null || _c === void 0 ? void 0 : _c.uuid,
                name: 'instantiatePrefab',
                args: [prefabUuid]
            });
        }
    },
    // async saveToPrefab(component_uuid:string, nodeName: string, prefabUuid: string) {
    //     // console.log('saveToPrefab:', component_uuid, nodeName, prefabUuid);
    //     const scene = director.getScene();
    //     const target = scene!.getChildByPath(`Canvas/${nodeName}`);
    //     if (!target) {
    //         console.error("node not found:", nodeName);
    //         return;
    //     }
    //     cce.Prefab.createPrefabAssetFromNode(target.uuid, prefabUuid);
    // },
    async createBulletPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // 需要判断是spriteList还是单个sprite
        // TODO: 当sourceAssetUuid是一个目录的时候，下面这里的路径可能存在bug。还没有验证。
        // TODO: 这个目录后续可能调整到bundle目录
        const targetPrefabDir = 'db://assets/resources/game/prefabs/bullet/';
        if (sourceAssetInfo.importer === 'sprite-atlas') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteList = asset;
                for (let spriteFrame of spriteList.getSpriteFrames()) {
                    const targetPrefabName = spriteFrame.name;
                    const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                    createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, spriteList);
                }
            });
        }
        else if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteFrame = asset;
                const targetPrefabName = spriteFrame.name;
                const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, null);
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    async createLevelPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // remove /x/x/*.* -> /x/x/
        let targetPrefabDir = sourceAssetInfo.path.replace('/Texture/', '/Prefab/');
        const targetPrefabPath = targetPrefabDir.substring(0, targetPrefabDir.lastIndexOf('/')) + '.prefab';
        const targetPrefabName = targetPrefabDir.substring(targetPrefabDir.lastIndexOf('/') + 1);
        if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                // console.log(`Level Prefab will be created at path ${targetPrefabPath}`);
                const spriteFrame = asset;
                if (spriteFrame) {
                    createNewLevelObject(targetPrefabName, targetPrefabPath, spriteFrame); // 这个是创建prefab，不是prefab里面的节点，所以不需要parent
                }
                else {
                    console.warn('Asset is not a sprite-frame:', sourceAssetInfo);
                }
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    // 阵型相关: addFormationPoint & saveFormationGroup
    addFormationPoint(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            formationEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    async saveFormationGroup(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            const jsonData = formationEditor.formationData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: 'db://assets/resources/game/level/wave/formation/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    // 路径相关: addPathPoint & savePath & clearPath
    addPathPoint(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.addNewPoint(0, 0, 0);
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    async savePath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            const jsonData = pathEditor.pathData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: 'db://assets/resources/game/level/wave/path/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    clearPath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.node.removeAllChildren();
            // @ts-ignore
            pathEditor.updateCurve();
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
};
function findComponentByUuid(node, uuid, className) {
    if (!node)
        return null;
    // 检查当前节点是否有指定的组件，如果有且UUID匹配则返回
    const component = node.getComponent(className);
    if (component) {
        // console.log(`${node.name} has component ${className}, uuid: ${component.uuid}, target: ${uuid}`);
        if (component.uuid === uuid) {
            return component;
        }
    }
    // 无论当前节点是否有组件，都要搜索子节点
    for (let i = 0; i < node.children.length; i++) {
        let found = findComponentByUuid(node.children[i], uuid, className);
        if (found) {
            return found;
        }
    }
    return null;
}
async function createNewBullet(prefabName, prefabPath, sourceSprite, sourceSpriteList) {
    const scene = cc_1.director.getScene();
    const target = scene.getChildByName('Canvas');
    if (!target) {
        console.error("Canvas node not found");
        return;
    }
    let node = new cc_1.Node(prefabName);
    node.parent = target;
    node.setPosition(new cc_1.Vec3(0, 0, 0));
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    node.addComponent('FBoxCollider');
    let movable = node.addComponent('Movable');
    node.addComponent('Bullet');
    node.addComponent('cc.BoxCollider2D');
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSpriteList) {
        sprite.spriteFrame = sourceSprite;
        sprite.spriteAtlas = sourceSpriteList;
    }
    if (sourceSprite) {
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Bullet prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create bullet prefab:', e);
    }
}
async function createNewLevelObject(prefabName, prefabPath, sourceSprite) {
    const scene = cc_1.director.getScene();
    let node = new cc_1.Node(prefabName);
    scene.addChild(node);
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSprite) {
        sprite.spriteFrame = sourceSprite;
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Level prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
    }
}
//# sourceMappingURL=data:application/json;base64,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