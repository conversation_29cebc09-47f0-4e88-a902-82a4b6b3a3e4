{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathTestExample.ts"], "names": ["_decorator", "Component", "Graphics", "Color", "Vec2", "EDITOR", "PathData", "PathPoint", "ccclass", "property", "executeInEditMode", "menu", "PathTestExample", "displayName", "_graphics", "_testPath", "graphics", "node", "getComponent", "addComponent", "onLoad", "createTestPath", "name", "segments", "closed", "testPoints", "smoothness", "points", "update", "_dt", "drawTest", "clear", "showTestPoints", "fillColor", "RED", "strokeColor", "BLACK", "lineWidth", "for<PERSON>ach", "point", "index", "pos", "position", "circle", "x", "y", "pointSize", "fill", "stroke", "showCurve", "WHITE", "curvePoints", "generateCurvePoints", "length", "moveTo", "i", "lineTo", "GREEN", "YELLOW", "radius", "validateCurve", "tolerance", "allPointsPassed", "definedPoint", "foundNearbyPoint", "curvePoint", "distance", "console", "warn", "log", "createDifferentTestPaths", "createStraightLineTest", "createSharpTurnTest", "createSmoothCurveTest", "p"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AACxCC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDX,U;AAEvD;AACA;AACA;;iCAIaY,e,WAHZJ,OAAO,CAAC,iBAAD,C,UACPG,IAAI,CAAC,eAAD,C,UACJD,iBAAiB,CAAC,IAAD,C,UAEbD,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,yDAVb,MAGaD,eAHb,SAGqCX,SAHrC,CAG+C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAUnCa,SAVmC,GAUN,IAVM;AAAA,eAWnCC,SAXmC,GAWb;AAAA;AAAA,qCAXa;AAAA;;AAaxB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKF,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKG,IAAL,CAAUC,YAAV,CAAuBhB,QAAvB,KAAoC,KAAKe,IAAL,CAAUE,YAAV,CAAuBjB,QAAvB,CAArD;AACH;;AACD,iBAAO,KAAKY,SAAZ;AACH;;AAESM,QAAAA,MAAM,GAAG;AACf,eAAKC,cAAL;AACH;;AAEOA,QAAAA,cAAc,GAAG;AACrB;AACA,eAAKN,SAAL,CAAeO,IAAf,GAAsB,MAAtB;AACA,eAAKP,SAAL,CAAeQ,QAAf,GAA0B,EAA1B;AACA,eAAKR,SAAL,CAAeS,MAAf,GAAwB,KAAxB,CAJqB,CAMrB;;AACA,cAAMC,UAAU,GAAG,CACf;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,CADe,EACa;AAC5B;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,GAApB,CAFe,EAEa;AAC5B;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAjB,CAHe,EAGa;AAC5B;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAC,GAApB,CAJe,EAIa;AAC5B;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CALe,CAKa;AALb,WAAnB,CAPqB,CAerB;;AACAA,UAAAA,UAAU,CAAC,CAAD,CAAV,CAAcC,UAAd,GAA2B,GAA3B;AACAD,UAAAA,UAAU,CAAC,CAAD,CAAV,CAAcC,UAAd,GAA2B,GAA3B,CAjBqB,CAiBY;;AACjCD,UAAAA,UAAU,CAAC,CAAD,CAAV,CAAcC,UAAd,GAA2B,GAA3B,CAlBqB,CAkBY;;AACjCD,UAAAA,UAAU,CAAC,CAAD,CAAV,CAAcC,UAAd,GAA2B,GAA3B,CAnBqB,CAmBY;;AACjCD,UAAAA,UAAU,CAAC,CAAD,CAAV,CAAcC,UAAd,GAA2B,GAA3B;AAEA,eAAKX,SAAL,CAAeY,MAAf,GAAwBF,UAAxB;AACH;;AAEMG,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB,cAAIxB,MAAJ,EAAY;AACR,iBAAKyB,QAAL;AACH;AACJ;;AAEOA,QAAAA,QAAQ,GAAG;AACf,cAAMd,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACe,KAAT,GAFe,CAIf;;AACA,cAAI,KAAKC,cAAT,EAAyB;AACrBhB,YAAAA,QAAQ,CAACiB,SAAT,GAAqB9B,KAAK,CAAC+B,GAA3B;AACAlB,YAAAA,QAAQ,CAACmB,WAAT,GAAuBhC,KAAK,CAACiC,KAA7B;AACApB,YAAAA,QAAQ,CAACqB,SAAT,GAAqB,CAArB;;AAEA,iBAAKtB,SAAL,CAAeY,MAAf,CAAsBW,OAAtB,CAA8B,CAACC,KAAD,EAAQC,KAAR,KAAkB;AAC5C,kBAAMC,GAAG,GAAGF,KAAK,CAACG,QAAlB;AACA1B,cAAAA,QAAQ,CAAC2B,MAAT,CAAgBF,GAAG,CAACG,CAApB,EAAuBH,GAAG,CAACI,CAA3B,EAA8B,KAAKC,SAAnC;AACA9B,cAAAA,QAAQ,CAAC+B,IAAT;AACA/B,cAAAA,QAAQ,CAACgC,MAAT,GAJ4C,CAM5C;AACA;AACH,aARD;AASH,WAnBc,CAqBf;;;AACA,cAAI,KAAKC,SAAT,EAAoB;AAChBjC,YAAAA,QAAQ,CAACmB,WAAT,GAAuBhC,KAAK,CAAC+C,KAA7B;AACAlC,YAAAA,QAAQ,CAACqB,SAAT,GAAqB,CAArB;;AAEA,gBAAMc,WAAW,GAAG,KAAKpC,SAAL,CAAeqC,mBAAf,EAApB;;AACA,gBAAID,WAAW,CAACE,MAAZ,GAAqB,CAAzB,EAA4B;AACxBrC,cAAAA,QAAQ,CAACsC,MAAT,CAAgBH,WAAW,CAAC,CAAD,CAAX,CAAeP,CAA/B,EAAkCO,WAAW,CAAC,CAAD,CAAX,CAAeN,CAAjD;;AACA,mBAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,WAAW,CAACE,MAAhC,EAAwCE,CAAC,EAAzC,EAA6C;AACzCvC,gBAAAA,QAAQ,CAACwC,MAAT,CAAgBL,WAAW,CAACI,CAAD,CAAX,CAAeX,CAA/B,EAAkCO,WAAW,CAACI,CAAD,CAAX,CAAeV,CAAjD;AACH;;AACD7B,cAAAA,QAAQ,CAACgC,MAAT;AACH,aAXe,CAahB;;;AACAhC,YAAAA,QAAQ,CAACiB,SAAT,GAAqB9B,KAAK,CAACsD,KAA3B;;AACA,iBAAK1C,SAAL,CAAeY,MAAf,CAAsBW,OAAtB,CAA8BC,KAAK,IAAI;AACnC,kBAAME,GAAG,GAAGF,KAAK,CAACG,QAAlB;AACA1B,cAAAA,QAAQ,CAAC2B,MAAT,CAAgBF,GAAG,CAACG,CAApB,EAAuBH,GAAG,CAACI,CAA3B,EAA8B,KAAKC,SAAL,GAAiB,CAA/C;AACA9B,cAAAA,QAAQ,CAAC+B,IAAT;AACH,aAJD;AAKH,WA1Cc,CA4Cf;;;AACA/B,UAAAA,QAAQ,CAACmB,WAAT,GAAuBhC,KAAK,CAACuD,MAA7B;AACA1C,UAAAA,QAAQ,CAACqB,SAAT,GAAqB,CAArB;;AACA,eAAKtB,SAAL,CAAeY,MAAf,CAAsBW,OAAtB,CAA8BC,KAAK,IAAI;AACnC,gBAAME,GAAG,GAAGF,KAAK,CAACG,QAAlB;AACA,gBAAMiB,MAAM,GAAG,KAAKb,SAAL,GAAiBP,KAAK,CAACb,UAAN,GAAmB,EAAnD;AACAV,YAAAA,QAAQ,CAAC2B,MAAT,CAAgBF,GAAG,CAACG,CAApB,EAAuBH,GAAG,CAACI,CAA3B,EAA8Bc,MAA9B;AACA3C,YAAAA,QAAQ,CAACgC,MAAT;AACH,WALD;AAMH;AAED;AACJ;AACA;;;AACWY,QAAAA,aAAa,GAAY;AAC5B,cAAMT,WAAW,GAAG,KAAKpC,SAAL,CAAeqC,mBAAf,EAApB;;AACA,cAAMS,SAAS,GAAG,GAAlB,CAF4B,CAEL;;AAEvB,cAAIC,eAAe,GAAG,IAAtB;;AAEA,eAAK/C,SAAL,CAAeY,MAAf,CAAsBW,OAAtB,CAA8B,CAACyB,YAAD,EAAevB,KAAf,KAAyB;AACnD,gBAAIwB,gBAAgB,GAAG,KAAvB,CADmD,CAGnD;;AACA,iBAAK,IAAMC,UAAX,IAAyBd,WAAzB,EAAsC;AAClC,kBAAMe,QAAQ,GAAG9D,IAAI,CAAC8D,QAAL,CAAcH,YAAY,CAACrB,QAA3B,EAAqCuB,UAArC,CAAjB;;AACA,kBAAIC,QAAQ,IAAIL,SAAhB,EAA2B;AACvBG,gBAAAA,gBAAgB,GAAG,IAAnB;AACA;AACH;AACJ;;AAED,gBAAI,CAACA,gBAAL,EAAuB;AACnBG,cAAAA,OAAO,CAACC,IAAR,yBAAoB5B,KAApB,UAA8BuB,YAAY,CAACnB,CAA3C,UAAiDmB,YAAY,CAAClB,CAA9D;AACAiB,cAAAA,eAAe,GAAG,KAAlB;AACH;AACJ,WAhBD;;AAkBA,cAAIA,eAAJ,EAAqB;AACjBK,YAAAA,OAAO,CAACE,GAAR,CAAY,oBAAZ;AACH,WAFD,MAEO;AACHF,YAAAA,OAAO,CAACE,GAAR,CAAY,oBAAZ;AACH;;AAED,iBAAOP,eAAP;AACH;AAED;AACJ;AACA;;;AACWQ,QAAAA,wBAAwB,GAAG;AAC9B;AACA,eAAKC,sBAAL;AACA,eAAKC,mBAAL;AACA,eAAKC,qBAAL;AACH;;AAEOF,QAAAA,sBAAsB,GAAG;AAC7B;AACA,cAAM5C,MAAM,GAAG,CACX;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,CADW,EAEX;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAjB,CAFW,EAGX;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAHW,CAAf;AAKAA,UAAAA,MAAM,CAACW,OAAP,CAAeoC,CAAC,IAAIA,CAAC,CAAChD,UAAF,GAAe,GAAnC;AAEA,eAAKX,SAAL,CAAeY,MAAf,GAAwBA,MAAxB;AACAwC,UAAAA,OAAO,CAACE,GAAR,CAAY,OAAZ,EAAqB,KAAKT,aAAL,EAArB;AACH;;AAEOY,QAAAA,mBAAmB,GAAG;AAC1B;AACA,cAAM7C,MAAM,GAAG,CACX;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,CADW,EAEX;AAAA;AAAA,sCAAc,CAAd,EAAiB,GAAjB,CAFW,EAGX;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAHW,CAAf;AAKAA,UAAAA,MAAM,CAACW,OAAP,CAAeoC,CAAC,IAAIA,CAAC,CAAChD,UAAF,GAAe,GAAnC,EAP0B,CAOe;;AAEzC,eAAKX,SAAL,CAAeY,MAAf,GAAwBA,MAAxB;AACAwC,UAAAA,OAAO,CAACE,GAAR,CAAY,SAAZ,EAAuB,KAAKT,aAAL,EAAvB;AACH;;AAEOa,QAAAA,qBAAqB,GAAG;AAC5B;AACA,cAAM9C,MAAM,GAAG,CACX;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,CADW,EAEX;AAAA;AAAA,sCAAc,CAAd,EAAiB,GAAjB,CAFW,EAGX;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAHW,CAAf;AAKAA,UAAAA,MAAM,CAACW,OAAP,CAAeoC,CAAC,IAAIA,CAAC,CAAChD,UAAF,GAAe,GAAnC,EAP4B,CAOa;;AAEzC,eAAKX,SAAL,CAAeY,MAAf,GAAwBA,MAAxB;AACAwC,UAAAA,OAAO,CAACE,GAAR,CAAY,SAAZ,EAAuB,KAAKT,aAAL,EAAvB;AACH;;AAjM0C,O;;;;;iBAEV,I;;;;;;;iBAGL,I;;;;;;;iBAGD,C", "sourcesContent": ["import { _decorator, Component, Graphics, Color, Vec2 } from 'cc';\nimport { EDITOR } from 'cc/env';\nimport { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\n\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\n\n/**\n * 路径测试示例 - 验证Catmull-Rom曲线是否正确经过所有定义点\n */\n@ccclass('PathTestExample')\n@menu(\"怪物/编辑器/路径测试示例\")\n@executeInEditMode(true)\nexport class PathTestExample extends Component {\n    @property({ displayName: \"显示测试点\" })\n    public showTestPoints: boolean = true;\n\n    @property({ displayName: \"显示曲线\" })\n    public showCurve: boolean = true;\n\n    @property({ displayName: \"点大小\" })\n    public pointSize: number = 8;\n\n    private _graphics: Graphics | null = null;\n    private _testPath: PathData = new PathData();\n\n    public get graphics(): Graphics {\n        if (!this._graphics) {\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\n        }\n        return this._graphics;\n    }\n\n    protected onLoad() {\n        this.createTestPath();\n    }\n\n    private createTestPath() {\n        // 创建一个简单的测试路径\n        this._testPath.name = \"测试路径\";\n        this._testPath.segments = 30;\n        this._testPath.closed = false;\n\n        // 添加几个测试点\n        const testPoints = [\n            new PathPoint(-200, 0),     // 起点\n            new PathPoint(-100, 150),   // 上弯\n            new PathPoint(0, 0),        // 中点\n            new PathPoint(100, -150),   // 下弯\n            new PathPoint(200, 0),      // 终点\n        ];\n\n        // 设置不同的平滑程度来测试效果\n        testPoints[0].smoothness = 0.5;\n        testPoints[1].smoothness = 0.8;  // 高平滑度\n        testPoints[2].smoothness = 0.2;  // 低平滑度（更尖锐）\n        testPoints[3].smoothness = 0.8;  // 高平滑度\n        testPoints[4].smoothness = 0.5;\n\n        this._testPath.points = testPoints;\n    }\n\n    public update(_dt: number) {\n        if (EDITOR) {\n            this.drawTest();\n        }\n    }\n\n    private drawTest() {\n        const graphics = this.graphics;\n        graphics.clear();\n\n        // 绘制原始定义点\n        if (this.showTestPoints) {\n            graphics.fillColor = Color.RED;\n            graphics.strokeColor = Color.BLACK;\n            graphics.lineWidth = 2;\n\n            this._testPath.points.forEach((point, index) => {\n                const pos = point.position;\n                graphics.circle(pos.x, pos.y, this.pointSize);\n                graphics.fill();\n                graphics.stroke();\n\n                // 绘制点的索引\n                // 注意：这里只是示意，实际文字绘制需要使用Label组件\n            });\n        }\n\n        // 绘制Catmull-Rom曲线\n        if (this.showCurve) {\n            graphics.strokeColor = Color.WHITE;\n            graphics.lineWidth = 3;\n\n            const curvePoints = this._testPath.generateCurvePoints();\n            if (curvePoints.length > 1) {\n                graphics.moveTo(curvePoints[0].x, curvePoints[0].y);\n                for (let i = 1; i < curvePoints.length; i++) {\n                    graphics.lineTo(curvePoints[i].x, curvePoints[i].y);\n                }\n                graphics.stroke();\n            }\n\n            // 用绿色标记曲线经过的原始定义点\n            graphics.fillColor = Color.GREEN;\n            this._testPath.points.forEach(point => {\n                const pos = point.position;\n                graphics.circle(pos.x, pos.y, this.pointSize / 2);\n                graphics.fill();\n            });\n        }\n\n        // 绘制平滑程度指示器\n        graphics.strokeColor = Color.YELLOW;\n        graphics.lineWidth = 1;\n        this._testPath.points.forEach(point => {\n            const pos = point.position;\n            const radius = this.pointSize + point.smoothness * 20;\n            graphics.circle(pos.x, pos.y, radius);\n            graphics.stroke();\n        });\n    }\n\n    /**\n     * 验证曲线是否经过定义点\n     */\n    public validateCurve(): boolean {\n        const curvePoints = this._testPath.generateCurvePoints();\n        const tolerance = 1.0; // 允许的误差范围\n\n        let allPointsPassed = true;\n        \n        this._testPath.points.forEach((definedPoint, index) => {\n            let foundNearbyPoint = false;\n            \n            // 检查曲线点中是否有足够接近定义点的点\n            for (const curvePoint of curvePoints) {\n                const distance = Vec2.distance(definedPoint.position, curvePoint);\n                if (distance <= tolerance) {\n                    foundNearbyPoint = true;\n                    break;\n                }\n            }\n            \n            if (!foundNearbyPoint) {\n                console.warn(`定义点 ${index} (${definedPoint.x}, ${definedPoint.y}) 没有被曲线经过`);\n                allPointsPassed = false;\n            }\n        });\n\n        if (allPointsPassed) {\n            console.log(\"✅ 验证通过：曲线正确经过所有定义点\");\n        } else {\n            console.log(\"❌ 验证失败：曲线没有经过某些定义点\");\n        }\n\n        return allPointsPassed;\n    }\n\n    /**\n     * 创建不同类型的测试路径\n     */\n    public createDifferentTestPaths() {\n        // 可以在这里添加更多测试用例\n        this.createStraightLineTest();\n        this.createSharpTurnTest();\n        this.createSmoothCurveTest();\n    }\n\n    private createStraightLineTest() {\n        // 测试直线（所有点在一条线上）\n        const points = [\n            new PathPoint(-100, 0),\n            new PathPoint(0, 0),\n            new PathPoint(100, 0),\n        ];\n        points.forEach(p => p.smoothness = 0.5);\n        \n        this._testPath.points = points;\n        console.log(\"直线测试:\", this.validateCurve());\n    }\n\n    private createSharpTurnTest() {\n        // 测试尖锐转角\n        const points = [\n            new PathPoint(-100, 0),\n            new PathPoint(0, 100),\n            new PathPoint(100, 0),\n        ];\n        points.forEach(p => p.smoothness = 0.1); // 低平滑度\n        \n        this._testPath.points = points;\n        console.log(\"尖锐转角测试:\", this.validateCurve());\n    }\n\n    private createSmoothCurveTest() {\n        // 测试平滑曲线\n        const points = [\n            new PathPoint(-100, 0),\n            new PathPoint(0, 100),\n            new PathPoint(100, 0),\n        ];\n        points.forEach(p => p.smoothness = 0.9); // 高平滑度\n        \n        this._testPath.points = points;\n        console.log(\"平滑曲线测试:\", this.validateCurve());\n    }\n}\n"]}