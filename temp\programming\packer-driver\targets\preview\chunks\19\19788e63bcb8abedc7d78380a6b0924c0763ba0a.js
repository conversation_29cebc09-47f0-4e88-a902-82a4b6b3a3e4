System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec2, EDITOR, PathData, PathPoint, _dec, _dec2, _dec3, _class, _crd, ccclass, executeInEditMode, menu, PathSegmentOptimizationTest;

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec2 = _cc.Vec2;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
      PathPoint = _unresolved_2.PathPoint;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "8f6e19WAtVAgLfFFvJKAhdZ", "PathSegmentOptimizationTest", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Vec2']);

      ({
        ccclass,
        executeInEditMode,
        menu
      } = _decorator);
      /**
       * 路径段优化测试 - 验证直线段的点数优化
       */

      _export("PathSegmentOptimizationTest", PathSegmentOptimizationTest = (_dec = ccclass('PathSegmentOptimizationTest'), _dec2 = menu("怪物/编辑器/路径段优化测试"), _dec3 = executeInEditMode(true), _dec(_class = _dec2(_class = _dec3(_class = class PathSegmentOptimizationTest extends Component {
        onLoad() {
          if (EDITOR) {
            this.runOptimizationTests();
          }
        }

        runOptimizationTests() {
          console.log("=== 路径段优化测试开始 ===");
          this.testLinearSegmentOptimization();
          this.testMixedSegmentOptimization();
          this.testCurveSegmentNormal();
          console.log("=== 路径段优化测试结束 ===");
        }
        /**
         * 测试纯直线段的优化
         */


        testLinearSegmentOptimization() {
          console.log("\n测试1: 纯直线段优化");
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          pathData.segments = 20; // 设置较大的段数
          // 创建3个点，全部设置为直线

          pathData.points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 100), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0)];
          pathData.points.forEach(p => p.smoothness = 0);
          var curvePoints = pathData.generateCurvePoints();
          console.log("segments\u8BBE\u7F6E: " + pathData.segments);
          console.log("\u751F\u6210\u7684\u70B9\u6570: " + curvePoints.length);
          console.log("\u7406\u8BBA\u6700\u5C11\u70B9\u6570: " + pathData.points.length + " (\u8D77\u70B9 + \u5404\u6BB5\u7EC8\u70B9)"); // 验证点数是否优化

          var expectedPoints = pathData.points.length;

          if (curvePoints.length === expectedPoints) {
            console.log("✅ 直线段优化成功：点数已最小化");
          } else {
            console.log("\u274C \u76F4\u7EBF\u6BB5\u4F18\u5316\u5931\u8D25\uFF1A\u671F\u671B" + expectedPoints + "\u4E2A\u70B9\uFF0C\u5B9E\u9645" + curvePoints.length + "\u4E2A\u70B9");
          } // 验证点的位置是否正确


          this.validateLinearPoints(curvePoints, pathData.points);
        }
        /**
         * 测试混合段的优化（部分直线，部分曲线）
         */


        testMixedSegmentOptimization() {
          console.log("\n测试2: 混合段优化");
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          pathData.segments = 20; // 创建4个点：直线-曲线-直线

          pathData.points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-150, 0), // smoothness = 0 (直线)
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-50, 100), // smoothness = 0 (直线)
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(50, -100), // smoothness = 1 (曲线)
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(150, 0) // smoothness = 1 (曲线)
          ];
          pathData.points[0].smoothness = 0; // 第一段：直线

          pathData.points[1].smoothness = 0; // 第一段：直线

          pathData.points[2].smoothness = 1; // 第二段：曲线

          pathData.points[3].smoothness = 1; // 第二段：曲线

          var curvePoints = pathData.generateCurvePoints();
          console.log("segments\u8BBE\u7F6E: " + pathData.segments);
          console.log("\u751F\u6210\u7684\u70B9\u6570: " + curvePoints.length); // 分析点数分布
          // 第一段（直线）：应该只有2个点（起点+终点）
          // 第二段（曲线）：应该有segments+1个点

          var expectedLinearPoints = 2; // 起点 + 第一段终点

          var expectedCurvePoints = pathData.segments; // 第二段的segments个点

          var expectedTotal = expectedLinearPoints + expectedCurvePoints;
          console.log("\u671F\u671B\u70B9\u6570\u5206\u5E03: \u76F4\u7EBF\u6BB52\u4E2A\u70B9 + \u66F2\u7EBF\u6BB5" + expectedCurvePoints + "\u4E2A\u70B9 = " + expectedTotal + "\u4E2A\u70B9");

          if (curvePoints.length === expectedTotal) {
            console.log("✅ 混合段优化成功");
          } else {
            console.log("\u274C \u6DF7\u5408\u6BB5\u4F18\u5316\u5F02\u5E38\uFF1A\u671F\u671B" + expectedTotal + "\u4E2A\u70B9\uFF0C\u5B9E\u9645" + curvePoints.length + "\u4E2A\u70B9");
          }
        }
        /**
         * 测试纯曲线段（确保曲线段点数正常）
         */


        testCurveSegmentNormal() {
          console.log("\n测试3: 纯曲线段正常生成");
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          pathData.segments = 10; // 创建3个点，全部设置为曲线

          pathData.points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 100), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0)];
          pathData.points.forEach(p => p.smoothness = 1);
          var curvePoints = pathData.generateCurvePoints();
          console.log("segments\u8BBE\u7F6E: " + pathData.segments);
          console.log("\u751F\u6210\u7684\u70B9\u6570: " + curvePoints.length); // 对于曲线，期望点数 = 起点 + 每段的segments个点

          var expectedPoints = 1 + (pathData.points.length - 1) * pathData.segments;
          console.log("\u671F\u671B\u70B9\u6570: 1(\u8D77\u70B9) + " + (pathData.points.length - 1) + "\u6BB5 \xD7 " + pathData.segments + "\u70B9/\u6BB5 = " + expectedPoints + "\u4E2A\u70B9");

          if (curvePoints.length === expectedPoints) {
            console.log("✅ 曲线段点数正常");
          } else {
            console.log("\u274C \u66F2\u7EBF\u6BB5\u70B9\u6570\u5F02\u5E38\uFF1A\u671F\u671B" + expectedPoints + "\u4E2A\u70B9\uFF0C\u5B9E\u9645" + curvePoints.length + "\u4E2A\u70B9");
          }
        }
        /**
         * 验证直线点的位置是否正确
         */


        validateLinearPoints(curvePoints, originalPoints) {
          console.log("验证直线点位置:");

          for (var i = 0; i < originalPoints.length; i++) {
            var originalPos = originalPoints[i].position;
            var curvePos = curvePoints[i];
            var distance = Vec2.distance(originalPos, curvePos);

            if (distance < 0.001) {
              console.log("  \u70B9" + i + ": \u2705 \u4F4D\u7F6E\u6B63\u786E (" + curvePos.x.toFixed(1) + ", " + curvePos.y.toFixed(1) + ")");
            } else {
              console.log("  \u70B9" + i + ": \u274C \u4F4D\u7F6E\u504F\u5DEE " + distance.toFixed(3) + " - \u671F\u671B(" + originalPos.x + ", " + originalPos.y + "), \u5B9E\u9645(" + curvePos.x.toFixed(1) + ", " + curvePos.y.toFixed(1) + ")");
            }
          }
        }
        /**
         * 性能对比测试
         */


        performanceTest() {
          console.log("\n=== 性能对比测试 ==="); // 创建一个包含多个直线段的路径

          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          pathData.segments = 50; // 大段数
          // 创建10个点的直线路径

          pathData.points = [];

          for (var i = 0; i < 10; i++) {
            var point = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
              error: Error()
            }), PathPoint) : PathPoint)(i * 100, Math.sin(i) * 50);
            point.smoothness = 0; // 全部设为直线

            pathData.points.push(point);
          }

          var startTime = performance.now();
          var curvePoints = pathData.generateCurvePoints();
          var endTime = performance.now();
          console.log("\u8DEF\u5F84\u70B9\u6570: " + pathData.points.length);
          console.log("segments\u8BBE\u7F6E: " + pathData.segments);
          console.log("\u751F\u6210\u7684\u66F2\u7EBF\u70B9\u6570: " + curvePoints.length);
          console.log("\u751F\u6210\u65F6\u95F4: " + (endTime - startTime).toFixed(2) + "ms"); // 计算优化效果

          var unoptimizedPoints = 1 + (pathData.points.length - 1) * pathData.segments;
          var optimizedPoints = curvePoints.length;
          var reduction = ((unoptimizedPoints - optimizedPoints) / unoptimizedPoints * 100).toFixed(1);
          console.log("\u672A\u4F18\u5316\u70B9\u6570: " + unoptimizedPoints);
          console.log("\u4F18\u5316\u540E\u70B9\u6570: " + optimizedPoints);
          console.log("\u70B9\u6570\u51CF\u5C11: " + reduction + "%");
        }

      }) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=19788e63bcb8abedc7d78380a6b0924c0763ba0a.js.map