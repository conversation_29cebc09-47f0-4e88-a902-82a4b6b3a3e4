{"__type__": "cc.TextAsset", "_name": "README_PathDataCache", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "text": "# PathData 内置缓存系统\n\n## 设计理念\n\n将缓存逻辑直接集成到PathData类中，而不是使用独立的缓存管理器。这种设计更符合面向对象的原则，数据和行为封装在一起。\n\n## 核心优势\n\n### 🎯 面向对象设计\n- **数据封装**: 缓存逻辑与数据紧密结合\n- **职责单一**: PathData负责自己的缓存管理\n- **接口简洁**: 外部使用者无需关心缓存细节\n\n### 🚀 性能优化\n- **智能缓存**: 只有数据变化时才重新计算\n- **版本控制**: 基于数据内容的哈希值检测变化\n- **延迟计算**: 只在需要时生成曲线点\n\n### 💾 序列化控制\n- **自定义序列化**: 排除缓存数据，只保存核心数据\n- **自动清理**: 反序列化时自动清除缓存\n- **数据一致性**: 确保缓存与实际数据同步\n\n## 实现细节\n\n### 缓存字段\n```typescript\n// 缓存的路径数据（不参与序列化）\nprivate _cachedCurvePoints: Vec2[] | null = null;\nprivate _cachedTotalDistance: number = 0;\nprivate _cachedDistances: number[] = [];\nprivate _cacheVersion: number = 0; // 用于检测数据变化\n```\n\n### 版本控制机制\n```typescript\nprivate getCurrentDataVersion(): number {\n    let hash = 0;\n    \n    // 基于关键属性计算哈希\n    hash = this.hashString(this.name, hash);\n    hash = this.hashNumber(this.segments, hash);\n    hash = this.hashBoolean(this.closed, hash);\n    \n    // 基于所有路径点计算哈希\n    for (const point of this.points) {\n        hash = this.hashNumber(point.position.x, hash);\n        hash = this.hashNumber(point.position.y, hash);\n        hash = this.hashNumber(point.smoothness, hash);\n        // ... 其他属性\n    }\n    \n    return hash;\n}\n```\n\n### 智能缓存检查\n```typescript\npublic generateCurvePoints(): Vec2[] {\n    // 检查缓存是否有效\n    if (this._cachedCurvePoints && this.isCacheValid()) {\n        return this._cachedCurvePoints;\n    }\n\n    // 生成新的曲线点并缓存\n    const curvePoints = this.generateCurvePointsInternal();\n    this._cachedCurvePoints = curvePoints;\n    this.calculateAndCacheDistances();\n    this.updateCacheVersion();\n\n    return curvePoints;\n}\n```\n\n## 序列化控制\n\n### 自定义序列化\n```typescript\npublic toJSON(): any {\n    return {\n        name: this.name,\n        startIdx: this.startIdx,\n        endIdx: this.endIdx,\n        points: this.points,\n        segments: this.segments,\n        closed: this.closed\n        // 注意：缓存字段被排除\n    };\n}\n```\n\n### 自定义反序列化\n```typescript\npublic fromJSON(data: any): void {\n    this.name = data.name || \"\";\n    this.startIdx = data.startIdx || 0;\n    this.endIdx = data.endIdx || -1;\n    this.points = data.points || [];\n    this.segments = data.segments || 20;\n    this.closed = data.closed || false;\n    \n    // 清除缓存，确保使用新数据重新计算\n    this.clearCache();\n}\n```\n\n## 使用方式\n\n### PathMovable中的使用\n```typescript\nprivate loadPathData() {\n    if (!this.pathAsset) return;\n\n    // 创建PathData实例并加载数据\n    this._pathData = PathData.fromJSON(this.pathAsset.json);\n    \n    // 使用PathData的内置缓存获取数据\n    this._curvePoints = this._pathData.generateCurvePoints();\n    this._totalDistance = this._pathData.getTotalDistance();\n    this._distances = this._pathData.getDistances();\n}\n```\n\n### 多个组件共享同一路径\n```typescript\n// 第一个组件：生成并缓存数据\nconst pathData1 = PathData.fromJSON(pathAsset.json);\nconst curvePoints1 = pathData1.generateCurvePoints(); // 计算并缓存\n\n// 第二个组件：使用相同的PathData实例\nconst pathData2 = PathData.fromJSON(pathAsset.json);\nconst curvePoints2 = pathData2.generateCurvePoints(); // 直接使用缓存\n```\n\n## 缓存生命周期\n\n### 1. 缓存创建\n- 首次调用`generateCurvePoints()`时创建\n- 计算曲线点、总距离、距离数组\n- 记录当前数据版本\n\n### 2. 缓存验证\n- 每次访问时检查数据版本\n- 如果数据未变化，直接返回缓存\n- 如果数据已变化，重新计算\n\n### 3. 缓存失效\n- 数据修改时自动失效\n- 调用`clearCache()`手动清除\n- 反序列化时自动清除\n\n## 性能特性\n\n### 内存效率\n- **按需分配**: 只有使用时才分配缓存内存\n- **自动清理**: 数据变化时自动清理旧缓存\n- **版本控制**: 避免不必要的重新计算\n\n### CPU效率\n- **哈希检查**: 快速检测数据变化\n- **延迟计算**: 只在需要时计算\n- **批量缓存**: 一次计算，多次使用\n\n### 对比独立缓存管理器\n\n| 特性 | 独立缓存管理器 | PathData内置缓存 |\n|------|---------------|------------------|\n| **设计复杂度** | 高（需要额外的管理类） | 低（集成在数据类中） |\n| **内存管理** | 需要手动清理 | 自动管理 |\n| **数据一致性** | 可能不同步 | 始终同步 |\n| **序列化控制** | 复杂 | 简单 |\n| **使用便利性** | 需要了解缓存API | 透明使用 |\n\n## 最佳实践\n\n### 1. 数据修改后清理缓存\n```typescript\n// 修改路径数据后\npathData.points.push(newPoint);\npathData.clearCache(); // 确保缓存失效\n```\n\n### 2. 批量修改优化\n```typescript\n// 批量修改时，最后统一清理缓存\npathData.points = newPoints;\npathData.segments = newSegments;\npathData.closed = newClosed;\npathData.clearCache(); // 一次性清理\n```\n\n### 3. 共享PathData实例\n```typescript\n// 推荐：多个组件共享同一个PathData实例\nconst sharedPathData = PathData.fromJSON(pathAsset.json);\n\nenemies.forEach(enemy => {\n    const pathMovable = enemy.getComponent(PathMovable);\n    pathMovable.setPathData(sharedPathData); // 共享实例\n});\n```\n\n## 调试技巧\n\n### 缓存命中率监控\n```typescript\n// 在generateCurvePoints中添加日志\npublic generateCurvePoints(): Vec2[] {\n    if (this._cachedCurvePoints && this.isCacheValid()) {\n        console.log(\"Cache hit for path:\", this.name);\n        return this._cachedCurvePoints;\n    }\n    \n    console.log(\"Cache miss for path:\", this.name);\n    // ... 重新计算\n}\n```\n\n### 版本变化追踪\n```typescript\n// 监控数据版本变化\nconst oldVersion = this._cacheVersion;\nconst newVersion = this.getCurrentDataVersion();\nif (oldVersion !== newVersion) {\n    console.log(`Path data changed: ${oldVersion} -> ${newVersion}`);\n}\n```\n\n这种设计让PathData成为一个自包含的、高效的数据类，既保持了性能优化，又简化了使用方式。\n"}