'use strict';
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const { updatePropByDump, disconnectGroup } = require('./../../prop');
const path_1 = __importDefault(require("path"));
exports.template = `
<div class="component-container"></div>

<ui-prop>
    <ui-label slot="label">请从下拉框中选择:</ui-label>
    <ui-select slot="content" class="prefab-dropdown"></ui-select>
</ui-prop>
<ui-prop>
    <ui-button class="btn-preview" style="display: none;">添加到场景</ui-button>
    <ui-button class="btn-new">新建emitter</ui-button>
    <ui-button class="btn-copy" style="display: none;">从选择复制新emitter</ui-button>
    <ui-button class="btn-reset" style="display: none;">重置预览</ui-button>
</ui-prop>
`;
exports.$ = {
    componentContainer: '.component-container',
    prefabDropdown: '.prefab-dropdown',
    btnPreview: '.btn-preview',
    btnNew: '.btn-new',
    btnCopy: '.btn-copy',
    btnReset: '.btn-reset',
};
const emitterDir = 'db://assets/resources/game/prefabs/emitter/';
let prefabList = [];
let selectedPrefab = null;
async function loadPrefabList() {
    const pattern = `${emitterDir}**/*.prefab`;
    try {
        // @ts-ignore
        const res = await Editor.Message.request('asset-db', 'query-assets', { pattern });
        const arr = Array.isArray(res) ? res : (Array.isArray(res === null || res === void 0 ? void 0 : res[0]) ? res[0] : []);
        const prefabs = arr
            .filter((a) => a && !a.isDirectory && a.name.endsWith('.prefab'))
            .map((a) => ({
            name: String(a.name || '').replace(/\.prefab$/i, ''),
            path: a.path || '',
            uuid: a.uuid || ''
        }))
            .filter(p => p.name)
            .sort((a, b) => a.name.localeCompare(b.name));
        return prefabs;
    }
    catch (e) {
        console.warn('loadPrefabList failed', e);
        return [];
    }
}
function updateButtonVisibility() {
    var _a, _b;
    const hasSelection = selectedPrefab !== null;
    this.$.btnPreview.style.display = hasSelection ? 'inline-block' : 'none';
    this.$.btnCopy.style.display = hasSelection ? 'inline-block' : 'none';
    this.$.btnReset.style.display = ((_b = (_a = this.dump) === null || _a === void 0 ? void 0 : _a.value) === null || _b === void 0 ? void 0 : _b.uuid) ? 'inline-block' : 'none';
}
function update(dump) {
    updatePropByDump(this, dump);
    this.dump = dump;
}
async function ready() {
    disconnectGroup(this);
    // Load prefab list
    prefabList = await loadPrefabList();
    // Setup dropdown options
    const dropdown = this.$.prefabDropdown;
    dropdown.innerHTML = '';
    if (prefabList.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = '(无可用的prefab文件)';
        option.disabled = true;
        dropdown.appendChild(option);
    }
    else {
        // Add prefab options
        prefabList.forEach(prefab => {
            const option = document.createElement('option');
            option.value = prefab.uuid;
            option.textContent = prefab.name;
            dropdown.appendChild(option);
        });
    }
    // Handle dropdown selection change
    dropdown.addEventListener('change', () => {
        const selectedUuid = dropdown.value;
        selectedPrefab = prefabList.find(p => p.uuid === selectedUuid) || null;
        updateButtonVisibility.call(this);
    });
    // Handle preview button
    this.$.btnPreview.addEventListener('click', () => {
        var _a;
        if (selectedPrefab) {
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'instantiatePrefab',
                args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid, selectedPrefab.uuid]
            });
        }
    });
    // Handle new emitter button
    this.$.btnNew.addEventListener('click', async () => {
        console.log('Create new emitter');
        // @ts-ignore
        const dirPath = path_1.default.join(Editor.Project.path, "assets", "resources", "game", "prefabs", "emitter");
        // @ts-ignore
        const retData = await Editor.Dialog.save({
            path: dirPath,
            filters: [
                { name: 'Prefab', extensions: ['prefab'] },
            ],
        });
        if (retData.canceled || !retData.filePath) {
            return;
        }
        const name = path_1.default.relative(dirPath, retData.filePath);
        const nameWithoutExt = name.replace(/\.prefab$/i, '');
        console.log('New emitter name:', name);
        const filePath = `${emitterDir}${name}`;
        try {
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'createNewEmitter',
                args: [nameWithoutExt, filePath]
            });
            // Refresh prefab list
            prefabList = await loadPrefabList();
        }
        catch (e) {
            console.error('Failed to create new emitter:', e);
        }
    });
    // Handle copy emitter button
    this.$.btnCopy.addEventListener('click', async () => {
        var _a;
        if (!selectedPrefab)
            return;
        const sourceUrl = selectedPrefab.path;
        const targetUrl = sourceUrl + '_copy';
        const nameWithoutExt = selectedPrefab.name + '_copy';
        console.log('Copy emitter from ', sourceUrl, ' to ', targetUrl);
        try {
            // @ts-ignore
            await Editor.Message.request('asset-db', 'copy-asset', sourceUrl + '.prefab', targetUrl + '.prefab');
            // Refresh prefab list
            prefabList = await loadPrefabList();
            selectedPrefab = prefabList.find(p => p.name === nameWithoutExt) || null;
            if (selectedPrefab) {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'emitter-editor',
                    method: 'instantiatePrefab',
                    args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid, selectedPrefab.uuid]
                });
            }
        }
        catch (e) {
            console.error('Failed to copy emitter:', e);
        }
    });
    // Handle reset preview button
    this.$.btnReset.addEventListener('click', () => {
        var _a, _b;
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'replay',
            args: [(_b = (_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid) === null || _b === void 0 ? void 0 : _b.value]
        });
    });
    // Initial button visibility update
    updateButtonVisibility.call(this);
}
//# sourceMappingURL=data:application/json;base64,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