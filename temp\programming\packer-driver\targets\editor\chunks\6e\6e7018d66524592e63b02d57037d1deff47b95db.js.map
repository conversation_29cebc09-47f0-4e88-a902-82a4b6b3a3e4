{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathEditorExample.ts"], "names": ["_decorator", "Component", "Node", "instantiate", "Prefab", "JsonAsset", "EDITOR", "PathData", "PathPoint", "PathEditor", "PathFollower", "ccclass", "property", "executeInEditMode", "menu", "PathEditorExample", "type", "displayName", "_pathEditor", "_testObject", "onLoad", "setup<PERSON>xample", "createPathEditor", "createExamplePath", "testObjectPrefab", "createTestObject", "pathEditorNode", "node", "getChildByName", "parent", "getComponent", "addComponent", "examplePathData", "pathData", "name", "segments", "closed", "points", "smoothness", "speed", "for<PERSON>ach", "point", "addPoint", "updateCurve", "pathFollower", "pathAsset", "moveSpeed", "autoMove", "loop", "autoFacing", "createPathTemplates", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createStraightPath", "createCircularPath", "radius", "pointCount", "i", "angle", "Math", "PI", "x", "cos", "y", "sin", "createZigzagPath", "index", "testPathFollowing", "resetToStart", "startAutoMove", "stopPathFollowing", "stopAutoMove"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;AACxDC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;AACVC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDd,U;AAEvD;AACA;AACA;AACA;;mCAIae,iB,WAHZJ,OAAO,CAAC,mBAAD,C,UACPG,IAAI,CAAC,eAAD,C,UACJD,iBAAiB,CAAC,IAAD,C,UAEbD,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAEZ,MAAR;AAAgBa,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,UAGRL,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAEX,SAAR;AAAmBY,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,yDAPb,MAGaF,iBAHb,SAGuCd,SAHvC,CAGiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAOrCiB,WAPqC,GAOJ,IAPI;AAAA,eAQrCC,WARqC,GAQV,IARU;AAAA;;AAUnCC,QAAAA,MAAM,GAAG;AACf,cAAId,MAAJ,EAAY;AACR,iBAAKe,YAAL;AACH;AACJ;;AAEOA,QAAAA,YAAY,GAAG;AACnB;AACA,eAAKC,gBAAL,GAFmB,CAInB;;AACA,eAAKC,iBAAL,GALmB,CAOnB;;AACA,cAAI,KAAKC,gBAAT,EAA2B;AACvB,iBAAKC,gBAAL;AACH;AACJ;;AAEOH,QAAAA,gBAAgB,GAAG;AACvB;AACA,cAAII,cAAc,GAAG,KAAKC,IAAL,CAAUC,cAAV,CAAyB,YAAzB,CAArB;;AACA,cAAI,CAACF,cAAL,EAAqB;AACjBA,YAAAA,cAAc,GAAG,IAAIxB,IAAJ,CAAS,YAAT,CAAjB;AACAwB,YAAAA,cAAc,CAACG,MAAf,GAAwB,KAAKF,IAA7B;AACH,WANsB,CAQvB;;;AACA,eAAKT,WAAL,GAAmBQ,cAAc,CAACI,YAAf;AAAA;AAAA,uCAAnB;;AACA,cAAI,CAAC,KAAKZ,WAAV,EAAuB;AACnB,iBAAKA,WAAL,GAAmBQ,cAAc,CAACK,YAAf;AAAA;AAAA,yCAAnB;AACH,WAZsB,CAcvB;;;AACA,cAAI,KAAKC,eAAT,EAA0B;AACtB,iBAAKd,WAAL,CAAiBe,QAAjB,GAA4B,KAAKD,eAAjC;AACH;AACJ;;AAEOT,QAAAA,iBAAiB,GAAG;AACxB,cAAI,CAAC,KAAKL,WAAN,IAAqB,KAAKc,eAA9B,EAA+C,OADvB,CAGxB;;AACA,gBAAMC,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAA,UAAAA,QAAQ,CAACC,IAAT,GAAgB,QAAhB;AACAD,UAAAA,QAAQ,CAACE,QAAT,GAAoB,EAApB;AACAF,UAAAA,QAAQ,CAACG,MAAT,GAAkB,KAAlB,CAPwB,CASxB;;AACA,gBAAMC,MAAM,GAAG,CACX;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,GAApB,EAAyB,CAAzB,CADW,EACoB;AAC/B;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,GAApB,EAAyB,CAAzB,CAFW,EAEoB;AAC/B;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAC,GAApB,EAAyB,CAAzB,CAHW,EAGoB;AAC/B;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAC,GAApB,EAAyB,CAAzB,CAJW,CAIoB;AAJpB,WAAf,CAVwB,CAiBxB;;AACAA,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUC,UAAV,GAAuB,GAAvB;AACAD,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUC,UAAV,GAAuB,GAAvB;AACAD,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUC,UAAV,GAAuB,GAAvB;AACAD,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUC,UAAV,GAAuB,GAAvB,CArBwB,CAuBxB;;AACAD,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUE,KAAV,GAAkB,GAAlB;AACAF,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUE,KAAV,GAAkB,GAAlB;AACAF,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUE,KAAV,GAAkB,GAAlB;AACAF,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUE,KAAV,GAAkB,GAAlB;AAEAN,UAAAA,QAAQ,CAACI,MAAT,GAAkBA,MAAlB,CA7BwB,CA+BxB;;AACAA,UAAAA,MAAM,CAACG,OAAP,CAAeC,KAAK,IAAI;AACpB,iBAAKvB,WAAL,CAAkBwB,QAAlB,CAA2BD,KAA3B;AACH,WAFD;;AAIA,eAAKvB,WAAL,CAAiByB,WAAjB;AACH;;AAEOlB,QAAAA,gBAAgB,GAAG;AACvB,cAAI,CAAC,KAAKD,gBAAN,IAA0B,CAAC,KAAKN,WAApC,EAAiD,OAD1B,CAGvB;;AACA,eAAKC,WAAL,GAAmBhB,WAAW,CAAC,KAAKqB,gBAAN,CAA9B;AACA,eAAKL,WAAL,CAAiBU,MAAjB,GAA0B,KAAKF,IAA/B;AACA,eAAKR,WAAL,CAAiBe,IAAjB,GAAwB,YAAxB,CANuB,CAQvB;;AACA,gBAAMU,YAAY,GAAG,KAAKzB,WAAL,CAAiBY,YAAjB;AAAA;AAAA,2CAArB,CATuB,CAWvB;;;AACA,cAAI,KAAKb,WAAL,CAAiBe,QAArB,EAA+B;AAC3BW,YAAAA,YAAY,CAACC,SAAb,GAAyB,KAAK3B,WAAL,CAAiBe,QAA1C;AACH,WAdsB,CAgBvB;;;AACAW,UAAAA,YAAY,CAACE,SAAb,GAAyB,GAAzB;AACAF,UAAAA,YAAY,CAACG,QAAb,GAAwB,IAAxB;AACAH,UAAAA,YAAY,CAACI,IAAb,GAAoB,IAApB;AACAJ,UAAAA,YAAY,CAACK,UAAb,GAA0B,IAA1B;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,mBAAmB,GAAG;AACzB,cAAI,CAAC,KAAKhC,WAAV,EAAuB,OADE,CAGzB;;AACA,eAAKA,WAAL,CAAiBS,IAAjB,CAAsBwB,iBAAtB,GAJyB,CAMzB;;;AACA,eAAKC,kBAAL,GAPyB,CAQzB;AACA;AACH;;AAEOA,QAAAA,kBAAkB,GAAG;AACzB,gBAAMf,MAAM,GAAG,CACX;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,EAAuB,CAAvB,CADW,EAEX;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,EAAsB,CAAtB,CAFW,CAAf;AAKAA,UAAAA,MAAM,CAACG,OAAP,CAAeC,KAAK,IAAI;AACpBA,YAAAA,KAAK,CAACH,UAAN,GAAmB,GAAnB;AACAG,YAAAA,KAAK,CAACF,KAAN,GAAc,GAAd;;AACA,iBAAKrB,WAAL,CAAkBwB,QAAlB,CAA2BD,KAA3B;AACH,WAJD;;AAMA,eAAKvB,WAAL,CAAkByB,WAAlB;AACH;;AAEOU,QAAAA,kBAAkB,GAAG;AACzB,gBAAMC,MAAM,GAAG,GAAf;AACA,gBAAMC,UAAU,GAAG,CAAnB;;AAEA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,UAApB,EAAgCC,CAAC,EAAjC,EAAqC;AACjC,kBAAMC,KAAK,GAAID,CAAC,GAAGD,UAAL,GAAmBG,IAAI,CAACC,EAAxB,GAA6B,CAA3C;AACA,kBAAMC,CAAC,GAAGF,IAAI,CAACG,GAAL,CAASJ,KAAT,IAAkBH,MAA5B;AACA,kBAAMQ,CAAC,GAAGJ,IAAI,CAACK,GAAL,CAASN,KAAT,IAAkBH,MAA5B;AAEA,kBAAMb,KAAK,GAAG;AAAA;AAAA,wCAAcmB,CAAd,EAAiBE,CAAjB,EAAoB,CAApB,CAAd;AACArB,YAAAA,KAAK,CAACH,UAAN,GAAmB,GAAnB,CANiC,CAMT;;AACxBG,YAAAA,KAAK,CAACF,KAAN,GAAc,GAAd;;AAEA,iBAAKrB,WAAL,CAAkBwB,QAAlB,CAA2BD,KAA3B;AACH,WAdwB,CAgBzB;;;AACA,cAAI,KAAKvB,WAAT,EAAsB,CAClB;AACH;;AAED,eAAKA,WAAL,CAAkByB,WAAlB;AACH;;AAEOqB,QAAAA,gBAAgB,GAAG;AACvB,gBAAM3B,MAAM,GAAG,CACX;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,EAAuB,CAAvB,CADW,EAEX;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,GAApB,EAAyB,CAAzB,CAFW,EAGX;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAC,GAAlB,EAAuB,CAAvB,CAHW,EAIX;AAAA;AAAA,sCAAc,GAAd,EAAmB,GAAnB,EAAwB,CAAxB,CAJW,EAKX;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,EAAsB,CAAtB,CALW,CAAf;AAQAA,UAAAA,MAAM,CAACG,OAAP,CAAe,CAACC,KAAD,EAAQwB,KAAR,KAAkB;AAC7BxB,YAAAA,KAAK,CAACH,UAAN,GAAmB2B,KAAK,GAAG,CAAR,KAAc,CAAd,GAAkB,GAAlB,GAAwB,GAA3C,CAD6B,CACmB;;AAChDxB,YAAAA,KAAK,CAACF,KAAN,GAAc,MAAM0B,KAAK,GAAG,EAA5B;;AACA,iBAAK/C,WAAL,CAAkBwB,QAAlB,CAA2BD,KAA3B;AACH,WAJD;;AAMA,eAAKvB,WAAL,CAAkByB,WAAlB;AACH;AAED;AACJ;AACA;;;AACWuB,QAAAA,iBAAiB,GAAG;AACvB,cAAI,CAAC,KAAK/C,WAAV,EAAuB;;AAEvB,gBAAMyB,YAAY,GAAG,KAAKzB,WAAL,CAAiBW,YAAjB;AAAA;AAAA,2CAArB;;AACA,cAAIc,YAAJ,EAAkB;AACdA,YAAAA,YAAY,CAACuB,YAAb;AACAvB,YAAAA,YAAY,CAACwB,aAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACWC,QAAAA,iBAAiB,GAAG;AACvB,cAAI,CAAC,KAAKlD,WAAV,EAAuB;;AAEvB,gBAAMyB,YAAY,GAAG,KAAKzB,WAAL,CAAiBW,YAAjB;AAAA;AAAA,2CAArB;;AACA,cAAIc,YAAJ,EAAkB;AACdA,YAAAA,YAAY,CAAC0B,YAAb;AACH;AACJ;;AA9M4C,O;;;;;iBAEJ,I;;;;;;;iBAGE,I", "sourcesContent": ["import { _decorator, Component, Node, Vec3, instantiate, Prefab, JsonAsset } from 'cc';\nimport { EDITOR } from 'cc/env';\nimport { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\nimport { PathEditor } from './PathEditor';\nimport { PathFollower } from './PathFollower';\n\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\n\n/**\n * 路径编辑器使用示例\n * 展示如何创建和使用路径编辑系统\n */\n@ccclass('PathEditorExample')\n@menu(\"怪物/编辑器/路径编辑示例\")\n@executeInEditMode(true)\nexport class PathEditorExample extends Component {\n    @property({ type: Prefab, displayName: \"测试对象预制体\" })\n    public testObjectPrefab: Prefab | null = null;\n\n    @property({ type: JsonAsset, displayName: \"示例路径数据\" })\n    public examplePathData: JsonAsset | null = null;\n\n    private _pathEditor: PathEditor | null = null;\n    private _testObject: Node | null = null;\n\n    protected onLoad() {\n        if (EDITOR) {\n            this.setupExample();\n        }\n    }\n\n    private setupExample() {\n        // 创建路径编辑器节点\n        this.createPathEditor();\n        \n        // 创建示例路径\n        this.createExamplePath();\n        \n        // 创建测试对象\n        if (this.testObjectPrefab) {\n            this.createTestObject();\n        }\n    }\n\n    private createPathEditor() {\n        // 查找或创建路径编辑器节点\n        let pathEditorNode = this.node.getChildByName('PathEditor');\n        if (!pathEditorNode) {\n            pathEditorNode = new Node('PathEditor');\n            pathEditorNode.parent = this.node;\n        }\n\n        // 添加PathEditor组件\n        this._pathEditor = pathEditorNode.getComponent(PathEditor);\n        if (!this._pathEditor) {\n            this._pathEditor = pathEditorNode.addComponent(PathEditor);\n        }\n\n        // 如果有示例路径数据，加载它\n        if (this.examplePathData) {\n            this._pathEditor.pathData = this.examplePathData;\n        }\n    }\n\n    private createExamplePath() {\n        if (!this._pathEditor || this.examplePathData) return;\n\n        // 创建一个示例路径（S形曲线）\n        const pathData = new PathData();\n        pathData.name = \"示例S形路径\";\n        pathData.segments = 20;\n        pathData.closed = false;\n\n        // 添加路径点\n        const points = [\n            new PathPoint(-300, 200, 0),   // 起点\n            new PathPoint(-100, 100, 0),   // 控制点1\n            new PathPoint(100, -100, 0),   // 控制点2\n            new PathPoint(300, -200, 0),   // 终点\n        ];\n\n        // 设置不同的平滑程度\n        points[0].smoothness = 0.3;\n        points[1].smoothness = 0.8;\n        points[2].smoothness = 0.8;\n        points[3].smoothness = 0.3;\n\n        // 设置速度\n        points[0].speed = 200;\n        points[1].speed = 400;\n        points[2].speed = 400;\n        points[3].speed = 200;\n\n        pathData.points = points;\n\n        // 添加点到编辑器\n        points.forEach(point => {\n            this._pathEditor!.addPoint(point);\n        });\n\n        this._pathEditor.updateCurve();\n    }\n\n    private createTestObject() {\n        if (!this.testObjectPrefab || !this._pathEditor) return;\n\n        // 创建测试对象\n        this._testObject = instantiate(this.testObjectPrefab);\n        this._testObject.parent = this.node;\n        this._testObject.name = \"TestObject\";\n\n        // 添加路径跟随器\n        const pathFollower = this._testObject.addComponent(PathFollower);\n        \n        // 如果有路径数据，设置给跟随器\n        if (this._pathEditor.pathData) {\n            pathFollower.pathAsset = this._pathEditor.pathData;\n        }\n\n        // 配置跟随器\n        pathFollower.moveSpeed = 100;\n        pathFollower.autoMove = true;\n        pathFollower.loop = true;\n        pathFollower.autoFacing = true;\n    }\n\n    /**\n     * 创建预设路径模板\n     */\n    public createPathTemplates() {\n        if (!this._pathEditor) return;\n\n        // 清空现有路径\n        this._pathEditor.node.removeAllChildren();\n\n        // 创建不同类型的路径模板\n        this.createStraightPath();\n        // this.createCircularPath();\n        // this.createZigzagPath();\n    }\n\n    private createStraightPath() {\n        const points = [\n            new PathPoint(-200, 0, 0),\n            new PathPoint(200, 0, 0),\n        ];\n\n        points.forEach(point => {\n            point.smoothness = 0.5;\n            point.speed = 300;\n            this._pathEditor!.addPoint(point);\n        });\n\n        this._pathEditor!.updateCurve();\n    }\n\n    private createCircularPath() {\n        const radius = 150;\n        const pointCount = 8;\n        \n        for (let i = 0; i < pointCount; i++) {\n            const angle = (i / pointCount) * Math.PI * 2;\n            const x = Math.cos(angle) * radius;\n            const y = Math.sin(angle) * radius;\n            \n            const point = new PathPoint(x, y, 0);\n            point.smoothness = 0.8; // 高平滑度创建圆形\n            point.speed = 250;\n            \n            this._pathEditor!.addPoint(point);\n        }\n\n        // 设置为闭合路径\n        if (this._pathEditor) {\n            // 这里需要访问PathEditor的内部数据，可能需要添加公共方法\n        }\n\n        this._pathEditor!.updateCurve();\n    }\n\n    private createZigzagPath() {\n        const points = [\n            new PathPoint(-200, 0, 0),\n            new PathPoint(-100, 100, 0),\n            new PathPoint(0, -100, 0),\n            new PathPoint(100, 100, 0),\n            new PathPoint(200, 0, 0),\n        ];\n\n        points.forEach((point, index) => {\n            point.smoothness = index % 2 === 0 ? 0.2 : 0.6; // 交替平滑度\n            point.speed = 200 + index * 50;\n            this._pathEditor!.addPoint(point);\n        });\n\n        this._pathEditor!.updateCurve();\n    }\n\n    /**\n     * 测试路径跟随\n     */\n    public testPathFollowing() {\n        if (!this._testObject) return;\n\n        const pathFollower = this._testObject.getComponent(PathFollower);\n        if (pathFollower) {\n            pathFollower.resetToStart();\n            pathFollower.startAutoMove();\n        }\n    }\n\n    /**\n     * 停止路径跟随\n     */\n    public stopPathFollowing() {\n        if (!this._testObject) return;\n\n        const pathFollower = this._testObject.getComponent(PathFollower);\n        if (pathFollower) {\n            pathFollower.stopAutoMove();\n        }\n    }\n}\n"]}