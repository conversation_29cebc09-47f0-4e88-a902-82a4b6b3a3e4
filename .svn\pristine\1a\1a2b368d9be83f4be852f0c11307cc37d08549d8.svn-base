import { _decorator, instantiate, Color, Component, JsonAsset, Rect, Vec3, Graphics, assetManager } from 'cc';
const { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, requireComponent  } = _decorator;
import { EDITOR } from 'cc/env';
import { FormationGroup, FormationPoint, SpawnGroup } from 'db://assets/bundles/common/script/game/data/WaveData';

@ccclass('FormationPointEditor')
@menu("怪物/编辑器/阵型点")
@requireComponent(Graphics)
@executeInEditMode(true)
@disallowMultiple(true)
export class FormationPointEditor extends Component {
    private _graphics: Graphics|null = null;
    public get graphics(): Graphics {
        if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
        }

        return this._graphics;
    }

    @property({type: [SpawnGroup], displayName: "出生组"})
    public spawnGroup: SpawnGroup[] = [];

    public get formationPoint(): FormationPoint {
        let point = new FormationPoint();
        point.spawnGroup = this.spawnGroup;
        point.x = this.node.position.x;
        point.y = this.node.position.y;
        return point;
    }

    public set formationPoint(value: FormationPoint) {
        this.spawnGroup = value.spawnGroup;
        this.node.position = new Vec3(value.x, value.y, 0);
    }

    public update(dt: number) {
        const graphics = this.graphics;
        graphics.clear();
        
        graphics.strokeColor = Color.RED;
        graphics.lineWidth = 10;
        graphics.circle(0, 0, 10);
        graphics.stroke();
    }
}