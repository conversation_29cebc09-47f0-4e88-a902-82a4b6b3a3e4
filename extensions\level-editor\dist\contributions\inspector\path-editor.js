/**
 * 路径编辑器
 */
'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const { updatePropByDump, disconnectGroup } = require('./../../prop');
exports.template = `
<div class="component-container"></div>
<ui-prop>
    <ui-button class="btn-add">添加路径点</ui-button>
    <ui-button class="btn-save">保存</ui-button>
    <ui-button class="btn-clear">清空路径</ui-button>
</ui-prop>
`;
exports.$ = {
    componentContainer: '.component-container',
    btnAdd: '.btn-add',
    btnSave: '.btn-save',
    btnClear: '.btn-clear',
};
function update(dump) {
    updatePropByDump(this, dump);
    this.dump = dump;
}
async function ready() {
    disconnectGroup(this);
    this.$.btnAdd.addEventListener('confirm', async () => {
        var _a;
        console.log('add path point', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'addPathPoint',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid.value]
        });
    });
    this.$.btnSave.addEventListener('confirm', async () => {
        var _a;
        console.log('save path', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'savePath',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid.value]
        });
    });
    this.$.btnClear.addEventListener('confirm', async () => {
        var _a;
        console.log('clear path', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'clearPath',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid.value]
        });
    });
}
//# sourceMappingURL=data:application/json;base64,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