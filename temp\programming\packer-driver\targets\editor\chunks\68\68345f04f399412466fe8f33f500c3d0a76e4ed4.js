System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, Component, JsonAsset, FormationGroup, FormationPoint, FormationPointEditor, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _crd, ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, requireComponent, FormationEditor;

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfFormationGroup(extras) {
    _reporterNs.report("FormationGroup", "db://assets/bundles/common/script/game/data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFormationPoint(extras) {
    _reporterNs.report("FormationPoint", "db://assets/bundles/common/script/game/data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFormationPointEditor(extras) {
    _reporterNs.report("FormationPointEditor", "./FormationPointEditor", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      Component = _cc.Component;
      JsonAsset = _cc.JsonAsset;
    }, function (_unresolved_2) {
      FormationGroup = _unresolved_2.FormationGroup;
      FormationPoint = _unresolved_2.FormationPoint;
    }, function (_unresolved_3) {
      FormationPointEditor = _unresolved_3.FormationPointEditor;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e2ae0RfVdhDR4WIUY+EAOh7", "FormationEditor", undefined);

      __checkObsolete__(['_decorator', 'instantiate', 'Node', 'Component', 'JsonAsset', 'Rect', 'Vec3', 'Graphics', 'assetManager']);

      ({
        ccclass,
        playOnFocus,
        executeInEditMode,
        property,
        disallowMultiple,
        menu,
        requireComponent
      } = _decorator);

      _export("FormationEditor", FormationEditor = (_dec = ccclass('FormationEditor'), _dec2 = menu("怪物/编辑器/阵型"), _dec3 = executeInEditMode(true), _dec4 = disallowMultiple(true), _dec5 = property({
        type: JsonAsset,
        displayName: "阵型数据"
      }), _dec6 = property({
        displayName: "阵型名字"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = (_class2 = class FormationEditor extends Component {
        constructor(...args) {
          super(...args);
          this._formationData = null;
          this._formationGroup = new (_crd && FormationGroup === void 0 ? (_reportPossibleCrUseOfFormationGroup({
            error: Error()
          }), FormationGroup) : FormationGroup)();
        }

        set formationData(value) {
          this._formationData = value;
          this.reload();
        }

        get formationData() {
          return this._formationData;
        }

        get formationName() {
          return this._formationGroup.name;
        }

        set formationName(value) {
          this._formationGroup.name = value;
        }

        reload() {
          if (!this._formationData) return;
          const formationGroup = new (_crd && FormationGroup === void 0 ? (_reportPossibleCrUseOfFormationGroup({
            error: Error()
          }), FormationGroup) : FormationGroup)();
          Object.assign(formationGroup, this._formationData.json);
          this._formationGroup = formationGroup;
          this.node.removeAllChildren();

          if (this._formationGroup && this._formationGroup.points.length > 0) {
            this._formationGroup.points.forEach(point => {
              this.addPoint(point);
            });
          }
        }

        save() {
          // save this._formationGroup to this._formationData
          const points = this.getComponentsInChildren(_crd && FormationPointEditor === void 0 ? (_reportPossibleCrUseOfFormationPointEditor({
            error: Error()
          }), FormationPointEditor) : FormationPointEditor);
          this._formationGroup.points = points.map(point => point.formationPoint);
          return JSON.stringify(this._formationGroup, null, 2);
        }

        addPoint(point) {
          const pointNode = new Node();
          pointNode.parent = this.node;
          const pointEditor = pointNode.addComponent(_crd && FormationPointEditor === void 0 ? (_reportPossibleCrUseOfFormationPointEditor({
            error: Error()
          }), FormationPointEditor) : FormationPointEditor);
          pointEditor.formationPoint = point;
        }

        addNewPoint(x, y) {
          const point = new (_crd && FormationPoint === void 0 ? (_reportPossibleCrUseOfFormationPoint({
            error: Error()
          }), FormationPoint) : FormationPoint)();
          point.x = x;
          point.y = y;
          this.addPoint(point);
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "formationData", [_dec5], Object.getOwnPropertyDescriptor(_class2.prototype, "formationData"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "formationName", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "formationName"), _class2.prototype)), _class2)) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=68345f04f399412466fe8f33f500c3d0a76e4ed4.js.map