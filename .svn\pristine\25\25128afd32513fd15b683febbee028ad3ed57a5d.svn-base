{"$schema": "./@types/schema/package/index.json", "package_version": 2, "name": "level-editor", "version": "1.0.0", "author": "Cocos Creator", "editor": ">=3.8.6", "scripts": {"preinstall": "node ./scripts/preinstall.js", "build": "npx tsc"}, "description": "i18n:level-editor.description", "main": "./dist/main.js", "dependencies": {"vue": "^3.1.4", "fs-extra": "^10.0.0"}, "devDependencies": {"@cocos/creator-types": "^3.8.6", "@types/fs-extra": "^9.0.5", "@types/node": "^18.17.1", "typescript": "^5.8.2"}, "panels": {"newlevel": {"title": "new Level Panel", "type": "dockable", "main": "dist/panels/newlevel/index.js", "size": {"min-width": 640, "min-height": 320, "width": 640, "height": 320}}}, "contributions": {"messages": {"save-level": {"methods": ["saveLevel"]}, "play-level": {"methods": ["playLevel"]}, "level-start": {"methods": ["levelStart"]}, "level-end": {"methods": ["levelEnd"]}}, "inspector": {"section": {"node": {"LevelEditorUI": "./dist/contributions/inspector/level-editor.js", "FormationEditor": "./dist/contributions/inspector/formation-editor.js", "PathEditor": "./dist/contributions/inspector/path-editor.js"}}}, "shortcuts": [{"message": "save-level", "win": "ctrl+t", "mac": "cmd+t"}, {"message": "play-level", "win": "space", "mac": "space"}, {"message": "level-start", "win": "ctrl+left", "mac": "cmd+left"}, {"message": "level-end", "win": "ctrl+right", "mac": "cmd+right"}], "scene": {"script": "./dist/contributions/scene/scene.js"}, "assets": {"menu": {"methods": "./dist/assets-menu.js", "createMenu": "onCreateMenu", "assetMenu": "onAssetMenu"}}}}