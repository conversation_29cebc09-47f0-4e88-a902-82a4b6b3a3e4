/**
 * 阵型编辑器
 */
'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const { updatePropByDump, disconnectGroup } = require('./../../prop');
exports.template = `
<div class="component-container"></div>
<ui-prop>
    <ui-button class="btn-add"">添加阵型点</ui-button>
    <ui-button class="btn-save">保存</ui-button>
</ui-prop>
`;
exports.$ = {
    componentContainer: '.component-container',
    btnAdd: '.btn-add',
    btnSave: '.btn-save',
};
function update(dump) {
    updatePropByDump(this, dump);
    this.dump = dump;
}
async function ready() {
    disconnectGroup(this);
    this.$.btnAdd.addEventListener('confirm', async () => {
        var _a;
        console.log('add formation point', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'addFormationPoint',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid.value]
        });
    });
    this.$.btnSave.addEventListener('confirm', async () => {
        var _a;
        console.log('save formation', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'saveFormationGroup',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid.value]
        });
    });
}
//# sourceMappingURL=data:application/json;base64,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