System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Graphics, Color, Vec2, EDITOR, PathData, PathPoint, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, executeInEditMode, menu, PathTestExample;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Graphics = _cc.Graphics;
      Color = _cc.Color;
      Vec2 = _cc.Vec2;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
      PathPoint = _unresolved_2.PathPoint;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "662ec/TD5tNPKb1wIW2lswA", "PathTestExample", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Graphics', 'Color', 'Vec2']);

      ({
        ccclass,
        property,
        executeInEditMode,
        menu
      } = _decorator);
      /**
       * 路径测试示例 - 验证Catmull-Rom曲线是否正确经过所有定义点
       */

      _export("PathTestExample", PathTestExample = (_dec = ccclass('PathTestExample'), _dec2 = menu("怪物/编辑器/路径测试示例"), _dec3 = executeInEditMode(true), _dec4 = property({
        displayName: "显示测试点"
      }), _dec5 = property({
        displayName: "显示曲线"
      }), _dec6 = property({
        displayName: "点大小"
      }), _dec(_class = _dec2(_class = _dec3(_class = (_class2 = class PathTestExample extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "showTestPoints", _descriptor, this);

          _initializerDefineProperty(this, "showCurve", _descriptor2, this);

          _initializerDefineProperty(this, "pointSize", _descriptor3, this);

          this._graphics = null;
          this._testPath = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
        }

        get graphics() {
          if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
          }

          return this._graphics;
        }

        onLoad() {
          this.createTestPath();
        }

        createTestPath() {
          // 创建一个简单的测试路径
          this._testPath.name = "测试路径";
          this._testPath.segments = 30;
          this._testPath.closed = false; // 添加几个测试点

          const testPoints = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-200, 0), // 起点
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 150), // 上弯
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 0), // 中点
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, -150), // 下弯
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(200, 0) // 终点
          ]; // 设置不同的平滑程度来测试效果

          testPoints[0].smoothness = 0.5;
          testPoints[1].smoothness = 0.8; // 高平滑度

          testPoints[2].smoothness = 0.2; // 低平滑度（更尖锐）

          testPoints[3].smoothness = 0.8; // 高平滑度

          testPoints[4].smoothness = 0.5;
          this._testPath.points = testPoints;
        }

        update(_dt) {
          if (EDITOR) {
            this.drawTest();
          }
        }

        drawTest() {
          const graphics = this.graphics;
          graphics.clear(); // 绘制原始定义点

          if (this.showTestPoints) {
            graphics.fillColor = Color.RED;
            graphics.strokeColor = Color.BLACK;
            graphics.lineWidth = 2;

            this._testPath.points.forEach((point, index) => {
              const pos = point.position;
              graphics.circle(pos.x, pos.y, this.pointSize);
              graphics.fill();
              graphics.stroke(); // 绘制点的索引
              // 注意：这里只是示意，实际文字绘制需要使用Label组件
            });
          } // 绘制Catmull-Rom曲线


          if (this.showCurve) {
            graphics.strokeColor = Color.WHITE;
            graphics.lineWidth = 3;

            const curvePoints = this._testPath.generateCurvePoints();

            if (curvePoints.length > 1) {
              graphics.moveTo(curvePoints[0].x, curvePoints[0].y);

              for (let i = 1; i < curvePoints.length; i++) {
                graphics.lineTo(curvePoints[i].x, curvePoints[i].y);
              }

              graphics.stroke();
            } // 用绿色标记曲线经过的原始定义点


            graphics.fillColor = Color.GREEN;

            this._testPath.points.forEach(point => {
              const pos = point.position;
              graphics.circle(pos.x, pos.y, this.pointSize / 2);
              graphics.fill();
            });
          } // 绘制平滑程度指示器


          graphics.strokeColor = Color.YELLOW;
          graphics.lineWidth = 1;

          this._testPath.points.forEach(point => {
            const pos = point.position;
            const radius = this.pointSize + point.smoothness * 20;
            graphics.circle(pos.x, pos.y, radius);
            graphics.stroke();
          });
        }
        /**
         * 验证曲线是否经过定义点
         */


        validateCurve() {
          const curvePoints = this._testPath.generateCurvePoints();

          const tolerance = 1.0; // 允许的误差范围

          let allPointsPassed = true;

          this._testPath.points.forEach((definedPoint, index) => {
            let foundNearbyPoint = false; // 检查曲线点中是否有足够接近定义点的点

            for (const curvePoint of curvePoints) {
              const distance = Vec2.distance(definedPoint.position, curvePoint);

              if (distance <= tolerance) {
                foundNearbyPoint = true;
                break;
              }
            }

            if (!foundNearbyPoint) {
              console.warn(`定义点 ${index} (${definedPoint.x}, ${definedPoint.y}) 没有被曲线经过`);
              allPointsPassed = false;
            }
          });

          if (allPointsPassed) {
            console.log("✅ 验证通过：曲线正确经过所有定义点");
          } else {
            console.log("❌ 验证失败：曲线没有经过某些定义点");
          }

          return allPointsPassed;
        }
        /**
         * 创建不同类型的测试路径
         */


        createDifferentTestPaths() {
          // 可以在这里添加更多测试用例
          this.createStraightLineTest();
          this.createSharpTurnTest();
          this.createSmoothCurveTest();
        }

        createStraightLineTest() {
          // 测试直线（所有点在一条线上）
          const points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0)];
          points.forEach(p => p.smoothness = 0.5);
          this._testPath.points = points;
          console.log("直线测试:", this.validateCurve());
        }

        createSharpTurnTest() {
          // 测试尖锐转角
          const points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 100), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0)];
          points.forEach(p => p.smoothness = 0.1); // 低平滑度

          this._testPath.points = points;
          console.log("尖锐转角测试:", this.validateCurve());
        }

        createSmoothCurveTest() {
          // 测试平滑曲线
          const points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 100), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0)];
          points.forEach(p => p.smoothness = 0.9); // 高平滑度

          this._testPath.points = points;
          console.log("平滑曲线测试:", this.validateCurve());
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "showTestPoints", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "showCurve", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "pointSize", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 8;
        }
      })), _class2)) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=5b06060396525943ecb6e7287e41be89e2834187.js.map