{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/FormationPointEditor.ts"], "names": ["_decorator", "Component", "Vec3", "Graphics", "FormationPoint", "SpawnGroup", "ccclass", "playOnFocus", "executeInEditMode", "property", "disallowMultiple", "menu", "requireComponent", "FormationPointEditor", "type", "displayName", "_graphics", "_cachedIndex", "graphics", "node", "getComponent", "addComponent", "formationPoint", "point", "spawnGroup", "x", "position", "y", "value", "update", "dt", "clear", "lineWidth", "circle", "stroke", "siblingIndex", "getSiblingIndex", "name"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAgCC,MAAAA,S,OAAAA,S;AAA4BC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;;AAGlDC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,U,iBAAAA,U;;;;;;;;;OAFnC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,WAAX;AAAwBC,QAAAA,iBAAxB;AAA2CC,QAAAA,QAA3C;AAAqDC,QAAAA,gBAArD;AAAuEC,QAAAA,IAAvE;AAA6EC,QAAAA;AAA7E,O,GAAmGZ,U;;sCAS5Fa,oB,WALZP,OAAO,CAAC,sBAAD,C,UACPK,IAAI,CAAC,YAAD,C,UACJC,gBAAgB,CAACT,QAAD,C,UAChBK,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAWZD,QAAQ,CAAC;AAACK,QAAAA,IAAI,EAAE;AAAA;AAAA,qCAAP;AAAqBC,QAAAA,WAAW,EAAE;AAAlC,OAAD,C,uFAfb,MAKaF,oBALb,SAK0CZ,SAL1C,CAKoD;AAAA;AAAA;AAAA,eACxCe,SADwC,GACb,IADa;;AAAA;;AAAA,eAaxCC,YAbwC,GAajB,CAAC,CAbgB;AAAA;;AAE7B,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKF,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKG,IAAL,CAAUC,YAAV,CAAuBjB,QAAvB,KAAoC,KAAKgB,IAAL,CAAUE,YAAV,CAAuBlB,QAAvB,CAArD;AACH;;AAED,iBAAO,KAAKa,SAAZ;AACH;;AAOwB,YAAdM,cAAc,GAAmB;AACxC,cAAIC,KAAK,GAAG;AAAA;AAAA,iDAAZ;AACAA,UAAAA,KAAK,CAACC,UAAN,GAAmB,KAAKA,UAAxB;AACAD,UAAAA,KAAK,CAACE,CAAN,GAAU,KAAKN,IAAL,CAAUO,QAAV,CAAmBD,CAA7B;AACAF,UAAAA,KAAK,CAACI,CAAN,GAAU,KAAKR,IAAL,CAAUO,QAAV,CAAmBC,CAA7B;AACA,iBAAOJ,KAAP;AACH;;AAEwB,YAAdD,cAAc,CAACM,KAAD,EAAwB;AAC7C,eAAKJ,UAAL,GAAkBI,KAAK,CAACJ,UAAxB;AACA,eAAKL,IAAL,CAAUO,QAAV,GAAqB,IAAIxB,IAAJ,CAAS0B,KAAK,CAACH,CAAf,EAAkBG,KAAK,CAACD,CAAxB,EAA2B,CAA3B,CAArB;AACH;;AAEME,QAAAA,MAAM,CAACC,EAAD,EAAa;AACtB,cAAMZ,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACa,KAAT;AAEAb,UAAAA,QAAQ,CAACc,SAAT,GAAqB,EAArB;AACAd,UAAAA,QAAQ,CAACe,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsB,EAAtB;AACAf,UAAAA,QAAQ,CAACgB,MAAT;AAEA,cAAMC,YAAY,GAAG,KAAKhB,IAAL,CAAUiB,eAAV,EAArB;;AACA,cAAID,YAAY,KAAK,KAAKlB,YAA1B,EAAwC;AACpC,iBAAKA,YAAL,GAAoBkB,YAApB;AACA,iBAAKhB,IAAL,CAAUkB,IAAV,cAA0BF,YAA1B;AACH;AACJ;;AAzC+C,O;;;;;iBAWd,E", "sourcesContent": ["import { _decorator, instantiate, Color, Component, JsonAsset, Rect, Vec3, Graphics, assetManager } from 'cc';\r\nconst { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, requireComponent  } = _decorator;\r\nimport { EDITOR } from 'cc/env';\r\nimport { FormationGroup, FormationPoint, SpawnGroup } from 'db://assets/bundles/common/script/game/data/WaveData';\r\n\r\n@ccclass('FormationPointEditor')\r\n@menu(\"怪物/编辑器/阵型点\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class FormationPointEditor extends Component {\r\n    private _graphics: Graphics|null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n\r\n        return this._graphics;\r\n    }\r\n\r\n    @property({type: [SpawnGroup], displayName: \"出生组\"})\r\n    public spawnGroup: SpawnGroup[] = [];\r\n\r\n    private _cachedIndex: number = -1;\r\n\r\n    public get formationPoint(): FormationPoint {\r\n        let point = new FormationPoint();\r\n        point.spawnGroup = this.spawnGroup;\r\n        point.x = this.node.position.x;\r\n        point.y = this.node.position.y;\r\n        return point;\r\n    }\r\n\r\n    public set formationPoint(value: FormationPoint) {\r\n        this.spawnGroup = value.spawnGroup;\r\n        this.node.position = new Vec3(value.x, value.y, 0);\r\n    }\r\n\r\n    public update(dt: number) {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n        \r\n        graphics.lineWidth = 10;\r\n        graphics.circle(0, 0, 10);\r\n        graphics.stroke();\r\n\r\n        const siblingIndex = this.node.getSiblingIndex();\r\n        if (siblingIndex !== this._cachedIndex) {\r\n            this._cachedIndex = siblingIndex;\r\n            this.node.name = `Point_${siblingIndex}`;\r\n        }\r\n    }\r\n}"]}