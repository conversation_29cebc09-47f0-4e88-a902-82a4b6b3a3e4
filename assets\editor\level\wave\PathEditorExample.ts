import { _decorator, Component, Node, Vec3, instantiate, Prefab, JsonAsset } from 'cc';
import { EDITOR } from 'cc/env';
import { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';
import { PathEditor } from './PathEditor';
import { PathFollower } from './PathFollower';

const { ccclass, property, executeInEditMode, menu } = _decorator;

/**
 * 路径编辑器使用示例
 * 展示如何创建和使用路径编辑系统
 */
@ccclass('PathEditorExample')
@menu("怪物/编辑器/路径编辑示例")
@executeInEditMode(true)
export class PathEditorExample extends Component {
    @property({ type: Prefab, displayName: "测试对象预制体" })
    public testObjectPrefab: Prefab | null = null;

    @property({ type: JsonAsset, displayName: "示例路径数据" })
    public examplePathData: JsonAsset | null = null;

    private _pathEditor: PathEditor | null = null;
    private _testObject: Node | null = null;

    protected onLoad() {
        if (EDITOR) {
            this.setupExample();
        }
    }

    private setupExample() {
        // 创建路径编辑器节点
        this.createPathEditor();
        
        // 创建示例路径
        this.createExamplePath();
        
        // 创建测试对象
        if (this.testObjectPrefab) {
            this.createTestObject();
        }
    }

    private createPathEditor() {
        // 查找或创建路径编辑器节点
        let pathEditorNode = this.node.getChildByName('PathEditor');
        if (!pathEditorNode) {
            pathEditorNode = new Node('PathEditor');
            pathEditorNode.parent = this.node;
        }

        // 添加PathEditor组件
        this._pathEditor = pathEditorNode.getComponent(PathEditor);
        if (!this._pathEditor) {
            this._pathEditor = pathEditorNode.addComponent(PathEditor);
        }

        // 如果有示例路径数据，加载它
        if (this.examplePathData) {
            this._pathEditor.pathData = this.examplePathData;
        }
    }

    private createExamplePath() {
        if (!this._pathEditor || this.examplePathData) return;

        // 创建一个示例路径（S形曲线）
        const pathData = new PathData();
        pathData.name = "示例S形路径";
        pathData.segments = 20;
        pathData.closed = false;

        // 添加路径点
        const points = [
            new PathPoint(-300, 200, 0),   // 起点
            new PathPoint(-100, 100, 0),   // 控制点1
            new PathPoint(100, -100, 0),   // 控制点2
            new PathPoint(300, -200, 0),   // 终点
        ];

        // 设置不同的平滑程度
        points[0].smoothness = 0.3;
        points[1].smoothness = 0.8;
        points[2].smoothness = 0.8;
        points[3].smoothness = 0.3;

        // 设置速度
        points[0].speed = 200;
        points[1].speed = 400;
        points[2].speed = 400;
        points[3].speed = 200;

        pathData.points = points;

        // 添加点到编辑器
        points.forEach(point => {
            this._pathEditor!.addPoint(point);
        });

        this._pathEditor.updateCurve();
    }

    private createTestObject() {
        if (!this.testObjectPrefab || !this._pathEditor) return;

        // 创建测试对象
        this._testObject = instantiate(this.testObjectPrefab);
        this._testObject.parent = this.node;
        this._testObject.name = "TestObject";

        // 添加路径跟随器
        const pathFollower = this._testObject.addComponent(PathFollower);
        
        // 如果有路径数据，设置给跟随器
        if (this._pathEditor.pathData) {
            pathFollower.pathAsset = this._pathEditor.pathData;
        }

        // 配置跟随器
        pathFollower.moveSpeed = 100;
        pathFollower.autoMove = true;
        pathFollower.loop = true;
        pathFollower.autoFacing = true;
    }

    /**
     * 创建预设路径模板
     */
    public createPathTemplates() {
        if (!this._pathEditor) return;

        // 清空现有路径
        this._pathEditor.node.removeAllChildren();

        // 创建不同类型的路径模板
        this.createStraightPath();
        // this.createCircularPath();
        // this.createZigzagPath();
    }

    private createStraightPath() {
        const points = [
            new PathPoint(-200, 0, 0),
            new PathPoint(200, 0, 0),
        ];

        points.forEach(point => {
            point.smoothness = 0.5;
            point.speed = 300;
            this._pathEditor!.addPoint(point);
        });

        this._pathEditor!.updateCurve();
    }

    private createCircularPath() {
        const radius = 150;
        const pointCount = 8;
        
        for (let i = 0; i < pointCount; i++) {
            const angle = (i / pointCount) * Math.PI * 2;
            const x = Math.cos(angle) * radius;
            const y = Math.sin(angle) * radius;
            
            const point = new PathPoint(x, y, 0);
            point.smoothness = 0.8; // 高平滑度创建圆形
            point.speed = 250;
            
            this._pathEditor!.addPoint(point);
        }

        // 设置为闭合路径
        if (this._pathEditor) {
            // 这里需要访问PathEditor的内部数据，可能需要添加公共方法
        }

        this._pathEditor!.updateCurve();
    }

    private createZigzagPath() {
        const points = [
            new PathPoint(-200, 0, 0),
            new PathPoint(-100, 100, 0),
            new PathPoint(0, -100, 0),
            new PathPoint(100, 100, 0),
            new PathPoint(200, 0, 0),
        ];

        points.forEach((point, index) => {
            point.smoothness = index % 2 === 0 ? 0.2 : 0.6; // 交替平滑度
            point.speed = 200 + index * 50;
            this._pathEditor!.addPoint(point);
        });

        this._pathEditor!.updateCurve();
    }

    /**
     * 测试路径跟随
     */
    public testPathFollowing() {
        if (!this._testObject) return;

        const pathFollower = this._testObject.getComponent(PathFollower);
        if (pathFollower) {
            pathFollower.resetToStart();
            pathFollower.startAutoMove();
        }
    }

    /**
     * 停止路径跟随
     */
    public stopPathFollowing() {
        if (!this._testObject) return;

        const pathFollower = this._testObject.getComponent(PathFollower);
        if (pathFollower) {
            pathFollower.stopAutoMove();
        }
    }
}
