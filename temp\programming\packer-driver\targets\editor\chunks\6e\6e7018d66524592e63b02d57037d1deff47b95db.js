System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, instantiate, Prefab, JsonAsset, EDITOR, PathData, PathPoint, PathEditor, PathFollower, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, executeInEditMode, menu, PathEditorExample;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathEditor(extras) {
    _reporterNs.report("PathEditor", "./PathEditor", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathFollower(extras) {
    _reporterNs.report("PathFollower", "./PathFollower", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      instantiate = _cc.instantiate;
      Prefab = _cc.Prefab;
      JsonAsset = _cc.JsonAsset;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
      PathPoint = _unresolved_2.PathPoint;
    }, function (_unresolved_3) {
      PathEditor = _unresolved_3.PathEditor;
    }, function (_unresolved_4) {
      PathFollower = _unresolved_4.PathFollower;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "411cfN1FxJJGLFrn5QL9Gr5", "PathEditorExample", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Vec3', 'instantiate', 'Prefab', 'JsonAsset']);

      ({
        ccclass,
        property,
        executeInEditMode,
        menu
      } = _decorator);
      /**
       * 路径编辑器使用示例
       * 展示如何创建和使用路径编辑系统
       */

      _export("PathEditorExample", PathEditorExample = (_dec = ccclass('PathEditorExample'), _dec2 = menu("怪物/编辑器/路径编辑示例"), _dec3 = executeInEditMode(true), _dec4 = property({
        type: Prefab,
        displayName: "测试对象预制体"
      }), _dec5 = property({
        type: JsonAsset,
        displayName: "示例路径数据"
      }), _dec(_class = _dec2(_class = _dec3(_class = (_class2 = class PathEditorExample extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "testObjectPrefab", _descriptor, this);

          _initializerDefineProperty(this, "examplePathData", _descriptor2, this);

          this._pathEditor = null;
          this._testObject = null;
        }

        onLoad() {
          if (EDITOR) {
            this.setupExample();
          }
        }

        setupExample() {
          // 创建路径编辑器节点
          this.createPathEditor(); // 创建示例路径

          this.createExamplePath(); // 创建测试对象

          if (this.testObjectPrefab) {
            this.createTestObject();
          }
        }

        createPathEditor() {
          // 查找或创建路径编辑器节点
          let pathEditorNode = this.node.getChildByName('PathEditor');

          if (!pathEditorNode) {
            pathEditorNode = new Node('PathEditor');
            pathEditorNode.parent = this.node;
          } // 添加PathEditor组件


          this._pathEditor = pathEditorNode.getComponent(_crd && PathEditor === void 0 ? (_reportPossibleCrUseOfPathEditor({
            error: Error()
          }), PathEditor) : PathEditor);

          if (!this._pathEditor) {
            this._pathEditor = pathEditorNode.addComponent(_crd && PathEditor === void 0 ? (_reportPossibleCrUseOfPathEditor({
              error: Error()
            }), PathEditor) : PathEditor);
          } // 如果有示例路径数据，加载它


          if (this.examplePathData) {
            this._pathEditor.pathData = this.examplePathData;
          }
        }

        createExamplePath() {
          if (!this._pathEditor || this.examplePathData) return; // 创建一个示例路径（S形曲线）

          const pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          pathData.name = "示例S形路径";
          pathData.segments = 20;
          pathData.closed = false; // 添加路径点

          const points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-300, 200, 0), // 起点
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 100, 0), // 控制点1
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, -100, 0), // 控制点2
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(300, -200, 0) // 终点
          ]; // 设置不同的平滑程度

          points[0].smoothness = 0.3;
          points[1].smoothness = 0.8;
          points[2].smoothness = 0.8;
          points[3].smoothness = 0.3; // 设置速度

          points[0].speed = 200;
          points[1].speed = 400;
          points[2].speed = 400;
          points[3].speed = 200;
          pathData.points = points; // 添加点到编辑器

          points.forEach(point => {
            this._pathEditor.addPoint(point);
          });

          this._pathEditor.updateCurve();
        }

        createTestObject() {
          if (!this.testObjectPrefab || !this._pathEditor) return; // 创建测试对象

          this._testObject = instantiate(this.testObjectPrefab);
          this._testObject.parent = this.node;
          this._testObject.name = "TestObject"; // 添加路径跟随器

          const pathFollower = this._testObject.addComponent(_crd && PathFollower === void 0 ? (_reportPossibleCrUseOfPathFollower({
            error: Error()
          }), PathFollower) : PathFollower); // 如果有路径数据，设置给跟随器


          if (this._pathEditor.pathData) {
            pathFollower.pathAsset = this._pathEditor.pathData;
          } // 配置跟随器


          pathFollower.moveSpeed = 100;
          pathFollower.autoMove = true;
          pathFollower.loop = true;
          pathFollower.autoFacing = true;
        }
        /**
         * 创建预设路径模板
         */


        createPathTemplates() {
          if (!this._pathEditor) return; // 清空现有路径

          this._pathEditor.node.removeAllChildren(); // 创建不同类型的路径模板


          this.createStraightPath(); // this.createCircularPath();
          // this.createZigzagPath();
        }

        createStraightPath() {
          const points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-200, 0, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(200, 0, 0)];
          points.forEach(point => {
            point.smoothness = 0.5;
            point.speed = 300;

            this._pathEditor.addPoint(point);
          });

          this._pathEditor.updateCurve();
        }

        createCircularPath() {
          const radius = 150;
          const pointCount = 8;

          for (let i = 0; i < pointCount; i++) {
            const angle = i / pointCount * Math.PI * 2;
            const x = Math.cos(angle) * radius;
            const y = Math.sin(angle) * radius;
            const point = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
              error: Error()
            }), PathPoint) : PathPoint)(x, y, 0);
            point.smoothness = 0.8; // 高平滑度创建圆形

            point.speed = 250;

            this._pathEditor.addPoint(point);
          } // 设置为闭合路径


          if (this._pathEditor) {// 这里需要访问PathEditor的内部数据，可能需要添加公共方法
          }

          this._pathEditor.updateCurve();
        }

        createZigzagPath() {
          const points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-200, 0, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, -100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(200, 0, 0)];
          points.forEach((point, index) => {
            point.smoothness = index % 2 === 0 ? 0.2 : 0.6; // 交替平滑度

            point.speed = 200 + index * 50;

            this._pathEditor.addPoint(point);
          });

          this._pathEditor.updateCurve();
        }
        /**
         * 测试路径跟随
         */


        testPathFollowing() {
          if (!this._testObject) return;

          const pathFollower = this._testObject.getComponent(_crd && PathFollower === void 0 ? (_reportPossibleCrUseOfPathFollower({
            error: Error()
          }), PathFollower) : PathFollower);

          if (pathFollower) {
            pathFollower.resetToStart();
            pathFollower.startAutoMove();
          }
        }
        /**
         * 停止路径跟随
         */


        stopPathFollowing() {
          if (!this._testObject) return;

          const pathFollower = this._testObject.getComponent(_crd && PathFollower === void 0 ? (_reportPossibleCrUseOfPathFollower({
            error: Error()
          }), PathFollower) : PathFollower);

          if (pathFollower) {
            pathFollower.stopAutoMove();
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "testObjectPrefab", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "examplePathData", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6e7018d66524592e63b02d57037d1deff47b95db.js.map