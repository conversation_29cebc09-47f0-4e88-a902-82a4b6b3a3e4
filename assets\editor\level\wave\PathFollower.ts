import { _decorator, Component, Vec2, CCFloat, JsonAsset } from 'cc';
import { PathData } from 'db://assets/bundles/common/script/game/data/PathData';

const { ccclass, property } = _decorator;

/**
 * 路径跟随器 - 运行时使用路径数据让对象沿路径移动
 */
@ccclass('PathFollower')
export class PathFollower extends Component {
    @property({ type: JsonAsset, displayName: "路径数据" })
    public pathAsset: JsonAsset | null = null;

    @property({ type: CCFloat, displayName: "移动速度", tooltip: "沿路径移动的速度" })
    public moveSpeed: number = 100;

    @property({ type: CCFloat, displayName: "当前进度", range: [0, 1], slide: true, tooltip: "当前在路径上的位置 [0-1]" })
    public progress: number = 0;

    @property({ displayName: "自动移动" })
    public autoMove: boolean = true;

    @property({ displayName: "循环移动" })
    public loop: boolean = false;

    @property({ displayName: "自动朝向" })
    public autoFacing: boolean = true;

    private _pathData: PathData | null = null;
    private _curvePoints: Vec2[] = [];
    private _totalDistance: number = 0;
    private _distances: number[] = [];

    protected onLoad() {
        this.loadPathData();
    }

    protected update(dt: number) {
        if (this.autoMove && this._curvePoints.length > 1) {
            this.moveAlongPath(dt);
        }
    }

    private loadPathData() {
        if (!this.pathAsset) return;

        this._pathData = new PathData();
        Object.assign(this._pathData, this.pathAsset.json);
        
        this.generateCurveData();
    }

    private generateCurveData() {
        if (!this._pathData || this._pathData.points.length < 2) return;

        // 生成曲线点
        this._curvePoints = this._pathData.generateCurvePoints();
        
        // 计算距离信息
        this.calculateDistances();
    }

    private calculateDistances() {
        this._distances = [0];
        this._totalDistance = 0;

        for (let i = 1; i < this._curvePoints.length; i++) {
            const distance = Vec2.distance(this._curvePoints[i - 1], this._curvePoints[i]);
            this._totalDistance += distance;
            this._distances.push(this._totalDistance);
        }
    }

    private moveAlongPath(dt: number) {
        if (this._totalDistance === 0) return;

        // 根据速度更新进度
        const deltaProgress = (this.moveSpeed * dt) / this._totalDistance;
        this.progress += deltaProgress;

        if (this.progress >= 1) {
            if (this.loop) {
                this.progress = this.progress - 1;
            } else {
                this.progress = 1;
                this.autoMove = false;
            }
        }

        this.updatePosition();
    }

    private updatePosition() {
        if (this._curvePoints.length < 2) return;

        const targetDistance = this.progress * this._totalDistance;
        const position = this.getPositionAtDistance(targetDistance);

        this.node.setPosition(position.x, position.y, 0);

        // 自动朝向
        if (this.autoFacing) {
            const direction = this.getDirectionAtDistance(targetDistance);
            if (direction.lengthSqr() > 0.001) {
                const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
                this.node.angle = -angle; // Cocos Creator的角度系统
            }
        }
    }

    private getPositionAtDistance(distance: number): Vec2 {
        if (distance <= 0) return this._curvePoints[0].clone();
        if (distance >= this._totalDistance) return this._curvePoints[this._curvePoints.length - 1].clone();

        // 找到距离对应的线段
        for (let i = 1; i < this._distances.length; i++) {
            if (distance <= this._distances[i]) {
                const segmentStart = this._distances[i - 1];
                const segmentEnd = this._distances[i];
                const segmentLength = segmentEnd - segmentStart;

                if (segmentLength === 0) return this._curvePoints[i - 1].clone();

                const t = (distance - segmentStart) / segmentLength;
                return Vec2.lerp(new Vec2(), this._curvePoints[i - 1], this._curvePoints[i], t);
            }
        }

        return this._curvePoints[this._curvePoints.length - 1].clone();
    }

    private getDirectionAtDistance(distance: number): Vec2 {
        const epsilon = 1; // 小的距离偏移用于计算方向
        const pos1 = this.getPositionAtDistance(distance);
        const pos2 = this.getPositionAtDistance(distance + epsilon);

        return Vec2.subtract(new Vec2(), pos2, pos1).normalize();
    }

    /**
     * 设置路径进度
     * @param progress 进度值 [0-1]
     */
    public setProgress(progress: number) {
        this.progress = Math.max(0, Math.min(1, progress));
        this.updatePosition();
    }

    /**
     * 重置到路径起点
     */
    public resetToStart() {
        this.setProgress(0);
    }

    /**
     * 移动到路径终点
     */
    public moveToEnd() {
        this.setProgress(1);
    }

    /**
     * 开始自动移动
     */
    public startAutoMove() {
        this.autoMove = true;
    }

    /**
     * 停止自动移动
     */
    public stopAutoMove() {
        this.autoMove = false;
    }

    /**
     * 获取当前位置对应的路径点属性（速度、角度等）
     */
    public getCurrentPathPointData() {
        if (!this._pathData || this._pathData.points.length === 0) return null;

        const pointIndex = Math.floor(this.progress * (this._pathData.points.length - 1));
        const clampedIndex = Math.max(0, Math.min(this._pathData.points.length - 1, pointIndex));
        
        return this._pathData.points[clampedIndex];
    }
}
