{"__type__": "cc.TextAsset", "_name": "README_OrientationArrow", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "text": "# 路径编辑器箭头系统\n\n## 概述\n路径编辑器现在包含两种箭头系统：\n1. **路径点朝向箭头** (PathPointEditor): 显示每个点上飞机的朝向\n2. **路径方向箭头** (PathEditor): 显示整个路径的行进方向\n\n## 路径方向箭头 (PathEditor)\n\n### 功能说明\n- **位置**: 绘制在路径的终点处\n- **颜色**: 红色填充箭头\n- **用途**: 指示路径的整体行进方向\n- **控制**: 通过\"显示方向箭头\"属性开启/关闭\n\n### 行为特点\n- **开放路径**: 在终点显示红色方向箭头\n- **闭合路径**: 不显示箭头（因为闭合路径没有明确的终点）\n- **方向计算**: 使用路径终点前的多个点计算平滑方向\n- **样式**: 填充的三角形箭头头部，更加醒目\n\n## 路径点朝向箭头 (PathPointEditor)\n\n### 功能说明\nPathPointEditor现在支持根据朝向类型绘制不同的箭头，用于可视化飞机在该点的朝向。箭头长度表示速度，箭头方向表示朝向。\n\n## 朝向类型说明\n\n### 1. FacingMoveDir (跟随移动方向)\n- **颜色**: 蓝色\n- **行为**: 箭头指向飞机的移动方向\n- **计算方式**:\n  - 起点：指向下一个路径点\n  - 终点：延续从上一个点的方向\n  - 中间点：使用前后两点的平均方向\n- **朝向参数**: 暂未使用\n\n### 2. FacingPlayer (朝向玩家)\n- **颜色**: 紫色 (Magenta)\n- **行为**: 箭头始终指向玩家位置\n- **计算方式**: 从当前点指向玩家位置 (默认屏幕底部中央 0, -400)\n- **朝向参数**: 暂未使用\n\n### 3. Fixed (固定朝向)\n- **颜色**: 橙色\n- **行为**: 箭头指向固定的角度\n- **计算方式**: 使用朝向参数作为角度值\n- **朝向参数**: 角度值 (0-360度)\n  - 0度 = 向右\n  - 90度 = 向上\n  - 180度 = 向左\n  - 270度 = 向下\n\n## 可视化元素\n\n### 箭头组成\n- **主线**: 从点中心指向朝向方向\n- **箭头头部**: 30度角的箭头尖端\n- **长度**: 根据速度值计算 (速度/10，最大100像素)\n\n### 颜色编码\n- 🔵 **蓝色**: 跟随移动方向\n- 🟣 **紫色**: 朝向玩家\n- 🟠 **橙色**: 固定朝向\n\n## 使用方法\n\n### 在编辑器中\n1. 选择路径点\n2. 在Inspector中设置：\n   - **朝向类型**: 选择所需的朝向行为\n   - **朝向参数**: 仅在固定朝向时需要设置角度\n   - **速度**: 影响箭头长度\n\n### 实时预览\n- 箭头会实时更新显示当前设置的朝向\n- 移动路径点时，跟随移动方向的箭头会自动调整\n- 不同朝向类型用不同颜色区分\n\n## 代码结构\n\n### 主要方法\n- `drawOrientationArrow()`: 绘制朝向箭头\n- `calculateArrowAngle()`: 根据朝向类型计算角度\n- `calculateMovementDirection()`: 计算移动方向\n- `calculatePlayerDirection()`: 计算朝向玩家的方向\n- `getArrowColorByOrientationType()`: 获取箭头颜色\n\n### 角度计算\n- 使用弧度制进行计算\n- 0弧度 = 向右 (东)\n- π/2弧度 = 向上 (北)\n- π弧度 = 向左 (西)\n- 3π/2弧度 = 向下 (南)\n\n## 扩展性\n\n### 未来可能的增强\n1. **朝向参数扩展**:\n   - 跟随移动方向时的角度偏移\n   - 朝向玩家时的预测偏移\n\n2. **新朝向类型**:\n   - 朝向特定目标点\n   - 随机朝向\n   - 基于时间的旋转朝向\n\n3. **可视化增强**:\n   - 箭头样式自定义\n   - 动画效果\n   - 更多颜色选项\n\n## 注意事项\n\n1. **性能**: 箭头在每帧更新时重新绘制，适合编辑器使用\n2. **坐标系**: 使用Cocos Creator的坐标系统 (Y轴向上)\n3. **角度单位**: 用户输入使用度数，内部计算使用弧度\n4. **玩家位置**: 目前使用固定位置，实际游戏中应从游戏状态获取\n\n## 调试技巧\n\n1. **验证朝向**: 通过箭头颜色快速识别朝向类型\n2. **检查角度**: 固定朝向时，箭头应指向设定的角度\n3. **移动测试**: 移动路径点时观察跟随移动方向的箭头变化\n4. **速度测试**: 调整速度值观察箭头长度变化\n"}