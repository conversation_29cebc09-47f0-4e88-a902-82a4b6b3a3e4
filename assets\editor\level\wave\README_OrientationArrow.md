# 路径编辑器箭头系统

## 概述
路径编辑器现在包含两种箭头系统：
1. **路径点朝向箭头** (PathPointEditor): 显示每个点上飞机的朝向
2. **路径方向箭头** (PathEditor): 显示整个路径的行进方向

## 路径方向箭头 (PathEditor)

### 功能说明
- **位置**: 绘制在路径的终点处
- **颜色**: 红色填充箭头
- **用途**: 指示路径的整体行进方向
- **控制**: 通过"显示方向箭头"属性开启/关闭

### 行为特点
- **开放路径**: 在终点显示红色方向箭头
- **闭合路径**: 不显示箭头（因为闭合路径没有明确的终点）
- **方向计算**: 使用路径终点前的多个点计算平滑方向
- **样式**: 填充的三角形箭头头部，更加醒目

## 路径点朝向箭头 (PathPointEditor)

### 功能说明
PathPointEditor现在支持根据朝向类型绘制不同的箭头，用于可视化飞机在该点的朝向。箭头长度表示速度，箭头方向表示朝向。

## 朝向类型说明

### 1. FacingMoveDir (跟随移动方向)
- **颜色**: 蓝色
- **行为**: 箭头指向飞机的移动方向
- **计算方式**:
  - 起点：指向下一个路径点
  - 终点：延续从上一个点的方向
  - 中间点：使用前后两点的平均方向
- **朝向参数**: 暂未使用

### 2. FacingPlayer (朝向玩家)
- **颜色**: 紫色 (Magenta)
- **行为**: 箭头始终指向玩家位置
- **计算方式**: 从当前点指向玩家位置 (默认屏幕底部中央 0, -400)
- **朝向参数**: 暂未使用

### 3. Fixed (固定朝向)
- **颜色**: 橙色
- **行为**: 箭头指向固定的角度
- **计算方式**: 使用朝向参数作为角度值
- **朝向参数**: 角度值 (0-360度)
  - 0度 = 向右
  - 90度 = 向上
  - 180度 = 向左
  - 270度 = 向下

## 可视化元素

### 箭头组成
- **主线**: 从点中心指向朝向方向
- **箭头头部**: 30度角的箭头尖端
- **长度**: 根据速度值计算 (速度/10，最大100像素)

### 颜色编码
- 🔵 **蓝色**: 跟随移动方向
- 🟣 **紫色**: 朝向玩家
- 🟠 **橙色**: 固定朝向

## 使用方法

### 在编辑器中
1. 选择路径点
2. 在Inspector中设置：
   - **朝向类型**: 选择所需的朝向行为
   - **朝向参数**: 仅在固定朝向时需要设置角度
   - **速度**: 影响箭头长度

### 实时预览
- 箭头会实时更新显示当前设置的朝向
- 移动路径点时，跟随移动方向的箭头会自动调整
- 不同朝向类型用不同颜色区分

## 代码结构

### 主要方法
- `drawOrientationArrow()`: 绘制朝向箭头
- `calculateArrowAngle()`: 根据朝向类型计算角度
- `calculateMovementDirection()`: 计算移动方向
- `calculatePlayerDirection()`: 计算朝向玩家的方向
- `getArrowColorByOrientationType()`: 获取箭头颜色

### 角度计算
- 使用弧度制进行计算
- 0弧度 = 向右 (东)
- π/2弧度 = 向上 (北)
- π弧度 = 向左 (西)
- 3π/2弧度 = 向下 (南)

## 扩展性

### 未来可能的增强
1. **朝向参数扩展**:
   - 跟随移动方向时的角度偏移
   - 朝向玩家时的预测偏移

2. **新朝向类型**:
   - 朝向特定目标点
   - 随机朝向
   - 基于时间的旋转朝向

3. **可视化增强**:
   - 箭头样式自定义
   - 动画效果
   - 更多颜色选项

## 注意事项

1. **性能**: 箭头在每帧更新时重新绘制，适合编辑器使用
2. **坐标系**: 使用Cocos Creator的坐标系统 (Y轴向上)
3. **角度单位**: 用户输入使用度数，内部计算使用弧度
4. **玩家位置**: 目前使用固定位置，实际游戏中应从游戏状态获取

## 调试技巧

1. **验证朝向**: 通过箭头颜色快速识别朝向类型
2. **检查角度**: 固定朝向时，箭头应指向设定的角度
3. **移动测试**: 移动路径点时观察跟随移动方向的箭头变化
4. **速度测试**: 调整速度值观察箭头长度变化
