{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/PathData.ts"], "names": ["_decorator", "Vec2", "CCFloat", "CCInteger", "Enum", "eOrientationType", "ccclass", "property", "PathPoint", "type", "displayName", "range", "slide", "tooltip", "constructor", "x", "y", "position", "value", "PathData", "editor<PERSON><PERSON><PERSON>", "catmullRomPoint", "t", "p0", "p1", "p2", "p3", "smoothness", "lerp", "t2", "t3", "catmullRom", "linear", "generateCurvePoints", "points", "length", "map", "p", "curvePoints", "pointCount", "push", "i", "getControlPoint", "startSmoothness", "endSmoothness", "j", "segments", "point", "Math", "min", "index", "closed", "wrappedIndex", "subtract", "add"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACtCC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;AAE9B;AACA;AACA;;2BAEaQ,S,WADZF,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE,MAA9B;AAAsCC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAA7C;AAAqDC,QAAAA,KAAK,EAAE,IAA5D;AAAkEC,QAAAA,OAAO,EAAE;AAA3E,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,IAAhC;AAAsCG,QAAAA,OAAO,EAAE;AAA/C,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEL,IAAI;AAAA;AAAA,iDAAZ;AAAgCM,QAAAA,WAAW,EAAE,MAA7C;AAAqDG,QAAAA,OAAO,EAAE;AAA9D,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,MAAhC;AAAwCG,QAAAA,OAAO,EAAE;AAAjD,OAAD,C,2BAjBb,MACaL,SADb,CACuB;AAmBnBM,QAAAA,WAAW,CAACC,CAAD,EAAgBC,CAAhB,EAA+B;AAAA,cAA9BD,CAA8B;AAA9BA,YAAAA,CAA8B,GAAlB,CAAkB;AAAA;;AAAA,cAAfC,CAAe;AAAfA,YAAAA,CAAe,GAAH,CAAG;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AACtC,eAAKD,CAAL,GAASA,CAAT;AACA,eAAKC,CAAL,GAASA,CAAT;AACH;;AAEkB,YAARC,QAAQ,GAAS;AACxB,iBAAO,IAAIhB,IAAJ,CAAS,KAAKc,CAAd,EAAiB,KAAKC,CAAtB,CAAP;AACH;;AAEkB,YAARC,QAAQ,CAACC,KAAD,EAAc;AAC7B,eAAKH,CAAL,GAASG,KAAK,CAACH,CAAf;AACA,eAAKC,CAAL,GAASE,KAAK,CAACF,CAAf;AACH;;AA/BkB,O;;;;;iBAEA,C;;;;;;;iBAGA,C;;;;;;;iBAGS,C;;;;;;;iBAGL,G;;;;;;;iBAGoB,C;;;;;;;iBAGT,C;;;AAiBtC;AACA;AACA;;;0BAEaG,Q,YADZb,OAAO,CAAC,UAAD,C,UAEHC,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,MAAf;AAAuBU,QAAAA,UAAU,EAAE;AAAnC,OAAD,C,WAGRb,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAE,CAACD,SAAD,CAAR;AAAqBE,QAAAA,WAAW,EAAE;AAAlC,OAAD,C,WAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,OAAhC;AAAyCG,QAAAA,OAAO,EAAE;AAAlD,OAAD,C,WAGRN,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,QAAf;AAAyBG,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,6BAXb,MACaM,QADb,CACsB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAalB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACiC,eAAfE,eAAe,CAACC,CAAD,EAAYC,EAAZ,EAAsBC,EAAtB,EAAgCC,EAAhC,EAA0CC,EAA1C,EAAoDC,UAApD,EAAoF;AAAA,cAAhCA,UAAgC;AAAhCA,YAAAA,UAAgC,GAAX,GAAW;AAAA;;AAC7G;AACA,cAAIA,UAAU,KAAK,CAAnB,EAAsB;AAClB,mBAAO1B,IAAI,CAAC2B,IAAL,CAAU,IAAI3B,IAAJ,EAAV,EAAsBuB,EAAtB,EAA0BC,EAA1B,EAA8BH,CAA9B,CAAP;AACH;;AAED,cAAMO,EAAE,GAAGP,CAAC,GAAGA,CAAf;AACA,cAAMQ,EAAE,GAAGD,EAAE,GAAGP,CAAhB,CAP6G,CAS7G;;AACA,cAAMS,UAAU,GAAG,IAAI9B,IAAJ,EAAnB;AACA8B,UAAAA,UAAU,CAAChB,CAAX,GAAe,OACV,IAAIS,EAAE,CAACT,CAAR,GACA,CAAC,CAACQ,EAAE,CAACR,CAAJ,GAAQU,EAAE,CAACV,CAAZ,IAAiBO,CADjB,GAEA,CAAC,IAAIC,EAAE,CAACR,CAAP,GAAW,IAAIS,EAAE,CAACT,CAAlB,GAAsB,IAAIU,EAAE,CAACV,CAA7B,GAAiCW,EAAE,CAACX,CAArC,IAA0Cc,EAF1C,GAGA,CAAC,CAACN,EAAE,CAACR,CAAJ,GAAQ,IAAIS,EAAE,CAACT,CAAf,GAAmB,IAAIU,EAAE,CAACV,CAA1B,GAA8BW,EAAE,CAACX,CAAlC,IAAuCe,EAJ5B,CAAf;AAOAC,UAAAA,UAAU,CAACf,CAAX,GAAe,OACV,IAAIQ,EAAE,CAACR,CAAR,GACA,CAAC,CAACO,EAAE,CAACP,CAAJ,GAAQS,EAAE,CAACT,CAAZ,IAAiBM,CADjB,GAEA,CAAC,IAAIC,EAAE,CAACP,CAAP,GAAW,IAAIQ,EAAE,CAACR,CAAlB,GAAsB,IAAIS,EAAE,CAACT,CAA7B,GAAiCU,EAAE,CAACV,CAArC,IAA0Ca,EAF1C,GAGA,CAAC,CAACN,EAAE,CAACP,CAAJ,GAAQ,IAAIQ,EAAE,CAACR,CAAf,GAAmB,IAAIS,EAAE,CAACT,CAA1B,GAA8BU,EAAE,CAACV,CAAlC,IAAuCc,EAJ5B,CAAf,CAlB6G,CAyB7G;;AACA,cAAIH,UAAU,GAAG,CAAjB,EAAoB;AAChB,gBAAMK,MAAM,GAAG/B,IAAI,CAAC2B,IAAL,CAAU,IAAI3B,IAAJ,EAAV,EAAsBuB,EAAtB,EAA0BC,EAA1B,EAA8BH,CAA9B,CAAf;AACA,mBAAOrB,IAAI,CAAC2B,IAAL,CAAU,IAAI3B,IAAJ,EAAV,EAAsB+B,MAAtB,EAA8BD,UAA9B,EAA0CJ,UAA1C,CAAP;AACH;;AAED,iBAAOI,UAAP;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,mBAAmB,GAAW;AACjC,cAAI,KAAKC,MAAL,CAAYC,MAAZ,GAAqB,CAAzB,EAA4B;AACxB,mBAAO,KAAKD,MAAL,CAAYE,GAAZ,CAAgBC,CAAC,IAAIA,CAAC,CAACpB,QAAvB,CAAP;AACH;;AAED,cAAMqB,WAAmB,GAAG,EAA5B;AACA,cAAMC,UAAU,GAAG,KAAKL,MAAL,CAAYC,MAA/B,CANiC,CAQjC;;AACAG,UAAAA,WAAW,CAACE,IAAZ,CAAiB,KAAKN,MAAL,CAAY,CAAZ,EAAejB,QAAhC,EATiC,CAWjC;;AACA,eAAK,IAAIwB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,UAAU,GAAG,CAAjC,EAAoCE,CAAC,EAArC,EAAyC;AACrC,gBAAMlB,EAAE,GAAG,KAAKmB,eAAL,CAAqBD,CAAC,GAAG,CAAzB,CAAX;AACA,gBAAMjB,EAAE,GAAG,KAAKU,MAAL,CAAYO,CAAZ,EAAexB,QAA1B;AACA,gBAAMQ,EAAE,GAAG,KAAKS,MAAL,CAAYO,CAAC,GAAG,CAAhB,EAAmBxB,QAA9B;AACA,gBAAMS,EAAE,GAAG,KAAKgB,eAAL,CAAqBD,CAAC,GAAG,CAAzB,CAAX,CAJqC,CAMrC;;AACA,gBAAME,eAAe,GAAG,KAAKT,MAAL,CAAYO,CAAZ,EAAed,UAAvC;AACA,gBAAMiB,aAAa,GAAG,KAAKV,MAAL,CAAYO,CAAC,GAAG,CAAhB,EAAmBd,UAAzC,CARqC,CAUrC;;AACA,gBAAIgB,eAAe,KAAK,CAApB,IAAyBC,aAAa,KAAK,CAA/C,EAAkD;AAC9C;AACA,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,KAAKC,QAA1B,EAAoCD,CAAC,EAArC,EAAyC;AACrC,oBAAMvB,CAAC,GAAGuB,CAAC,GAAG,KAAKC,QAAnB;AACA,oBAAMC,KAAK,GAAG9C,IAAI,CAAC2B,IAAL,CAAU,IAAI3B,IAAJ,EAAV,EAAsBuB,EAAtB,EAA0BC,EAA1B,EAA8BH,CAA9B,CAAd;AACAgB,gBAAAA,WAAW,CAACE,IAAZ,CAAiBO,KAAjB;AACH;AACJ,aAPD,MAOO;AACH;AACA,kBAAMpB,UAAU,GAAGqB,IAAI,CAACC,GAAL,CAASN,eAAT,EAA0BC,aAA1B,CAAnB,CAFG,CAIH;;AACA,mBAAK,IAAIC,EAAC,GAAG,CAAb,EAAgBA,EAAC,IAAI,KAAKC,QAA1B,EAAoCD,EAAC,EAArC,EAAyC;AACrC,oBAAMvB,EAAC,GAAGuB,EAAC,GAAG,KAAKC,QAAnB;;AACA,oBAAMC,MAAK,GAAG5B,QAAQ,CAACE,eAAT,CAAyBC,EAAzB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CC,UAA5C,CAAd;;AACAW,gBAAAA,WAAW,CAACE,IAAZ,CAAiBO,MAAjB;AACH;AACJ;AACJ;;AAED,iBAAOT,WAAP;AACH;AAED;AACJ;AACA;;;AACYI,QAAAA,eAAe,CAACQ,KAAD,EAAsB;AACzC,cAAMX,UAAU,GAAG,KAAKL,MAAL,CAAYC,MAA/B;;AAEA,cAAI,KAAKgB,MAAT,EAAiB;AACb;AACA,gBAAMC,YAAY,GAAG,CAAEF,KAAK,GAAGX,UAAT,GAAuBA,UAAxB,IAAsCA,UAA3D;AACA,mBAAO,KAAKL,MAAL,CAAYkB,YAAZ,EAA0BnC,QAAjC;AACH,WAJD,MAIO;AACH;AACA,gBAAIiC,KAAK,GAAG,CAAZ,EAAe;AACX;AACA,kBAAM3B,EAAE,GAAG,KAAKW,MAAL,CAAY,CAAZ,EAAejB,QAA1B;AACA,kBAAMO,EAAE,GAAG,KAAKU,MAAL,CAAY,CAAZ,EAAejB,QAA1B;AACA,qBAAOhB,IAAI,CAACoD,QAAL,CAAc,IAAIpD,IAAJ,EAAd,EAA0BsB,EAA1B,EAA8BtB,IAAI,CAACoD,QAAL,CAAc,IAAIpD,IAAJ,EAAd,EAA0BuB,EAA1B,EAA8BD,EAA9B,CAA9B,CAAP;AACH,aALD,MAKO,IAAI2B,KAAK,IAAIX,UAAb,EAAyB;AAC5B;AACA,kBAAMhB,EAAE,GAAG,KAAKW,MAAL,CAAYK,UAAU,GAAG,CAAzB,EAA4BtB,QAAvC;AACA,kBAAMO,GAAE,GAAG,KAAKU,MAAL,CAAYK,UAAU,GAAG,CAAzB,EAA4BtB,QAAvC;AACA,qBAAOhB,IAAI,CAACqD,GAAL,CAAS,IAAIrD,IAAJ,EAAT,EAAqBuB,GAArB,EAAyBvB,IAAI,CAACoD,QAAL,CAAc,IAAIpD,IAAJ,EAAd,EAA0BuB,GAA1B,EAA8BD,EAA9B,CAAzB,CAAP;AACH,aALM,MAKA;AACH,qBAAO,KAAKW,MAAL,CAAYgB,KAAZ,EAAmBjC,QAA1B;AACH;AACJ;AACJ;;AAnIiB,O;;;;;iBAEI,E;;;;;;;iBAGO,E;;;;;;;iBAGH,E;;;;;;;iBAGD,K", "sourcesContent": ["import { _decorator, Vec2, CCFloat, CC<PERSON><PERSON>ger, Enum } from 'cc';\r\nimport { eOrientationType } from './WaveData';\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 路径点数据\r\n */\r\n@ccclass(\"PathPoint\")\r\nexport class PathPoint {\r\n    @property({ type: CCFloat, displayName: \"X坐标\" })\r\n    public x: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"Y坐标\" })\r\n    public y: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"平滑程度\", range: [0, 1], slide: true, tooltip: \"0=直线连接, 1=最大平滑曲线\" })\r\n    public smoothness: number = 1;\r\n\r\n    @property({ type: CCInteger, displayName: \"速度\", tooltip: \"飞机在此点的速度\" })\r\n    public speed: number = 500;\r\n\r\n    @property({ type: Enum(eOrientationType), displayName: \"朝向类型\", tooltip: \"飞机在此点的朝向\" })\r\n    public orientationType: eOrientationType = 0;\r\n\r\n    @property({ type: CCInteger, displayName: \"朝向参数\", tooltip: \"根据朝向类型不同而不同\" })\r\n    public orientationParam: number = 0;\r\n\r\n    constructor(x: number = 0, y: number = 0) {\r\n        this.x = x;\r\n        this.y = y;\r\n    }\r\n\r\n    public get position(): Vec2 {\r\n        return new Vec2(this.x, this.y);\r\n    }\r\n\r\n    public set position(value: Vec2) {\r\n        this.x = value.x;\r\n        this.y = value.y;\r\n    }\r\n}\r\n\r\n/**\r\n * 路径数据\r\n */\r\n@ccclass(\"PathData\")\r\nexport class PathData {\r\n    @property({ displayName: '路径名称', editorOnly: true })\r\n    public name: string = \"\";\r\n\r\n    @property({ type: [PathPoint], displayName: '路径点' })\r\n    public points: PathPoint[] = [];\r\n\r\n    @property({ type: CCInteger, displayName: \"曲线分段数\", tooltip: \"每段曲线的细分数量，影响曲线平滑度\" })\r\n    public segments: number = 20;\r\n\r\n    @property({ displayName: \"是否闭合路径\", tooltip: \"路径是否形成闭环\" })\r\n    public closed: boolean = false;\r\n\r\n    /**\r\n     * 获取Catmull-Rom曲线上的点\r\n     * @param t 参数值 [0, 1]\r\n     * @param p0 前一个控制点（用于计算切线）\r\n     * @param p1 起始点（曲线经过此点）\r\n     * @param p2 结束点（曲线经过此点）\r\n     * @param p3 后一个控制点（用于计算切线）\r\n     * @param smoothness 平滑程度 [0, 1]，0=直线，1=最平滑曲线\r\n     */\r\n    public static catmullRomPoint(t: number, p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number = 0.5): Vec2 {\r\n        // 当smoothness为0时，直接返回线性插值（直线）\r\n        if (smoothness === 0) {\r\n            return Vec2.lerp(new Vec2(), p1, p2, t);\r\n        }\r\n\r\n        const t2 = t * t;\r\n        const t3 = t2 * t;\r\n\r\n        // 标准Catmull-Rom插值公式\r\n        const catmullRom = new Vec2();\r\n        catmullRom.x = 0.5 * (\r\n            (2 * p1.x) +\r\n            (-p0.x + p2.x) * t +\r\n            (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 +\r\n            (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3\r\n        );\r\n\r\n        catmullRom.y = 0.5 * (\r\n            (2 * p1.y) +\r\n            (-p0.y + p2.y) * t +\r\n            (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 +\r\n            (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3\r\n        );\r\n\r\n        // 当smoothness不为1时，在线性插值和Catmull-Rom之间混合\r\n        if (smoothness < 1) {\r\n            const linear = Vec2.lerp(new Vec2(), p1, p2, t);\r\n            return Vec2.lerp(new Vec2(), linear, catmullRom, smoothness);\r\n        }\r\n\r\n        return catmullRom;\r\n    }\r\n\r\n    /**\r\n     * 生成完整的曲线路径点\r\n     */\r\n    public generateCurvePoints(): Vec2[] {\r\n        if (this.points.length < 2) {\r\n            return this.points.map(p => p.position);\r\n        }\r\n\r\n        const curvePoints: Vec2[] = [];\r\n        const pointCount = this.points.length;\r\n\r\n        // 添加第一个点（确保曲线经过起点）\r\n        curvePoints.push(this.points[0].position);\r\n\r\n        // 为每一段生成曲线点\r\n        for (let i = 0; i < pointCount - 1; i++) {\r\n            const p0 = this.getControlPoint(i - 1);\r\n            const p1 = this.points[i].position;\r\n            const p2 = this.points[i + 1].position;\r\n            const p3 = this.getControlPoint(i + 2);\r\n\r\n            // 检查是否有任何一个端点要求直线连接\r\n            const startSmoothness = this.points[i].smoothness;\r\n            const endSmoothness = this.points[i + 1].smoothness;\r\n\r\n            // 如果任一端点的smoothness为0，则整段使用直线\r\n            if (startSmoothness === 0 || endSmoothness === 0) {\r\n                // 直线连接\r\n                for (let j = 1; j <= this.segments; j++) {\r\n                    const t = j / this.segments;\r\n                    const point = Vec2.lerp(new Vec2(), p1, p2, t);\r\n                    curvePoints.push(point);\r\n                }\r\n            } else {\r\n                // 使用平滑程度的最小值（更保守的方法）\r\n                const smoothness = Math.min(startSmoothness, endSmoothness);\r\n\r\n                // 生成这一段的曲线点（不包括起点，因为已经添加过了）\r\n                for (let j = 1; j <= this.segments; j++) {\r\n                    const t = j / this.segments;\r\n                    const point = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);\r\n                    curvePoints.push(point);\r\n                }\r\n            }\r\n        }\r\n\r\n        return curvePoints;\r\n    }\r\n\r\n    /**\r\n     * 获取控制点（处理边界情况）\r\n     */\r\n    private getControlPoint(index: number): Vec2 {\r\n        const pointCount = this.points.length;\r\n\r\n        if (this.closed) {\r\n            // 闭合路径，使用循环索引\r\n            const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;\r\n            return this.points[wrappedIndex].position;\r\n        } else {\r\n            // 开放路径，边界处理\r\n            if (index < 0) {\r\n                // 延伸第一个点\r\n                const p0 = this.points[0].position;\r\n                const p1 = this.points[1].position;\r\n                return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else if (index >= pointCount) {\r\n                // 延伸最后一个点\r\n                const p0 = this.points[pointCount - 2].position;\r\n                const p1 = this.points[pointCount - 1].position;\r\n                return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else {\r\n                return this.points[index].position;\r\n            }\r\n        }\r\n    }\r\n}"]}