import { _decorator, instantiate, Node, Component, JsonAsset, Rect, Vec3, Graphics, assetManager } from 'cc';
const { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, requireComponent  } = _decorator;
import { EDITOR } from 'cc/env';

import { FormationGroup, FormationPoint } from 'db://assets/bundles/common/script/game/data/WaveData';
import { FormationPointEditor } from './FormationPointEditor';

@ccclass('FormationEditor')
@menu("怪物/编辑器/阵型")
@executeInEditMode(true)
@disallowMultiple(true)
export class FormationEditor extends Component {
    @property({type: JsonAsset, displayName: "阵型数据"})
    public set formationData(value: JsonAsset) {
        this._formationData = value;
        this.reload();
    }
    public get formationData(): JsonAsset|null {
        return this._formationData;
    }

    @property({displayName: "阵型名字"})
    public get formationName(): string {
        return this._formationGroup.name;
    }
    public set formationName(value:string) {
        this._formationGroup.name = value;
    }

    private _formationData: JsonAsset|null = null;
    private _formationGroup: FormationGroup = new FormationGroup();

    public reload() {
        if (!this._formationData) return;

        const formationGroup = new FormationGroup();
        Object.assign(formationGroup, this._formationData.json);
        this._formationGroup = formationGroup;
        
        this.node.removeAllChildren();
        if (this._formationGroup && this._formationGroup.points.length > 0) {
            this._formationGroup.points.forEach((point) => {
                this.addPoint(point);
            });
        }        
    }

    public save(): string {
        // save this._formationGroup to this._formationData
        const points = this.getComponentsInChildren(FormationPointEditor);
        this._formationGroup.points = points.map((point) => point.formationPoint);
        return JSON.stringify(this._formationGroup, null, 2);
    }

    public addPoint(point: FormationPoint) {
        const pointNode = new Node();
        pointNode.parent = this.node;
        const pointEditor = pointNode.addComponent(FormationPointEditor);
        pointEditor.formationPoint = point;
    }

    public addNewPoint(x: number, y: number) {
        const point = new FormationPoint();
        point.x = x;
        point.y = y;
        this.addPoint(point);
    }
}