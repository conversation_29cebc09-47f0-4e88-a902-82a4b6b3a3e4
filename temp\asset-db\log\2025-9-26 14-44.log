2025-9-26 14:44:12-log: Cannot access game frame or container.
2025-9-26 14:44:12-debug: asset-db:require-engine-code (432ms)
2025-9-26 14:44:12-log: [bullet]:bullet wasm lib loaded.
2025-9-26 14:44:12-log: meshopt wasm decoder initialized
2025-9-26 14:44:12-log: [box2d]:box2d wasm lib loaded.
2025-9-26 14:44:12-log: Using legacy pipeline
2025-9-26 14:44:12-log: Forward render pipeline initialized.
2025-9-26 14:44:12-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.81MB, end 80.17MB, increase: 49.37MB
2025-9-26 14:44:12-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.34MB, end 84.40MB, increase: 3.07MB
2025-9-26 14:44:12-log: Cocos Creator v3.8.6
2025-9-26 14:44:13-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.20MB, end 287.76MB, increase: 207.56MB
2025-9-26 14:44:13-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.44MB, end 289.31MB, increase: 204.87MB
2025-9-26 14:44:13-debug: [Assets Memory track]: asset-db-plugin-register: project start:81.19MB, end 287.73MB, increase: 206.55MB
2025-9-26 14:44:13-debug: run package(google-play) handler(enable) success!
2025-9-26 14:44:13-debug: run package(google-play) handler(enable) start
2025-9-26 14:44:13-debug: run package(harmonyos-next) handler(enable) success!
2025-9-26 14:44:13-debug: run package(harmonyos-next) handler(enable) start
2025-9-26 14:44:13-debug: run package(honor-mini-game) handler(enable) start
2025-9-26 14:44:13-debug: run package(honor-mini-game) handler(enable) success!
2025-9-26 14:44:13-debug: run package(huawei-agc) handler(enable) success!
2025-9-26 14:44:13-debug: run package(huawei-quick-game) handler(enable) start
2025-9-26 14:44:13-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-26 14:44:13-debug: run package(huawei-agc) handler(enable) start
2025-9-26 14:44:13-debug: run package(ios) handler(enable) start
2025-9-26 14:44:13-debug: run package(ios) handler(enable) success!
2025-9-26 14:44:13-debug: run package(linux) handler(enable) success!
2025-9-26 14:44:13-debug: run package(mac) handler(enable) start
2025-9-26 14:44:13-debug: run package(linux) handler(enable) start
2025-9-26 14:44:13-debug: run package(mac) handler(enable) success!
2025-9-26 14:44:13-debug: run package(native) handler(enable) success!
2025-9-26 14:44:13-debug: run package(migu-mini-game) handler(enable) start
2025-9-26 14:44:13-debug: run package(ohos) handler(enable) start
2025-9-26 14:44:13-debug: run package(migu-mini-game) handler(enable) success!
2025-9-26 14:44:13-debug: run package(native) handler(enable) start
2025-9-26 14:44:13-debug: run package(ohos) handler(enable) success!
2025-9-26 14:44:13-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-26 14:44:13-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-26 14:44:13-debug: run package(taobao-mini-game) handler(enable) start
2025-9-26 14:44:13-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-26 14:44:13-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-26 14:44:13-debug: run package(oppo-mini-game) handler(enable) start
2025-9-26 14:44:13-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-26 14:44:13-debug: run package(web-desktop) handler(enable) success!
2025-9-26 14:44:13-debug: run package(vivo-mini-game) handler(enable) start
2025-9-26 14:44:13-debug: run package(web-desktop) handler(enable) start
2025-9-26 14:44:13-debug: run package(web-mobile) handler(enable) start
2025-9-26 14:44:13-debug: run package(web-mobile) handler(enable) success!
2025-9-26 14:44:13-debug: run package(wechatgame) handler(enable) start
2025-9-26 14:44:13-debug: run package(wechatgame) handler(enable) success!
2025-9-26 14:44:13-debug: run package(wechatprogram) handler(enable) success!
2025-9-26 14:44:13-debug: run package(windows) handler(enable) start
2025-9-26 14:44:13-debug: run package(wechatprogram) handler(enable) start
2025-9-26 14:44:13-debug: run package(windows) handler(enable) success!
2025-9-26 14:44:13-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-26 14:44:13-debug: run package(cocos-service) handler(enable) success!
2025-9-26 14:44:13-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-26 14:44:13-debug: run package(im-plugin) handler(enable) start
2025-9-26 14:44:13-debug: run package(im-plugin) handler(enable) success!
2025-9-26 14:44:13-debug: run package(cocos-service) handler(enable) start
2025-9-26 14:44:13-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-26 14:44:13-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-26 14:44:13-debug: run package(emitter-editor) handler(enable) start
2025-9-26 14:44:13-debug: run package(emitter-editor) handler(enable) success!
2025-9-26 14:44:13-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-9-26 14:44:13-debug: refresh asset db://assets/editor/enum-gen success
2025-9-26 14:44:13-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-9-26 14:44:13-debug: refresh asset db://assets/editor/enum-gen success
2025-9-26 14:44:13-debug: asset-db:worker-init: initPlugin (1057ms)
2025-9-26 14:44:13-debug: [Assets Memory track]: asset-db:worker-init start:30.80MB, end 290.07MB, increase: 259.27MB
2025-9-26 14:44:13-debug: Run asset db hook programming:beforePreStart success!
2025-9-26 14:44:13-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-26 14:44:13-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-26 14:44:13-debug: Run asset db hook programming:beforePreStart ...
2025-9-26 14:44:13-debug: start custom db i18n...
2025-9-26 14:44:13-debug: run package(i18n) handler(enable) success!
2025-9-26 14:44:13-debug: run package(i18n) handler(enable) start
2025-9-26 14:44:13-debug: start asset-db(i18n)...
2025-9-26 14:44:13-debug: run package(level-editor) handler(enable) start
2025-9-26 14:44:13-debug: run package(level-editor) handler(enable) success!
2025-9-26 14:44:13-debug: asset-db:worker-init (1631ms)
2025-9-26 14:44:13-debug: asset-db-hook-engine-extends-beforePreStart (64ms)
2025-9-26 14:44:13-debug: asset-db-hook-programming-beforePreStart (64ms)
2025-9-26 14:44:13-debug: [Assets Memory track]: asset-db:worker-startup-database[i18n] start:291.35MB, end 298.66MB, increase: 7.30MB
2025-9-26 14:44:13-debug: Preimport db internal success
2025-9-26 14:44:13-debug: run package(localization-editor) handler(enable) start
2025-9-26 14:44:13-debug: run package(localization-editor) handler(enable) success!
2025-9-26 14:44:13-debug: asset-db:worker-startup-database[i18n] (90ms)
2025-9-26 14:44:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:44:14-debug: run package(placeholder) handler(enable) start
2025-9-26 14:44:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:44:14-debug: run package(placeholder) handler(enable) success!
2025-9-26 14:44:14-debug: Preimport db assets success
2025-9-26 14:44:14-debug: Run asset db hook programming:afterPreStart ...
2025-9-26 14:44:14-debug: starting packer-driver...
2025-9-26 14:44:20-debug: initialize scripting environment...
2025-9-26 14:44:20-debug: [[Executor]] prepare before lock
2025-9-26 14:44:20-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-26 14:44:20-debug: [[Executor]] prepare after unlock
2025-9-26 14:44:20-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-26 14:44:20-debug: Run asset db hook programming:afterPreStart success!
2025-9-26 14:44:20-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-26 14:44:20-debug: Start up the 'internal' database...
2025-9-26 14:44:21-debug: asset-db-hook-programming-afterPreStart (7072ms)
2025-9-26 14:44:21-debug: asset-db:worker-effect-data-processing (241ms)
2025-9-26 14:44:21-debug: asset-db-hook-engine-extends-afterPreStart (241ms)
2025-9-26 14:44:21-debug: Start up the 'assets' database...
2025-9-26 14:44:21-debug: asset-db:worker-startup-database[internal] (7360ms)
2025-9-26 14:44:21-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PathEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:44:21-debug: [Assets Memory track]: asset-db:worker-init: startup start:178.42MB, end 196.74MB, increase: 18.32MB
2025-9-26 14:44:21-debug: lazy register asset handler directory
2025-9-26 14:44:21-debug: lazy register asset handler *
2025-9-26 14:44:21-debug: lazy register asset handler text
2025-9-26 14:44:21-debug: lazy register asset handler spine-data
2025-9-26 14:44:21-debug: lazy register asset handler dragonbones
2025-9-26 14:44:21-debug: lazy register asset handler json
2025-9-26 14:44:21-debug: lazy register asset handler javascript
2025-9-26 14:44:21-debug: lazy register asset handler terrain
2025-9-26 14:44:21-debug: lazy register asset handler dragonbones-atlas
2025-9-26 14:44:21-debug: lazy register asset handler scene
2025-9-26 14:44:21-debug: lazy register asset handler sprite-frame
2025-9-26 14:44:21-debug: lazy register asset handler tiled-map
2025-9-26 14:44:21-debug: lazy register asset handler typescript
2025-9-26 14:44:21-debug: lazy register asset handler prefab
2025-9-26 14:44:21-debug: lazy register asset handler image
2025-9-26 14:44:21-debug: lazy register asset handler buffer
2025-9-26 14:44:21-debug: lazy register asset handler texture
2025-9-26 14:44:21-debug: lazy register asset handler texture-cube
2025-9-26 14:44:21-debug: lazy register asset handler erp-texture-cube
2025-9-26 14:44:21-debug: lazy register asset handler render-texture
2025-9-26 14:44:21-debug: lazy register asset handler sign-image
2025-9-26 14:44:21-debug: lazy register asset handler alpha-image
2025-9-26 14:44:21-debug: lazy register asset handler texture-cube-face
2025-9-26 14:44:21-debug: lazy register asset handler gltf
2025-9-26 14:44:21-debug: lazy register asset handler rt-sprite-frame
2025-9-26 14:44:21-debug: lazy register asset handler gltf-animation
2025-9-26 14:44:21-debug: lazy register asset handler gltf-material
2025-9-26 14:44:21-debug: lazy register asset handler gltf-mesh
2025-9-26 14:44:21-debug: lazy register asset handler gltf-embeded-image
2025-9-26 14:44:21-debug: lazy register asset handler gltf-skeleton
2025-9-26 14:44:21-debug: lazy register asset handler fbx
2025-9-26 14:44:21-debug: lazy register asset handler material
2025-9-26 14:44:21-debug: lazy register asset handler gltf-scene
2025-9-26 14:44:21-debug: lazy register asset handler physics-material
2025-9-26 14:44:21-debug: lazy register asset handler audio-clip
2025-9-26 14:44:21-debug: lazy register asset handler animation-graph
2025-9-26 14:44:21-debug: lazy register asset handler effect-header
2025-9-26 14:44:21-debug: lazy register asset handler animation-graph-variant
2025-9-26 14:44:21-debug: lazy register asset handler animation-mask
2025-9-26 14:44:21-debug: lazy register asset handler animation-clip
2025-9-26 14:44:21-debug: lazy register asset handler ttf-font
2025-9-26 14:44:21-debug: lazy register asset handler particle
2025-9-26 14:44:21-debug: lazy register asset handler effect
2025-9-26 14:44:21-debug: lazy register asset handler bitmap-font
2025-9-26 14:44:21-debug: lazy register asset handler sprite-atlas
2025-9-26 14:44:21-debug: lazy register asset handler auto-atlas
2025-9-26 14:44:21-debug: lazy register asset handler label-atlas
2025-9-26 14:44:21-debug: lazy register asset handler render-stage
2025-9-26 14:44:21-debug: lazy register asset handler instantiation-material
2025-9-26 14:44:21-debug: lazy register asset handler instantiation-skeleton
2025-9-26 14:44:21-debug: lazy register asset handler instantiation-mesh
2025-9-26 14:44:21-debug: lazy register asset handler render-flow
2025-9-26 14:44:21-debug: lazy register asset handler render-pipeline
2025-9-26 14:44:21-debug: lazy register asset handler instantiation-animation
2025-9-26 14:44:21-debug: lazy register asset handler video-clip
2025-9-26 14:44:21-debug: asset-db:worker-startup-database[assets] (7326ms)
2025-9-26 14:44:21-debug: asset-db:ready (10891ms)
2025-9-26 14:44:21-debug: asset-db:start-database (7443ms)
2025-9-26 14:44:21-debug: fix the bug of updateDefaultUserData
2025-9-26 14:44:21-debug: init worker message success
2025-9-26 14:44:21-debug: programming:execute-script (4ms)
2025-9-26 14:44:21-debug: [Build Memory track]: builder:worker-init start:194.80MB, end 207.69MB, increase: 12.89MB
2025-9-26 14:44:21-debug: builder:worker-init (317ms)
2025-9-26 14:45:33-debug: Query all assets info in project
2025-9-26 14:45:33-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-26 14:45:33-debug: Skip compress image, progress: 0%
2025-9-26 14:45:33-debug: 查询 Asset Bundle start, progress: 0%
2025-9-26 14:45:33-debug: Num of bundles: 16..., progress: 0%
2025-9-26 14:45:33-debug: Init all bundles start..., progress: 0%
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:45:33-debug: Init bundle root assets start..., progress: 0%
2025-9-26 14:45:33-debug:   Number of all scripts: 272
2025-9-26 14:45:33-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-26 14:45:33-debug: Init bundle root assets success..., progress: 0%
2025-9-26 14:45:33-debug:   Number of all scenes: 11
2025-9-26 14:45:33-debug:   Number of other assets: 2061
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-9-26 14:45:33-debug: [Build Memory track]: 查询 Asset Bundle start:202.90MB, end 204.34MB, increase: 1.44MB
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:45:33-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-9-26 14:45:33-debug: 查询 Asset Bundle start, progress: 5%
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-26 14:45:33-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-26 14:45:33-debug: [Build Memory track]: 查询 Asset Bundle start:204.37MB, end 203.80MB, increase: -587.76KB
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:45:33-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-26 14:45:33-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-26 14:45:33-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-26 14:45:33-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:203.84MB, end 203.87MB, increase: 26.61KB
2025-9-26 14:45:33-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-26 14:45:33-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-26 14:45:33-debug: [Build Memory track]: 填充脚本数据到 settings.json start:203.90MB, end 203.92MB, increase: 19.96KB
2025-9-26 14:45:33-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-26 14:45:33-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-26 14:45:33-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:203.96MB, end 204.27MB, increase: 315.27KB
2025-9-26 14:45:33-debug: Query all assets info in project
2025-9-26 14:45:33-debug: Query all assets info in project
2025-9-26 14:45:33-debug: Query all assets info in project
2025-9-26 14:45:33-debug: Query all assets info in project
2025-9-26 14:45:33-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-26 14:45:33-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-26 14:45:33-debug: Skip compress image, progress: 0%
2025-9-26 14:45:33-debug: Skip compress image, progress: 0%
2025-9-26 14:45:33-debug: Skip compress image, progress: 0%
2025-9-26 14:45:33-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-26 14:45:33-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-26 14:45:33-debug: Skip compress image, progress: 0%
2025-9-26 14:45:33-debug: Init all bundles start..., progress: 0%
2025-9-26 14:45:33-debug: 查询 Asset Bundle start, progress: 0%
2025-9-26 14:45:33-debug: Num of bundles: 16..., progress: 0%
2025-9-26 14:45:33-debug: Num of bundles: 16..., progress: 0%
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:45:33-debug: Init all bundles start..., progress: 0%
2025-9-26 14:45:33-debug: Init bundle root assets start..., progress: 0%
2025-9-26 14:45:33-debug: 查询 Asset Bundle start, progress: 0%
2025-9-26 14:45:33-debug: 查询 Asset Bundle start, progress: 0%
2025-9-26 14:45:33-debug: Init bundle root assets start..., progress: 0%
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:45:33-debug: Init all bundles start..., progress: 0%
2025-9-26 14:45:33-debug: Init bundle root assets start..., progress: 0%
2025-9-26 14:45:33-debug: Num of bundles: 16..., progress: 0%
2025-9-26 14:45:33-debug: Init all bundles start..., progress: 0%
2025-9-26 14:45:33-debug: 查询 Asset Bundle start, progress: 0%
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:45:33-debug: Num of bundles: 16..., progress: 0%
2025-9-26 14:45:33-debug: Init bundle root assets start..., progress: 0%
2025-9-26 14:45:33-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-26 14:45:33-debug:   Number of all scenes: 11
2025-9-26 14:45:33-debug:   Number of other assets: 2061
2025-9-26 14:45:33-debug: Init bundle root assets success..., progress: 0%
2025-9-26 14:45:33-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-26 14:45:33-debug:   Number of all scripts: 272
2025-9-26 14:45:33-debug:   Number of other assets: 2061
2025-9-26 14:45:33-debug:   Number of all scripts: 272
2025-9-26 14:45:33-debug: Init bundle root assets success..., progress: 0%
2025-9-26 14:45:33-debug:   Number of all scenes: 11
2025-9-26 14:45:33-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-26 14:45:33-debug:   Number of all scenes: 11
2025-9-26 14:45:33-debug:   Number of all scripts: 272
2025-9-26 14:45:33-debug:   Number of other assets: 2061
2025-9-26 14:45:33-debug: Init bundle root assets success..., progress: 0%
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ---- (39ms)
2025-9-26 14:45:33-log: run build task 查询 Asset Bundle success in 39 ms√, progress: 5%
2025-9-26 14:45:33-debug: [Build Memory track]: 查询 Asset Bundle start:205.71MB, end 208.54MB, increase: 2.83MB
2025-9-26 14:45:33-debug: 查询 Asset Bundle start, progress: 5%
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:45:33-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-26 14:45:33-debug: [Build Memory track]: 查询 Asset Bundle start:208.57MB, end 208.59MB, increase: 14.59KB
2025-9-26 14:45:33-debug: 查询 Asset Bundle start, progress: 5%
2025-9-26 14:45:33-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-26 14:45:33-debug: [Build Memory track]: 查询 Asset Bundle start:208.62MB, end 208.64MB, increase: 14.59KB
2025-9-26 14:45:33-debug: 查询 Asset Bundle start, progress: 5%
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:45:33-debug:   Number of all scripts: 272
2025-9-26 14:45:33-debug:   Number of all scenes: 11
2025-9-26 14:45:33-debug: Init bundle root assets success..., progress: 0%
2025-9-26 14:45:33-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-26 14:45:33-debug:   Number of other assets: 2061
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-9-26 14:45:33-log: run build task 查询 Asset Bundle success in 16 ms√, progress: 5%
2025-9-26 14:45:33-debug: [Build Memory track]: 查询 Asset Bundle start:208.68MB, end 209.07MB, increase: 400.67KB
2025-9-26 14:45:33-debug: 查询 Asset Bundle start, progress: 5%
2025-9-26 14:45:33-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:45:33-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-26 14:45:33-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-26 14:45:33-debug: [Build Memory track]: 查询 Asset Bundle start:209.11MB, end 209.14MB, increase: 36.43KB
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:45:33-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-26 14:45:33-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:45:33-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-26 14:45:33-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:45:33-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-26 14:45:33-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-26 14:45:33-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-26 14:45:33-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.26MB, end 209.54MB, increase: 286.38KB
2025-9-26 14:45:33-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-26 14:45:33-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-26 14:45:33-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-26 14:45:33-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-26 14:45:33-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-26 14:45:33-debug: [Build Memory track]: 填充脚本数据到 settings.json start:209.70MB, end 209.71MB, increase: 15.25KB
2025-9-26 14:45:33-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-26 14:45:33-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-26 14:45:33-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:45:33-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:45:33-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-26 14:45:33-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-26 14:45:33-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:45:33-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-26 14:45:33-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-26 14:45:33-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-26 14:45:33-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-26 14:45:33-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.88MB, end 209.91MB, increase: 34.47KB
2025-9-26 14:45:33-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-26 14:45:33-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-26 14:45:33-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-26 14:45:33-debug: [Build Memory track]: 填充脚本数据到 settings.json start:209.95MB, end 209.98MB, increase: 26.69KB
2025-9-26 14:45:33-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:45:33-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-26 14:45:33-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-26 14:45:33-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.01MB, end 211.06MB, increase: 1.05MB
2025-9-26 14:45:33-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-26 14:45:33-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-26 14:45:33-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-26 14:49:35-debug: Query all assets info in project
2025-9-26 14:49:35-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-26 14:49:35-debug: Skip compress image, progress: 0%
2025-9-26 14:49:35-debug: Init all bundles start..., progress: 0%
2025-9-26 14:49:35-debug: 查询 Asset Bundle start, progress: 0%
2025-9-26 14:49:35-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:49:35-debug: Num of bundles: 16..., progress: 0%
2025-9-26 14:49:35-debug: Init bundle root assets start..., progress: 0%
2025-9-26 14:49:35-debug:   Number of all scenes: 11
2025-9-26 14:49:35-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-26 14:49:35-debug:   Number of all scripts: 272
2025-9-26 14:49:35-debug: Init bundle root assets success..., progress: 0%
2025-9-26 14:49:35-debug:   Number of other assets: 2061
2025-9-26 14:49:35-debug: // ---- build task 查询 Asset Bundle ---- (30ms)
2025-9-26 14:49:35-log: run build task 查询 Asset Bundle success in 30 ms√, progress: 5%
2025-9-26 14:49:35-debug: 查询 Asset Bundle start, progress: 5%
2025-9-26 14:49:35-debug: [Build Memory track]: 查询 Asset Bundle start:210.76MB, end 211.73MB, increase: 999.49KB
2025-9-26 14:49:35-debug: // ---- build task 查询 Asset Bundle ----
2025-9-26 14:49:35-debug: refresh db internal success
2025-9-26 14:49:35-debug: refresh db i18n success
2025-9-26 14:49:35-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-9-26 14:49:35-debug: %cDestroy%c: E:\M2Game\Client\assets\editor\level\wave\PathEditorExample.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-26 14:49:35-debug: [Build Memory track]: 查询 Asset Bundle start:211.77MB, end 212.51MB, increase: 762.61KB
2025-9-26 14:49:35-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-26 14:49:35-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-9-26 14:49:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:49:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (45ms)
2025-9-26 14:49:35-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.55MB, end 213.11MB, increase: 569.55KB
2025-9-26 14:49:35-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-26 14:49:35-log: run build task 整理部分构建选项内数据到 settings.json success in 45 ms√, progress: 12%
2025-9-26 14:49:35-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-26 14:49:35-debug: // ---- build task 填充脚本数据到 settings.json ---- (7ms)
2025-9-26 14:49:35-log: run build task 填充脚本数据到 settings.json success in 7 ms√, progress: 13%
2025-9-26 14:49:35-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-26 14:49:35-debug: [Build Memory track]: 填充脚本数据到 settings.json start:213.14MB, end 213.62MB, increase: 484.75KB
2025-9-26 14:49:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-26 14:49:35-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:49:35-log: run build task 整理部分构建选项内数据到 settings.json success in 11 ms√, progress: 15%
2025-9-26 14:49:35-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.65MB, end 215.22MB, increase: 1.57MB
2025-9-26 14:49:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (11ms)
2025-9-26 14:49:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:49:35-debug: refresh db assets success
2025-9-26 14:49:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:49:35-debug: asset-db:refresh-all-database (249ms)
2025-9-26 14:49:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:50:03-debug: refresh db internal success
2025-9-26 14:50:03-debug: refresh db i18n success
2025-9-26 14:50:03-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:50:04-debug: refresh db assets success
2025-9-26 14:50:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:50:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:50:04-debug: asset-db:refresh-all-database (172ms)
2025-9-26 14:50:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 14:50:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:50:21-debug: refresh db internal success
2025-9-26 14:50:21-debug: refresh db i18n success
2025-9-26 14:50:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:50:22-debug: refresh db assets success
2025-9-26 14:50:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:50:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:50:22-debug: asset-db:refresh-all-database (169ms)
2025-9-26 14:50:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:50:52-debug: refresh db internal success
2025-9-26 14:50:52-debug: refresh db i18n success
2025-9-26 14:50:52-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:50:52-debug: refresh db assets success
2025-9-26 14:50:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:50:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:50:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 14:50:52-debug: asset-db:refresh-all-database (170ms)
2025-9-26 14:50:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:51:00-debug: refresh db internal success
2025-9-26 14:51:00-debug: refresh db i18n success
2025-9-26 14:51:00-debug: refresh db assets success
2025-9-26 14:51:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:51:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:51:00-debug: asset-db:refresh-all-database (161ms)
2025-9-26 14:51:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 14:51:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:51:02-debug: refresh db internal success
2025-9-26 14:51:02-debug: refresh db i18n success
2025-9-26 14:51:02-debug: refresh db assets success
2025-9-26 14:51:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:51:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:51:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 14:51:02-debug: asset-db:refresh-all-database (156ms)
2025-9-26 14:51:02-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 14:51:11-debug: refresh db internal success
2025-9-26 14:51:11-debug: refresh db i18n success
2025-9-26 14:51:11-debug: refresh db assets success
2025-9-26 14:51:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:51:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:51:11-debug: asset-db:refresh-all-database (121ms)
2025-9-26 14:51:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 14:51:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:51:13-debug: refresh db internal success
2025-9-26 14:51:13-debug: refresh db i18n success
2025-9-26 14:51:13-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:51:13-debug: refresh db assets success
2025-9-26 14:51:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:51:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:51:13-debug: asset-db:refresh-all-database (125ms)
2025-9-26 14:51:13-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 14:51:13-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 14:51:21-debug: refresh db internal success
2025-9-26 14:51:21-debug: refresh db i18n success
2025-9-26 14:51:21-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:51:21-debug: refresh db assets success
2025-9-26 14:51:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:51:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:51:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 14:51:21-debug: asset-db:refresh-all-database (122ms)
2025-9-26 14:51:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:51:31-debug: refresh db internal success
2025-9-26 14:51:31-debug: refresh db i18n success
2025-9-26 14:51:31-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:51:31-debug: refresh db assets success
2025-9-26 14:51:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:51:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:51:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 14:51:31-debug: asset-db:refresh-all-database (122ms)
2025-9-26 14:51:31-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 14:51:37-debug: refresh db internal success
2025-9-26 14:51:37-debug: refresh db i18n success
2025-9-26 14:51:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:51:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:51:37-debug: refresh db assets success
2025-9-26 14:51:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 14:51:37-debug: asset-db:refresh-all-database (116ms)
2025-9-26 14:51:37-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 14:53:14-debug: refresh db internal success
2025-9-26 14:53:14-debug: refresh db i18n success
2025-9-26 14:53:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:53:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:53:14-debug: refresh db assets success
2025-9-26 14:53:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:53:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:53:14-debug: asset-db:refresh-all-database (170ms)
2025-9-26 14:53:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 14:53:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:53:27-debug: refresh db internal success
2025-9-26 14:53:27-debug: refresh db i18n success
2025-9-26 14:53:27-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:53:27-debug: refresh db assets success
2025-9-26 14:53:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:53:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:53:27-debug: asset-db:refresh-all-database (170ms)
2025-9-26 14:53:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:54:57-debug: refresh db internal success
2025-9-26 14:54:57-debug: refresh db i18n success
2025-9-26 14:54:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:54:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:54:57-debug: refresh db assets success
2025-9-26 14:54:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:54:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:54:57-debug: asset-db:refresh-all-database (175ms)
2025-9-26 14:54:57-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 14:54:57-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 14:55:00-debug: refresh db internal success
2025-9-26 14:55:00-debug: refresh db i18n success
2025-9-26 14:55:01-debug: refresh db assets success
2025-9-26 14:55:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:55:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:55:01-debug: asset-db:refresh-all-database (142ms)
2025-9-26 14:55:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 14:55:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:58:18-debug: refresh db internal success
2025-9-26 14:58:18-debug: refresh db i18n success
2025-9-26 14:58:18-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:58:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:58:18-debug: refresh db assets success
2025-9-26 14:58:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:58:18-debug: asset-db:refresh-all-database (159ms)
2025-9-26 14:58:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:58:59-debug: refresh db internal success
2025-9-26 14:58:59-debug: refresh db i18n success
2025-9-26 14:58:59-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 14:58:59-debug: refresh db assets success
2025-9-26 14:58:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:58:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:58:59-debug: asset-db:refresh-all-database (177ms)
2025-9-26 14:58:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 14:58:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 14:59:50-debug: refresh db internal success
2025-9-26 14:59:50-debug: refresh db i18n success
2025-9-26 14:59:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 14:59:50-debug: refresh db assets success
2025-9-26 14:59:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 14:59:50-debug: asset-db:refresh-all-database (181ms)
2025-9-26 14:59:50-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 14:59:50-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 15:00:25-debug: refresh db internal success
2025-9-26 15:00:25-debug: refresh db i18n success
2025-9-26 15:00:25-debug: refresh db assets success
2025-9-26 15:00:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:00:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:00:25-debug: asset-db:refresh-all-database (160ms)
2025-9-26 15:00:25-debug: asset-db:worker-effect-data-processing (5ms)
2025-9-26 15:00:25-debug: asset-db-hook-engine-extends-afterRefresh (6ms)
2025-9-26 15:03:27-debug: refresh db internal success
2025-9-26 15:03:27-debug: refresh db i18n success
2025-9-26 15:03:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:03:27-debug: refresh db assets success
2025-9-26 15:03:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:03:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:03:27-debug: asset-db:refresh-all-database (154ms)
2025-9-26 15:03:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:03:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:04:57-debug: refresh db internal success
2025-9-26 15:04:57-debug: refresh db i18n success
2025-9-26 15:04:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathTestExample.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:04:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:04:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:04:58-debug: refresh db assets success
2025-9-26 15:04:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:04:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:04:58-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 15:04:58-debug: asset-db:refresh-all-database (172ms)
2025-9-26 15:04:58-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-26 15:05:22-debug: refresh db internal success
2025-9-26 15:05:22-debug: refresh db i18n success
2025-9-26 15:05:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:05:22-debug: refresh db assets success
2025-9-26 15:05:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:05:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:05:22-debug: asset-db:refresh-all-database (170ms)
2025-9-26 15:05:22-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-26 15:05:22-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-26 15:08:02-debug: refresh db internal success
2025-9-26 15:08:02-debug: refresh db i18n success
2025-9-26 15:08:02-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:08:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:08:02-debug: refresh db assets success
2025-9-26 15:08:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:08:02-debug: asset-db:refresh-all-database (162ms)
2025-9-26 15:08:40-debug: refresh db internal success
2025-9-26 15:08:40-debug: refresh db i18n success
2025-9-26 15:08:40-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:08:40-debug: refresh db assets success
2025-9-26 15:08:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:08:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:08:40-debug: asset-db:refresh-all-database (158ms)
2025-9-26 15:09:41-debug: refresh db internal success
2025-9-26 15:09:41-debug: refresh db i18n success
2025-9-26 15:09:41-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:09:41-debug: refresh db assets success
2025-9-26 15:09:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:09:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:09:41-debug: asset-db:refresh-all-database (166ms)
2025-9-26 15:12:02-debug: refresh db internal success
2025-9-26 15:12:02-debug: refresh db i18n success
2025-9-26 15:12:02-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:12:02-debug: refresh db assets success
2025-9-26 15:12:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:12:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:12:02-debug: asset-db:refresh-all-database (158ms)
2025-9-26 15:12:40-debug: refresh db internal success
2025-9-26 15:12:40-debug: refresh db i18n success
2025-9-26 15:12:40-debug: %cDestroy%c: E:\M2Game\Client\assets\editor\level\wave\PathTestExample.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-26 15:12:40-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:12:40-debug: refresh db assets success
2025-9-26 15:12:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:12:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:12:40-debug: asset-db:refresh-all-database (167ms)
2025-9-26 15:12:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:12:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:13:42-debug: refresh db internal success
2025-9-26 15:13:42-debug: refresh db i18n success
2025-9-26 15:13:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:13:43-debug: refresh db assets success
2025-9-26 15:13:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:13:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:13:43-debug: asset-db:refresh-all-database (156ms)
2025-9-26 15:13:43-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-26 15:13:43-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-26 15:16:33-debug: refresh db internal success
2025-9-26 15:16:33-debug: refresh db i18n success
2025-9-26 15:16:33-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\SmoothnessTester.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:16:33-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:16:33-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:16:33-debug: refresh db assets success
2025-9-26 15:16:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:16:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:16:33-debug: asset-db:refresh-all-database (189ms)
2025-9-26 15:16:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:17:40-debug: refresh db internal success
2025-9-26 15:17:40-debug: refresh db i18n success
2025-9-26 15:17:41-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:17:41-debug: refresh db assets success
2025-9-26 15:17:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:17:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:17:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:17:41-debug: asset-db:refresh-all-database (157ms)
2025-9-26 15:17:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:19:51-debug: refresh db internal success
2025-9-26 15:19:51-debug: refresh db i18n success
2025-9-26 15:19:51-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:19:52-debug: refresh db assets success
2025-9-26 15:19:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:19:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:19:52-debug: asset-db:refresh-all-database (159ms)
2025-9-26 15:27:01-debug: refresh db internal success
2025-9-26 15:27:01-debug: refresh db i18n success
2025-9-26 15:27:01-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\README_OrientationArrow.md
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:27:01-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:27:01-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:27:01-debug: refresh db assets success
2025-9-26 15:27:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:27:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:27:01-debug: asset-db:refresh-all-database (172ms)
2025-9-26 15:27:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:27:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:27:15-debug: refresh db internal success
2025-9-26 15:27:15-debug: refresh db i18n success
2025-9-26 15:27:15-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:27:15-debug: refresh db assets success
2025-9-26 15:27:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:27:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:27:15-debug: asset-db:refresh-all-database (166ms)
2025-9-26 15:27:15-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 15:27:15-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 15:27:21-debug: refresh db internal success
2025-9-26 15:27:21-debug: refresh db i18n success
2025-9-26 15:27:21-debug: refresh db assets success
2025-9-26 15:27:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:27:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:27:21-debug: asset-db:refresh-all-database (161ms)
2025-9-26 15:27:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:27:21-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 15:27:44-debug: refresh db internal success
2025-9-26 15:27:44-debug: refresh db i18n success
2025-9-26 15:27:44-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:27:44-debug: refresh db assets success
2025-9-26 15:27:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:27:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:27:44-debug: asset-db:refresh-all-database (181ms)
2025-9-26 15:27:44-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 15:27:44-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 15:30:00-debug: refresh db internal success
2025-9-26 15:30:00-debug: refresh db i18n success
2025-9-26 15:30:00-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:30:00-debug: refresh db assets success
2025-9-26 15:30:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:30:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:30:00-debug: asset-db:refresh-all-database (164ms)
2025-9-26 15:35:45-debug: refresh db internal success
2025-9-26 15:35:45-debug: refresh db i18n success
2025-9-26 15:35:45-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:35:45-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\README_OrientationArrow.md
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:35:45-debug: refresh db assets success
2025-9-26 15:35:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:35:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:35:45-debug: asset-db:refresh-all-database (188ms)
2025-9-26 15:35:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:35:45-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 15:38:14-debug: refresh db internal success
2025-9-26 15:38:14-debug: refresh db i18n success
2025-9-26 15:38:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:38:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:38:14-debug: refresh db assets success
2025-9-26 15:38:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:38:14-debug: asset-db:refresh-all-database (122ms)
2025-9-26 15:41:04-debug: refresh db internal success
2025-9-26 15:41:04-debug: refresh db i18n success
2025-9-26 15:41:04-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\README_UndoFix.md
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:41:04-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:41:04-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:41:04-debug: refresh db assets success
2025-9-26 15:41:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:41:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:41:04-debug: asset-db:refresh-all-database (190ms)
2025-9-26 15:41:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:41:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:41:35-debug: refresh db internal success
2025-9-26 15:41:35-debug: refresh db i18n success
2025-9-26 15:41:35-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:41:35-debug: refresh db assets success
2025-9-26 15:41:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:41:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:41:35-debug: asset-db:refresh-all-database (162ms)
2025-9-26 15:41:35-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-26 15:41:35-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-26 15:42:56-debug: refresh db internal success
2025-9-26 15:42:56-debug: refresh db i18n success
2025-9-26 15:42:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:42:56-debug: refresh db assets success
2025-9-26 15:42:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:42:56-debug: asset-db:refresh-all-database (158ms)
2025-9-26 15:42:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:42:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:43:39-debug: refresh db internal success
2025-9-26 15:43:39-debug: refresh db i18n success
2025-9-26 15:43:39-debug: refresh db assets success
2025-9-26 15:43:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:43:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:43:39-debug: asset-db:refresh-all-database (143ms)
2025-9-26 15:43:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:43:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:44:35-debug: refresh db internal success
2025-9-26 15:44:35-debug: refresh db i18n success
2025-9-26 15:44:35-debug: refresh db assets success
2025-9-26 15:44:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:44:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:44:35-debug: asset-db:refresh-all-database (117ms)
2025-9-26 15:46:40-debug: refresh db internal success
2025-9-26 15:46:40-debug: refresh db i18n success
2025-9-26 15:46:40-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:46:41-debug: refresh db assets success
2025-9-26 15:46:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:46:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:46:41-debug: asset-db:refresh-all-database (168ms)
2025-9-26 15:46:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:46:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:47:26-debug: refresh db internal success
2025-9-26 15:47:26-debug: refresh db i18n success
2025-9-26 15:47:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:47:26-debug: refresh db assets success
2025-9-26 15:47:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:47:26-debug: asset-db:refresh-all-database (132ms)
2025-9-26 15:47:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:47:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:47:29-debug: refresh db internal success
2025-9-26 15:47:29-debug: refresh db i18n success
2025-9-26 15:47:29-debug: refresh db assets success
2025-9-26 15:47:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:47:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:47:29-debug: asset-db:refresh-all-database (118ms)
2025-9-26 15:47:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:47:51-debug: refresh db internal success
2025-9-26 15:47:51-debug: refresh db i18n success
2025-9-26 15:47:51-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:47:51-debug: refresh db assets success
2025-9-26 15:47:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:47:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:47:51-debug: asset-db:refresh-all-database (141ms)
2025-9-26 15:47:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:47:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:47:51-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\path\p_01.json...
2025-9-26 15:47:51-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path\p_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:47:51-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave\path success
2025-9-26 15:47:51-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:48:54-debug: refresh db internal success
2025-9-26 15:48:54-debug: refresh db i18n success
2025-9-26 15:48:54-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:48:54-debug: refresh db assets success
2025-9-26 15:48:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:48:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:48:54-debug: asset-db:refresh-all-database (151ms)
2025-9-26 15:48:54-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-26 15:49:12-debug: refresh db internal success
2025-9-26 15:49:12-debug: refresh db i18n success
2025-9-26 15:49:12-debug: refresh db assets success
2025-9-26 15:49:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:49:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:49:12-debug: asset-db:refresh-all-database (118ms)
2025-9-26 15:49:14-debug: refresh db internal success
2025-9-26 15:49:14-debug: refresh db i18n success
2025-9-26 15:49:14-debug: refresh db assets success
2025-9-26 15:49:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:49:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:49:14-debug: asset-db:refresh-all-database (121ms)
2025-9-26 15:49:14-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-26 15:49:14-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-26 15:50:06-debug: refresh db internal success
2025-9-26 15:50:06-debug: refresh db i18n success
2025-9-26 15:50:07-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:50:07-debug: refresh db assets success
2025-9-26 15:50:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:50:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:50:07-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 15:50:07-debug: asset-db:refresh-all-database (162ms)
2025-9-26 15:50:07-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 15:50:11-debug: refresh db internal success
2025-9-26 15:50:11-debug: refresh db i18n success
2025-9-26 15:50:11-debug: refresh db assets success
2025-9-26 15:50:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:50:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:50:11-debug: asset-db:refresh-all-database (159ms)
2025-9-26 15:50:11-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 15:50:11-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 15:50:12-debug: refresh db internal success
2025-9-26 15:50:12-debug: refresh db i18n success
2025-9-26 15:50:12-debug: refresh db assets success
2025-9-26 15:50:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:50:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:50:12-debug: asset-db:refresh-all-database (121ms)
2025-9-26 15:50:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:50:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:50:14-debug: refresh db internal success
2025-9-26 15:50:14-debug: refresh db i18n success
2025-9-26 15:50:14-debug: refresh db assets success
2025-9-26 15:50:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:50:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:50:14-debug: asset-db:refresh-all-database (117ms)
2025-9-26 15:50:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:50:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 15:55:06-debug: refresh db internal success
2025-9-26 15:55:06-debug: refresh db i18n success
2025-9-26 15:55:06-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\SmoothnessTester.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:55:06-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:55:06-debug: refresh db assets success
2025-9-26 15:55:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:55:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:55:06-debug: asset-db:refresh-all-database (168ms)
2025-9-26 15:55:06-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 15:55:06-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-26 15:58:40-debug: refresh db internal success
2025-9-26 15:58:40-debug: refresh db i18n success
2025-9-26 15:58:40-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:58:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 15:58:40-debug: refresh db assets success
2025-9-26 15:58:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 15:58:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 15:58:40-debug: asset-db:refresh-all-database (157ms)
2025-9-26 15:58:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 15:58:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 16:00:11-debug: refresh db internal success
2025-9-26 16:00:11-debug: refresh db i18n success
2025-9-26 16:00:11-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 16:00:11-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 16:00:11-debug: refresh db assets success
2025-9-26 16:00:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 16:00:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 16:00:11-debug: asset-db:refresh-all-database (157ms)
2025-9-26 16:00:11-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 16:00:11-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 16:00:52-debug: refresh db internal success
2025-9-26 16:00:52-debug: refresh db i18n success
2025-9-26 16:00:52-debug: refresh db assets success
2025-9-26 16:00:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 16:00:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 16:00:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 16:00:52-debug: asset-db:refresh-all-database (116ms)
2025-9-26 16:00:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 16:00:58-debug: refresh db internal success
2025-9-26 16:00:58-debug: refresh db i18n success
2025-9-26 16:00:58-debug: refresh db assets success
2025-9-26 16:00:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 16:00:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 16:00:58-debug: asset-db:refresh-all-database (129ms)
2025-9-26 16:00:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 16:00:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 16:01:29-debug: refresh db internal success
2025-9-26 16:01:29-debug: refresh db i18n success
2025-9-26 16:01:29-debug: refresh db assets success
2025-9-26 16:01:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 16:01:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 16:01:29-debug: asset-db:refresh-all-database (126ms)
2025-9-26 16:03:18-debug: refresh db internal success
2025-9-26 16:03:18-debug: refresh db i18n success
2025-9-26 16:03:18-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 16:03:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 16:03:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 16:03:18-debug: refresh db assets success
2025-9-26 16:03:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 16:03:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 16:03:18-debug: asset-db:refresh-all-database (133ms)
2025-9-26 16:03:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 16:04:40-debug: refresh db internal success
2025-9-26 16:04:40-debug: refresh db i18n success
2025-9-26 16:04:40-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 16:04:40-debug: refresh db assets success
2025-9-26 16:04:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 16:04:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 16:04:40-debug: asset-db:refresh-all-database (132ms)
2025-9-26 16:04:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 16:04:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 16:06:14-debug: refresh db internal success
2025-9-26 16:06:14-debug: refresh db i18n success
2025-9-26 16:06:14-debug: refresh db assets success
2025-9-26 16:06:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 16:06:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 16:06:14-debug: asset-db:refresh-all-database (165ms)
2025-9-26 16:09:41-debug: refresh db internal success
2025-9-26 16:09:41-debug: refresh db i18n success
2025-9-26 16:09:41-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 16:09:41-debug: refresh db assets success
2025-9-26 16:09:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 16:09:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 16:09:41-debug: asset-db:refresh-all-database (171ms)
2025-9-26 16:09:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 16:09:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 16:10:31-debug: refresh db internal success
2025-9-26 16:10:31-debug: refresh db i18n success
2025-9-26 16:10:31-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 16:10:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 16:10:31-debug: refresh db assets success
2025-9-26 16:10:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 16:10:31-debug: asset-db:refresh-all-database (162ms)
2025-9-26 16:10:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 16:10:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 16:20:16-debug: refresh db internal success
2025-9-26 16:20:16-debug: refresh db i18n success
2025-9-26 16:20:16-debug: refresh db assets success
2025-9-26 16:20:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 16:20:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 16:20:16-debug: asset-db:refresh-all-database (172ms)
2025-9-26 16:20:16-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 16:20:16-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 16:20:31-debug: refresh db internal success
2025-9-26 16:20:31-debug: refresh db i18n success
2025-9-26 16:20:31-debug: refresh db assets success
2025-9-26 16:20:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 16:20:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 16:20:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 16:20:31-debug: asset-db:refresh-all-database (118ms)
2025-9-26 16:20:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
