System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec2, EDITOR, PathData, PathPoint, _dec, _dec2, _dec3, _class, _crd, ccclass, executeInEditMode, menu, SmoothnessTester;

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec2 = _cc.Vec2;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
      PathPoint = _unresolved_2.PathPoint;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6f991KS7bJFGag9Czp33zJQ", "SmoothnessTester", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Vec2']);

      ({
        ccclass,
        executeInEditMode,
        menu
      } = _decorator);
      /**
       * 平滑度测试器 - 验证smoothness=0时是否为直线
       */

      _export("SmoothnessTester", SmoothnessTester = (_dec = ccclass('SmoothnessTester'), _dec2 = menu("怪物/编辑器/平滑度测试器"), _dec3 = executeInEditMode(true), _dec(_class = _dec2(_class = _dec3(_class = class SmoothnessTester extends Component {
        onLoad() {
          if (EDITOR) {
            this.testSmoothness();
          }
        }

        testSmoothness() {
          console.log("=== 平滑度测试开始 ==="); // 测试1: smoothness = 0 应该产生直线

          this.testLinearBehavior(); // 测试2: smoothness = 1 应该产生平滑曲线

          this.testSmoothBehavior(); // 测试3: 混合平滑度测试

          this.testMixedSmoothness();
          console.log("=== 平滑度测试结束 ===");
        }

        testLinearBehavior() {
          console.log("测试1: smoothness = 0 (直线)");
          const points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 100), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0)]; // 设置所有点的平滑度为0

          points.forEach(p => p.smoothness = 0);
          const pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          pathData.points = points;
          pathData.segments = 10;
          const curvePoints = pathData.generateCurvePoints(); // 验证是否为直线：检查中间点是否在两端点的连线上

          this.validateLinearSegments(curvePoints, points);
        }

        testSmoothBehavior() {
          console.log("测试2: smoothness = 1 (最平滑)");
          const points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-100, 0), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 100), new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0)]; // 设置所有点的平滑度为1

          points.forEach(p => p.smoothness = 1);
          const pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          pathData.points = points;
          pathData.segments = 10;
          const curvePoints = pathData.generateCurvePoints();
          console.log(`生成了 ${curvePoints.length} 个曲线点`);
          console.log("第一个点:", curvePoints[0]);
          console.log("中间点:", curvePoints[Math.floor(curvePoints.length / 2)]);
          console.log("最后一个点:", curvePoints[curvePoints.length - 1]);
        }

        testMixedSmoothness() {
          console.log("测试3: 混合平滑度");
          const points = [new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-150, 0), // smoothness = 0.5
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(-50, 100), // smoothness = 0 (直线)
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(50, -100), // smoothness = 0 (直线)
          new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(150, 0) // smoothness = 0.5
          ];
          points[0].smoothness = 0.5;
          points[1].smoothness = 0; // 这个点应该形成直线连接

          points[2].smoothness = 0; // 这个点应该形成直线连接

          points[3].smoothness = 0.5;
          const pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          pathData.points = points;
          pathData.segments = 20;
          const curvePoints = pathData.generateCurvePoints();
          console.log(`混合平滑度测试生成了 ${curvePoints.length} 个点`);
        }

        validateLinearSegments(curvePoints, originalPoints) {
          const tolerance = 0.1; // 允许的误差

          let isLinear = true; // 检查每一段是否为直线

          for (let i = 0; i < originalPoints.length - 1; i++) {
            const startPoint = originalPoints[i].position;
            const endPoint = originalPoints[i + 1].position; // 找到这一段对应的曲线点

            const segmentStart = i * 10; // 假设每段10个点

            const segmentEnd = Math.min((i + 1) * 10, curvePoints.length - 1);

            for (let j = segmentStart; j <= segmentEnd; j++) {
              const curvePoint = curvePoints[j];
              const t = (j - segmentStart) / (segmentEnd - segmentStart); // 计算理论上的直线点

              const expectedLinearPoint = Vec2.lerp(new Vec2(), startPoint, endPoint, t); // 检查距离

              const distance = Vec2.distance(curvePoint, expectedLinearPoint);

              if (distance > tolerance) {
                console.warn(`点 ${j} 偏离直线，距离: ${distance.toFixed(3)}`);
                isLinear = false;
              }
            }
          }

          if (isLinear) {
            console.log("✅ 验证通过：smoothness=0时确实产生直线");
          } else {
            console.log("❌ 验证失败：smoothness=0时没有产生直线");
          }

          return isLinear;
        }
        /**
         * 手动测试单个插值点
         */


        testSinglePoint() {
          const p0 = new Vec2(-100, 0);
          const p1 = new Vec2(0, 100);
          const p2 = new Vec2(100, 0);
          const p3 = new Vec2(200, 100);
          console.log("=== 单点插值测试 ==="); // 测试t=0.5时的不同smoothness值

          for (let smoothness = 0; smoothness <= 1; smoothness += 0.25) {
            const result = (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
              error: Error()
            }), PathData) : PathData).catmullRomPoint(0.5, p0, p1, p2, p3, smoothness);
            console.log(`smoothness=${smoothness}: (${result.x.toFixed(2)}, ${result.y.toFixed(2)})`);
          } // 验证smoothness=0时是否为线性插值


          const linearResult = Vec2.lerp(new Vec2(), p1, p2, 0.5);
          const smoothness0Result = (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData).catmullRomPoint(0.5, p0, p1, p2, p3, 0);
          const distance = Vec2.distance(linearResult, smoothness0Result);
          console.log(`线性插值结果: (${linearResult.x}, ${linearResult.y})`);
          console.log(`smoothness=0结果: (${smoothness0Result.x}, ${smoothness0Result.y})`);
          console.log(`距离差: ${distance}`);

          if (distance < 0.001) {
            console.log("✅ smoothness=0确实等于线性插值");
          } else {
            console.log("❌ smoothness=0不等于线性插值");
          }
        }

      }) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=91ebcc4a94536e71b9f6d11623164ccd3539b531.js.map