"[\n  {\n    \"__type__\": \"cc.SceneAsset\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_native\": \"\",\n    \"scene\": {\n      \"__id__\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.Scene\",\n    \"_name\": \"PathEditor\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": null,\n    \"_children\": [\n      {\n        \"__id__\": 2\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"autoReleaseAssets\": false,\n    \"_globals\": {\n      \"__id__\": 32\n    },\n    \"_id\": \"f87e7412-cf27-4eb5-a664-0071cad67cf8\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Canvas\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 1\n    },\n    \"_children\": [\n      {\n        \"__id__\": 3\n      },\n      {\n        \"__id__\": 5\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 29\n      },\n      {\n        \"__id__\": 30\n      },\n      {\n        \"__id__\": 31\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 375,\n      \"y\": 667,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"faDU1dP+NH/7LftrOeHnar\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Camera\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 4\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 1000\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"30ULKOv+FFHZjbW134mFVm\"\n  },\n  {\n    \"__type__\": \"cc.Camera\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 3\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_projection\": 0,\n    \"_priority\": 1073741824,\n    \"_fov\": 45,\n    \"_fovAxis\": 0,\n    \"_orthoHeight\": 667,\n    \"_near\": 1,\n    \"_far\": 2000,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_depth\": 1,\n    \"_stencil\": 0,\n    \"_clearFlags\": 6,\n    \"_rect\": {\n      \"__type__\": \"cc.Rect\",\n      \"x\": 0,\n      \"y\": 0,\n      \"width\": 1,\n      \"height\": 1\n    },\n    \"_aperture\": 19,\n    \"_shutter\": 7,\n    \"_iso\": 0,\n    \"_screenScale\": 1,\n    \"_visibility\": 41943040,\n    \"_targetTexture\": null,\n    \"_postProcess\": null,\n    \"_usePostProcess\": false,\n    \"_cameraType\": -1,\n    \"_trackingType\": 0,\n    \"_id\": \"4dddk0EUJMjLKoR1z1H7DR\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"PathEditor\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 6\n      },\n      {\n        \"__id__\": 10\n      },\n      {\n        \"__id__\": 14\n      },\n      {\n        \"__id__\": 18\n      },\n      {\n        \"__id__\": 22\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 26\n      },\n      {\n        \"__id__\": 27\n      },\n      {\n        \"__id__\": 28\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -179.372,\n      \"y\": 212.705,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d8eQW7RQBMSbAN/eEfWQWW\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Point_0\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 5\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 7\n      },\n      {\n        \"__id__\": 8\n      },\n      {\n        \"__id__\": 9\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -2.384,\n      \"y\": 623.564,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b0Mlep4K9H5bTq2joMot/B\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 6\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"efxvGP+CdBNooc08oZNXRH\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 6\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 3,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"92U5MMD6VFHKqge6TV224b\"\n  },\n  {\n    \"__type__\": \"77022mnugRFv4miHgaxYrGl\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 6\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"f7hd0mi+FMGbxCFUp/i5Fp\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Point_1\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 5\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 11\n      },\n      {\n        \"__id__\": 12\n      },\n      {\n        \"__id__\": 13\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -170.895,\n      \"y\": 333.816,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"08zx7sGWtFS5gpHmfY5OYp\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 10\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"70X+G82ltBNo0knSnJy+JL\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 10\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 3,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 255,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"1cbGdSl9hEOrNqyvyj7KTP\"\n  },\n  {\n    \"__type__\": \"77022mnugRFv4miHgaxYrGl\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 10\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"fc4Ee/9BtI2rNL2k/gdJ3m\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Point_2\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 5\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 15\n      },\n      {\n        \"__id__\": 16\n      },\n      {\n        \"__id__\": 17\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -100.109,\n      \"y\": 33.978,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"48S4MOrjVNtZ7an6JVm8uT\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 14\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"34a+kZCCFHn7Vlmjt3dmP9\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 14\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 3,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"d7nwZBnUFOFrpLBw7t6Dqf\"\n  },\n  {\n    \"__type__\": \"77022mnugRFv4miHgaxYrGl\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 14\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"d7N+ItIrVA2pU292HMYzco\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Point_3\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 5\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 19\n      },\n      {\n        \"__id__\": 20\n      },\n      {\n        \"__id__\": 21\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 45.257,\n      \"y\": -166.936,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"0bvBRLQRlD8bMHmgLtW7kj\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 18\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"02YuuV/tVFLb3b+d9ba26R\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 18\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 3,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"abK+/0a01MbocnzoeUgQqr\"\n  },\n  {\n    \"__type__\": \"77022mnugRFv4miHgaxYrGl\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 18\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"62nDQcKzBMi7OeZ1zJwVii\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Point_4\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 5\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 23\n      },\n      {\n        \"__id__\": 24\n      },\n      {\n        \"__id__\": 25\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 737.273,\n      \"y\": -456.585,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"50CbkMbWFDWoRDVNfvLOjO\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 22\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"00GTderTJCbYELr3dLu+Hd\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 22\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 3,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"8fatNNgs9LNpTyYm88aCsZ\"\n  },\n  {\n    \"__type__\": \"77022mnugRFv4miHgaxYrGl\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 22\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"2e7WVxkr9MJ5HpRGMqOfuM\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"72E9gcDKdKQ57rpzbQZNrq\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 3,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"f0TQvpKx1MqrLFiFfX82s4\"\n  },\n  {\n    \"__type__\": \"3b164ESPc1DLJK8SqXjxSO0\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"curveColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_id\": \"c3zfAMYhxLJYgc6Yz4naAM\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 750,\n      \"height\": 1334\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"877MyVSH5HT4ua4aw8hv3u\"\n  },\n  {\n    \"__type__\": \"cc.Canvas\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_cameraComponent\": {\n      \"__id__\": 4\n    },\n    \"_alignCanvasWithScreen\": true,\n    \"_id\": \"d62tTthv1JvoSFwcjsZlx9\"\n  },\n  {\n    \"__type__\": \"cc.Widget\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_alignFlags\": 45,\n    \"_target\": null,\n    \"_left\": 0,\n    \"_right\": 0,\n    \"_top\": 0,\n    \"_bottom\": 0,\n    \"_horizontalCenter\": 0,\n    \"_verticalCenter\": 0,\n    \"_isAbsLeft\": true,\n    \"_isAbsRight\": true,\n    \"_isAbsTop\": true,\n    \"_isAbsBottom\": true,\n    \"_isAbsHorizontalCenter\": true,\n    \"_isAbsVerticalCenter\": true,\n    \"_originalWidth\": 0,\n    \"_originalHeight\": 0,\n    \"_alignMode\": 2,\n    \"_lockFlags\": 0,\n    \"_id\": \"85bL1fMaJJ3bPnFIT6qe5w\"\n  },\n  {\n    \"__type__\": \"cc.SceneGlobals\",\n    \"ambient\": {\n      \"__id__\": 33\n    },\n    \"shadows\": {\n      \"__id__\": 34\n    },\n    \"_skybox\": {\n      \"__id__\": 35\n    },\n    \"fog\": {\n      \"__id__\": 36\n    },\n    \"octree\": {\n      \"__id__\": 37\n    },\n    \"skin\": {\n      \"__id__\": 38\n    },\n    \"lightProbeInfo\": {\n      \"__id__\": 39\n    },\n    \"postSettings\": {\n      \"__id__\": 40\n    },\n    \"bakedWithStationaryMainLight\": false,\n    \"bakedWithHighpLightmap\": false\n  },\n  {\n    \"__type__\": \"cc.AmbientInfo\",\n    \"_skyColorHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.5,\n      \"z\": 0.8,\n      \"w\": 0.520833125\n    },\n    \"_skyColor\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.5,\n      \"z\": 0.8,\n      \"w\": 0.520833125\n    },\n    \"_skyIllumHDR\": 20000,\n    \"_skyIllum\": 20000,\n    \"_groundAlbedoHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.2,\n      \"z\": 0.2,\n      \"w\": 1\n    },\n    \"_groundAlbedo\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.2,\n      \"z\": 0.2,\n      \"w\": 1\n    },\n    \"_skyColorLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.452588,\n      \"y\": 0.607642,\n      \"z\": 0.755699,\n      \"w\": 0\n    },\n    \"_skyIllumLDR\": 0.8,\n    \"_groundAlbedoLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.618555,\n      \"y\": 0.577848,\n      \"z\": 0.544564,\n      \"w\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.ShadowsInfo\",\n    \"_enabled\": false,\n    \"_type\": 0,\n    \"_normal\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 1,\n      \"z\": 0\n    },\n    \"_distance\": 0,\n    \"_planeBias\": 1,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 76,\n      \"g\": 76,\n      \"b\": 76,\n      \"a\": 255\n    },\n    \"_maxReceived\": 4,\n    \"_size\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1024,\n      \"y\": 1024\n    }\n  },\n  {\n    \"__type__\": \"cc.SkyboxInfo\",\n    \"_envLightingType\": 0,\n    \"_envmapHDR\": {\n      \"__uuid__\": \"d032ac98-05e1-4090-88bb-eb640dcb5fc1@b47c0\",\n      \"__expectedType__\": \"cc.TextureCube\"\n    },\n    \"_envmap\": {\n      \"__uuid__\": \"d032ac98-05e1-4090-88bb-eb640dcb5fc1@b47c0\",\n      \"__expectedType__\": \"cc.TextureCube\"\n    },\n    \"_envmapLDR\": {\n      \"__uuid__\": \"6f01cf7f-81bf-4a7e-bd5d-0afc19696480@b47c0\",\n      \"__expectedType__\": \"cc.TextureCube\"\n    },\n    \"_diffuseMapHDR\": null,\n    \"_diffuseMapLDR\": null,\n    \"_enabled\": true,\n    \"_useHDR\": true,\n    \"_editableMaterial\": null,\n    \"_reflectionHDR\": null,\n    \"_reflectionLDR\": null,\n    \"_rotationAngle\": 0\n  },\n  {\n    \"__type__\": \"cc.FogInfo\",\n    \"_type\": 0,\n    \"_fogColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 200,\n      \"g\": 200,\n      \"b\": 200,\n      \"a\": 255\n    },\n    \"_enabled\": false,\n    \"_fogDensity\": 0.3,\n    \"_fogStart\": 0.5,\n    \"_fogEnd\": 300,\n    \"_fogAtten\": 5,\n    \"_fogTop\": 1.5,\n    \"_fogRange\": 1.2,\n    \"_accurate\": false\n  },\n  {\n    \"__type__\": \"cc.OctreeInfo\",\n    \"_enabled\": false,\n    \"_minPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -1024,\n      \"y\": -1024,\n      \"z\": -1024\n    },\n    \"_maxPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1024,\n      \"y\": 1024,\n      \"z\": 1024\n    },\n    \"_depth\": 8\n  },\n  {\n    \"__type__\": \"cc.SkinInfo\",\n    \"_enabled\": true,\n    \"_blurRadius\": 0.01,\n    \"_sssIntensity\": 3\n  },\n  {\n    \"__type__\": \"cc.LightProbeInfo\",\n    \"_giScale\": 1,\n    \"_giSamples\": 1024,\n    \"_bounces\": 2,\n    \"_reduceRinging\": 0,\n    \"_showProbe\": true,\n    \"_showWireframe\": true,\n    \"_showConvex\": false,\n    \"_data\": null,\n    \"_lightProbeSphereVolume\": 1\n  },\n  {\n    \"__type__\": \"cc.PostSettingsInfo\",\n    \"_toneMappingType\": 0\n  }\n]"