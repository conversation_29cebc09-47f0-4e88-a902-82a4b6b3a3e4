# PathMovable 组件

## 概述
PathMovable是一个专门用于路径移动的组件，类似于Movable但专注于沿预定义路径移动。它实现了IMovable接口，提供了高性能的路径数据缓存机制。

## 主要特性

### 🚀 性能优化
- **路径数据缓存**: 同一个路径资源只生成一次curvePoints，所有组件共享
- **智能可见性检查**: 降低检查频率，提高性能
- **内存友好**: 避免重复计算和存储路径数据

### 🎮 移动功能
- **路径跟随**: 沿Catmull-Rom曲线平滑移动
- **速度控制**: 支持恒定速度和加速度
- **循环移动**: 支持循环和往返移动
- **反向移动**: 支持反向沿路径移动

### 🎨 视觉效果
- **自动朝向**: 自动面向移动方向
- **倾斜振荡**: 支持垂直于移动方向的振荡效果
- **朝向偏移**: 支持图片默认朝向设置

### 📡 事件系统
- **可见性事件**: onBecomeVisible / onBecomeInvisible
- **链式调用**: 支持流畅的API调用

## 与Movable的对比

| 特性 | Movable | PathMovable |
|------|---------|-------------|
| 移动方式 | 自由移动+追踪 | 沿路径移动 |
| 追踪功能 | ✅ 支持目标追踪 | ❌ 无追踪功能 |
| 路径移动 | ❌ 不支持 | ✅ 专门优化 |
| 性能 | 一般 | 高（缓存优化） |
| 倾斜振荡 | ✅ 支持 | ✅ 支持 |
| 事件系统 | ✅ 支持 | ✅ 支持 |

## 使用方法

### 基本设置
```typescript
// 获取PathMovable组件
const pathMovable = node.getComponent(PathMovable);

// 设置基本属性
pathMovable.speed = 200;           // 移动速度
pathMovable.loop = true;           // 循环移动
pathMovable.isFacingMoveDir = true; // 朝向移动方向
```

### 路径控制
```typescript
// 设置进度
pathMovable.setProgress(0.5);     // 移动到路径中点

// 重置位置
pathMovable.resetToStart();       // 回到起点
pathMovable.moveToEnd();          // 移动到终点

// 获取当前状态
const progress = pathMovable.getProgress(); // 获取当前进度
const pointData = pathMovable.getCurrentPathPointData(); // 获取当前路径点数据
```

### 高级效果
```typescript
// 倾斜振荡效果
pathMovable.tiltSpeed = 2.0;      // 振荡频率
pathMovable.tiltOffset = 20;      // 振荡幅度

// 加速度
pathMovable.acceleration = 50;    // 加速度

// 反向移动
pathMovable.reverse = true;       // 反向沿路径移动
```

### 事件监听
```typescript
// 监听可见性变化
pathMovable.on(eMoveEvent.onBecomeVisible, () => {
    console.log("对象变为可见");
});

pathMovable.on(eMoveEvent.onBecomeInvisible, () => {
    console.log("对象变为不可见");
});
```

## 路径数据缓存机制

### 缓存原理
```typescript
class PathDataCache {
    // 使用JsonAsset的UUID作为缓存键
    private static _cache = new Map<string, CachedPathData>();
    
    // 第一次加载时生成并缓存
    // 后续相同路径的组件直接使用缓存数据
}
```

### 缓存优势
1. **内存节省**: 相同路径的多个对象共享曲线数据
2. **CPU节省**: 避免重复的generateCurvePoints计算
3. **加载速度**: 后续组件瞬间完成路径数据加载

### 缓存管理
```typescript
// 清除所有缓存（在场景切换时调用）
PathMovable.clearPathCache();
```

## 性能特性

### 优化点
1. **路径数据共享**: 同一路径资源只计算一次
2. **可见性检查优化**: 每5帧检查一次而非每帧
3. **距离计算缓存**: 预计算所有距离信息
4. **内存复用**: 使用对象池模式的Vec2操作

### 适用场景
- **敌机移动**: 大量敌机沿相同路径移动
- **弹幕系统**: 复杂弹幕轨迹
- **UI动画**: 界面元素路径动画
- **特效系统**: 粒子轨迹效果

## 最佳实践

### 1. 路径资源管理
```typescript
// 推荐：为不同类型的移动创建专门的路径资源
// assets/resources/paths/enemy/
//   ├── wave_pattern.json
//   ├── spiral_pattern.json
//   └── zigzag_pattern.json
```

### 2. 性能优化
```typescript
// 在场景结束时清理缓存
onDestroy() {
    PathMovable.clearPathCache();
}

// 批量创建时复用路径资源
const sharedPathAsset = this.wavePathAsset;
enemies.forEach(enemy => {
    const pathMovable = enemy.getComponent(PathMovable);
    pathMovable.pathAsset = sharedPathAsset; // 共享同一个路径资源
});
```

### 3. 动态控制
```typescript
// 根据游戏状态动态调整移动参数
if (gameState.isSlowMotion) {
    pathMovable.speed *= 0.5;
}

// 根据难度调整振荡效果
pathMovable.tiltSpeed = difficulty * 1.5;
pathMovable.tiltOffset = difficulty * 10;
```

## 调试技巧

### 1. 进度监控
```typescript
// 在update中监控移动进度
console.log(`Progress: ${pathMovable.getProgress().toFixed(2)}`);
```

### 2. 路径点数据检查
```typescript
// 检查当前路径点的属性
const pointData = pathMovable.getCurrentPathPointData();
if (pointData) {
    console.log(`Speed: ${pointData.speed}, Orientation: ${pointData.orientationType}`);
}
```

### 3. 缓存状态检查
```typescript
// 检查缓存是否生效（第一个组件会生成缓存，后续组件应该直接使用）
// 在loadPathData方法中添加日志即可验证
```

## 注意事项

1. **路径资源**: 确保pathAsset正确设置
2. **缓存清理**: 在适当时机清理缓存避免内存泄漏
3. **坐标系**: 使用Cocos Creator的坐标系统
4. **性能监控**: 大量对象时注意性能表现

PathMovable提供了高效、灵活的路径移动解决方案，特别适合需要大量对象沿相同路径移动的场景。
