System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, Color, Component, JsonAsset, CCInteger, Graphics, PathData, PathPoint, PathPointEditor, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _class, _class2, _descriptor, _crd, ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent, PathEditor;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPointEditor(extras) {
    _reporterNs.report("PathPointEditor", "./PathPointEditor", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      Color = _cc.Color;
      Component = _cc.Component;
      JsonAsset = _cc.JsonAsset;
      CCInteger = _cc.CCInteger;
      Graphics = _cc.Graphics;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
      PathPoint = _unresolved_2.PathPoint;
    }, function (_unresolved_3) {
      PathPointEditor = _unresolved_3.PathPointEditor;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3b164ESPc1DLJK8SqXjxSO0", "PathEditor", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Color', 'Component', 'JsonAsset', 'CCInteger', 'Graphics']);

      ({
        ccclass,
        executeInEditMode,
        property,
        disallowMultiple,
        menu,
        requireComponent
      } = _decorator);

      _export("PathEditor", PathEditor = (_dec = ccclass('PathEditor'), _dec2 = menu("怪物/编辑器/路径编辑"), _dec3 = requireComponent(Graphics), _dec4 = executeInEditMode(true), _dec5 = disallowMultiple(true), _dec6 = property({
        type: JsonAsset,
        displayName: "路径数据"
      }), _dec7 = property({
        displayName: "路径名称"
      }), _dec8 = property({
        type: CCInteger,
        displayName: '起始点'
      }), _dec9 = property({
        type: CCInteger,
        displayName: '结束点(-1代表默认最后个点)'
      }), _dec10 = property({
        displayName: "是否闭合",

        visible() {
          // @ts-ignore
          return this._pathDataObj.points.length >= 3;
        }

      }), _dec11 = property({
        displayName: "曲线颜色"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = _dec5(_class = (_class2 = class PathEditor extends Component {
        constructor(...args) {
          super(...args);
          this._graphics = null;

          _initializerDefineProperty(this, "curveColor", _descriptor, this);

          this._showDirectionArrow = true;
          this._pathData = null;
          this._pathDataObj = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          this._cachedChildrenCount = 0;
        }

        get graphics() {
          if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
          }

          return this._graphics;
        }

        set pathData(value) {
          this._pathData = value;
          this.reload();
        }

        get pathData() {
          return this._pathData;
        }

        get pathName() {
          return this._pathDataObj.name;
        }

        set pathName(value) {
          this._pathDataObj.name = value;
        }

        get startIdx() {
          return this._pathDataObj.startIdx;
        }

        set startIdx(value) {
          this._pathDataObj.startIdx = value;
        }

        get endIdx() {
          return this._pathDataObj.endIdx;
        }

        set endIdx(value) {
          this._pathDataObj.endIdx = value;
        }

        get isClosed() {
          return this._pathDataObj.closed;
        }

        set isClosed(value) {
          this._pathDataObj.closed = value;
        }

        reload() {
          if (!this._pathData) return;
          const pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          Object.assign(pathData, this._pathData.json);
          this._pathDataObj = pathData;
          this.node.removeAllChildren();

          if (this._pathDataObj && this._pathDataObj.points.length > 0) {
            this._pathDataObj.points.forEach(point => {
              this.addPoint(point);
            });
          }

          this.updateCurve();
        }

        save() {
          // 收集所有路径点数据
          const pointEditors = this.getComponentsInChildren(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          this._pathDataObj.points = pointEditors.map(editor => editor.pathPoint);
          return JSON.stringify(this._pathDataObj, null, 2);
        }

        addPoint(point) {
          const pointNode = new Node();
          pointNode.parent = this.node;
          pointNode.setPosition(point.x, point.y, 0);
          const pointEditor = pointNode.addComponent(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          pointEditor.pathPoint = point;
        }

        addNewPoint(x, y) {
          const point = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(x, y);
          this.addPoint(point);
          this.updateCurve();
        }

        updateCurve() {
          // 收集当前所有点的数据
          const pointEditors = this.getComponentsInChildren(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          this._pathDataObj.points = pointEditors.map(editor => editor.pathPoint);
        }

        drawPath() {
          const graphics = this.graphics;
          graphics.clear();
          if (this._pathDataObj.points.length < 2) return; // 绘制Catmull-Rom曲线

          graphics.strokeColor = this.curveColor;
          graphics.lineWidth = 5;

          const curvePoints = this._pathDataObj.generateCurvePoints(true);

          if (curvePoints.length > 1) {
            graphics.moveTo(curvePoints[0].x, curvePoints[0].y);

            for (let i = 1; i < curvePoints.length; i++) {
              graphics.lineTo(curvePoints[i].x, curvePoints[i].y);
            } // 如果是闭合路径，连接回起点


            if (this._pathDataObj.closed) {
              graphics.lineTo(curvePoints[0].x, curvePoints[0].y);
            }

            graphics.stroke(); // 绘制路径终点的方向箭头（仅对非闭合路径）

            if (this._showDirectionArrow && !this._pathDataObj.closed) {
              this.drawPathDirectionArrow(graphics, curvePoints);
            }
          }
        }

        update(_dt) {
          const childrenCount = this.node.children.length;

          if (childrenCount !== this._cachedChildrenCount) {
            this._cachedChildrenCount = childrenCount;
          }

          this.updateCurve();
          this.drawPath();
        }
        /**
         * 绘制路径方向箭头
         */


        drawPathDirectionArrow(graphics, curvePoints) {
          if (curvePoints.length < 2) return; // 如果是闭合路径，不绘制箭头（因为没有明确的终点）

          if (this._pathDataObj.closed) return; // 计算终点的方向（使用最后几个点来获得更准确的方向）

          const endPoint = curvePoints[curvePoints.length - 1];
          let prevPoint = curvePoints[curvePoints.length - 2]; // 如果有足够的点，使用更远的点来计算方向，获得更平滑的方向

          if (curvePoints.length >= 5) {
            prevPoint = curvePoints[curvePoints.length - 5];
          } // 计算方向角度


          const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x); // 箭头参数

          const arrowLength = 40;
          const arrowHeadLength = 20;
          const arrowHeadAngle = Math.PI / 5; // 36度，更尖锐的箭头
          // 设置箭头样式

          graphics.strokeColor = Color.RED;
          graphics.fillColor = Color.RED;
          graphics.lineWidth = 3; // 计算箭头起点（从路径终点开始）

          const arrowStartX = endPoint.x;
          const arrowStartY = endPoint.y; // 计算箭头终点

          const arrowEndX = arrowStartX + Math.cos(direction) * arrowLength;
          const arrowEndY = arrowStartY + Math.sin(direction) * arrowLength; // 绘制箭头主线

          graphics.moveTo(arrowStartX, arrowStartY);
          graphics.lineTo(arrowEndX, arrowEndY);
          graphics.stroke(); // 绘制箭头头部（填充三角形）

          const leftX = arrowEndX - Math.cos(direction - arrowHeadAngle) * arrowHeadLength;
          const leftY = arrowEndY - Math.sin(direction - arrowHeadAngle) * arrowHeadLength;
          const rightX = arrowEndX - Math.cos(direction + arrowHeadAngle) * arrowHeadLength;
          const rightY = arrowEndY - Math.sin(direction + arrowHeadAngle) * arrowHeadLength; // 绘制填充的箭头头部

          graphics.moveTo(arrowEndX, arrowEndY);
          graphics.lineTo(leftX, leftY);
          graphics.lineTo(rightX, rightY);
          graphics.close();
          graphics.fill();
          graphics.stroke();
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "pathData", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "pathData"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "pathName", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "pathName"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "startIdx", [_dec8], Object.getOwnPropertyDescriptor(_class2.prototype, "startIdx"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "endIdx", [_dec9], Object.getOwnPropertyDescriptor(_class2.prototype, "endIdx"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "isClosed", [_dec10], Object.getOwnPropertyDescriptor(_class2.prototype, "isClosed"), _class2.prototype), _descriptor = _applyDecoratedDescriptor(_class2.prototype, "curveColor", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return Color.WHITE;
        }
      })), _class2)) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b09cd97fe2cb43a7b5c53aaa1e3a7c89d31e63fc.js.map