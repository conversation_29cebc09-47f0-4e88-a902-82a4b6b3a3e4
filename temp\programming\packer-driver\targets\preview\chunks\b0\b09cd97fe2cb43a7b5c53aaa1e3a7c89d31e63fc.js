System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, Color, Component, JsonAsset, Vec3, Graphics, input, Input, EventMouse, EDITOR, PathData, PathPoint, PathPointEditor, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent, PathEditor;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPointEditor(extras) {
    _reporterNs.report("PathPointEditor", "./PathPointEditor", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      Color = _cc.Color;
      Component = _cc.Component;
      JsonAsset = _cc.JsonAsset;
      Vec3 = _cc.Vec3;
      Graphics = _cc.Graphics;
      input = _cc.input;
      Input = _cc.Input;
      EventMouse = _cc.EventMouse;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
      PathPoint = _unresolved_2.PathPoint;
    }, function (_unresolved_3) {
      PathPointEditor = _unresolved_3.PathPointEditor;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3b164ESPc1DLJK8SqXjxSO0", "PathEditor", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Color', 'Component', 'JsonAsset', 'Vec3', 'Graphics', 'input', 'Input', 'EventMouse']);

      ({
        ccclass,
        executeInEditMode,
        property,
        disallowMultiple,
        menu,
        requireComponent
      } = _decorator);

      _export("PathEditor", PathEditor = (_dec = ccclass('PathEditor'), _dec2 = menu("怪物/编辑器/路径编辑"), _dec3 = requireComponent(Graphics), _dec4 = executeInEditMode(true), _dec5 = disallowMultiple(true), _dec6 = property({
        type: JsonAsset,
        displayName: "路径数据"
      }), _dec7 = property({
        displayName: "路径名称"
      }), _dec8 = property({
        displayName: "显示曲线"
      }), _dec9 = property({
        displayName: "显示控制点"
      }), _dec10 = property({
        displayName: "曲线颜色"
      }), _dec11 = property({
        displayName: "控制线颜色"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = _dec5(_class = (_class2 = class PathEditor extends Component {
        constructor() {
          super(...arguments);
          this._graphics = null;

          _initializerDefineProperty(this, "showCurve", _descriptor, this);

          _initializerDefineProperty(this, "showControlPoints", _descriptor2, this);

          _initializerDefineProperty(this, "curveColor", _descriptor3, this);

          _initializerDefineProperty(this, "controlLineColor", _descriptor4, this);

          this._pathData = null;
          this._pathDataObj = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          this._selectedPoint = null;
        }

        get graphics() {
          if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
          }

          return this._graphics;
        }

        set pathData(value) {
          this._pathData = value;
          this.reload();
        }

        get pathData() {
          return this._pathData;
        }

        get pathName() {
          return this._pathDataObj.name;
        }

        set pathName(value) {
          this._pathDataObj.name = value;
        }

        onLoad() {
          if (EDITOR) {
            this.setupEditorEvents();
          }
        }

        onDestroy() {
          if (EDITOR) {
            this.removeEditorEvents();
          }
        }

        setupEditorEvents() {
          if (EDITOR) {
            input.on(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);
            input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
          }
        }

        removeEditorEvents() {
          if (EDITOR) {
            input.off(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);
            input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
          }
        }

        onMouseDown(event) {
          if (!EDITOR) return; // 右键添加新点

          if (event.getButton() === EventMouse.BUTTON_RIGHT) {
            var screenPos = event.getLocation();
            var worldPos = this.screenToWorld(new Vec3(screenPos.x, screenPos.y, 0)); // 转换为本地坐标

            var localPos = worldPos;

            if (this.node.parent) {
              this.node.parent.inverseTransformPoint(worldPos, localPos);
            }

            this.addNewPoint(localPos.x, localPos.y, localPos.z);
          }
        }

        onKeyDown(event) {
          if (!EDITOR || !this._selectedPoint) return; // 删除选中的点

          if (event.keyCode === 46) {
            // Delete key
            this.removePoint(this._selectedPoint);
          }
        }

        screenToWorld(screenPos) {
          // 简化的屏幕到世界坐标转换，实际项目中可能需要更复杂的实现
          return screenPos;
        }

        reload() {
          if (!this._pathData) return;
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          Object.assign(pathData, this._pathData.json);
          this._pathDataObj = pathData;
          this.node.removeAllChildren();

          if (this._pathDataObj && this._pathDataObj.points.length > 0) {
            this._pathDataObj.points.forEach(point => {
              this.addPoint(point);
            });
          }

          this.updateCurve();
        }

        save() {
          // 收集所有路径点数据
          var pointEditors = this.getComponentsInChildren(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          this._pathDataObj.points = pointEditors.map(editor => editor.pathPoint);
          return JSON.stringify(this._pathDataObj, null, 2);
        }

        addPoint(point) {
          var pointNode = new Node();
          pointNode.parent = this.node;
          pointNode.position = point.position;
          var pointEditor = pointNode.addComponent(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          pointEditor.pathPoint = point;
        }

        addNewPoint(x, y, z) {
          if (z === void 0) {
            z = 0;
          }

          var point = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(x, y, z);
          this.addPoint(point);
          this.updateCurve();
        }

        removePoint(pointEditor) {
          if (this._selectedPoint === pointEditor) {
            this._selectedPoint = null;
          }

          pointEditor.node.destroy();
          this.updateCurve();
        }

        selectPoint(pointEditor) {
          // 取消之前选中的点
          if (this._selectedPoint && this._selectedPoint !== pointEditor) {
            this._selectedPoint.selected = false;
          }

          this._selectedPoint = pointEditor;
        }

        updateCurve() {
          if (!this.showCurve) return; // 收集当前所有点的数据

          var pointEditors = this.getComponentsInChildren(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          this._pathDataObj.points = pointEditors.map(editor => editor.pathPoint);
          this.drawPath();
        }

        drawPath() {
          var graphics = this.graphics;
          graphics.clear();
          if (this._pathDataObj.points.length < 2) return; // 绘制控制线（连接原始控制点）

          if (this.showControlPoints) {
            graphics.strokeColor = this.controlLineColor;
            graphics.lineWidth = 1;

            for (var i = 0; i < this._pathDataObj.points.length - 1; i++) {
              var p1 = this._pathDataObj.points[i].position;
              var p2 = this._pathDataObj.points[i + 1].position;
              graphics.moveTo(p1.x, p1.y);
              graphics.lineTo(p2.x, p2.y);
            }

            graphics.stroke();
          } // 绘制Catmull-Rom曲线


          graphics.strokeColor = this.curveColor;
          graphics.lineWidth = 3;

          var curvePoints = this._pathDataObj.generateCurvePoints();

          if (curvePoints.length > 1) {
            graphics.moveTo(curvePoints[0].x, curvePoints[0].y);

            for (var _i = 1; _i < curvePoints.length; _i++) {
              graphics.lineTo(curvePoints[_i].x, curvePoints[_i].y);
            }

            graphics.stroke();
          }
        }

        update(_dt) {
          if (EDITOR) {
            this.drawPath();
          }
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "pathData", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "pathData"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "pathName", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "pathName"), _class2.prototype), _descriptor = _applyDecoratedDescriptor(_class2.prototype, "showCurve", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "showControlPoints", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "curveColor", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return Color.WHITE;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "controlLineColor", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return Color.GRAY;
        }
      })), _class2)) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b09cd97fe2cb43a7b5c53aaa1e3a7c89d31e63fc.js.map