{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/FormationEditor.ts"], "names": ["_decorator", "Node", "Component", "JsonAsset", "FormationGroup", "FormationPoint", "FormationPointEditor", "ccclass", "playOnFocus", "executeInEditMode", "property", "disallowMultiple", "menu", "requireComponent", "FormationEditor", "type", "displayName", "_formationData", "_formationGroup", "formationData", "value", "reload", "formationName", "name", "formationGroup", "Object", "assign", "json", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "points", "length", "for<PERSON>ach", "point", "addPoint", "save", "getComponentsInChildren", "map", "formationPoint", "JSON", "stringify", "pointNode", "parent", "pointEditor", "addComponent", "addNewPoint", "x", "y"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAyBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;AAI1CC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,c,iBAAAA,c;;AAChBC,MAAAA,oB,iBAAAA,oB;;;;;;;;;OAJH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,WAAX;AAAwBC,QAAAA,iBAAxB;AAA2CC,QAAAA,QAA3C;AAAqDC,QAAAA,gBAArD;AAAuEC,QAAAA,IAAvE;AAA6EC,QAAAA;AAA7E,O,GAAmGb,U;;iCAU5Fc,e,WAJZP,OAAO,CAAC,iBAAD,C,UACPK,IAAI,CAAC,WAAD,C,UACJH,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAEZD,QAAQ,CAAC;AAACK,QAAAA,IAAI,EAAEZ,SAAP;AAAkBa,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UASRN,QAAQ,CAAC;AAACM,QAAAA,WAAW,EAAE;AAAd,OAAD,C,wEAdb,MAIaF,eAJb,SAIqCZ,SAJrC,CAI+C;AAAA;AAAA;AAAA,eAkBnCe,cAlBmC,GAkBF,IAlBE;AAAA,eAmBnCC,eAnBmC,GAmBD;AAAA;AAAA,iDAnBC;AAAA;;AAEnB,YAAbC,aAAa,CAACC,KAAD,EAAmB;AACvC,eAAKH,cAAL,GAAsBG,KAAtB;AACA,eAAKC,MAAL;AACH;;AACuB,YAAbF,aAAa,GAAmB;AACvC,iBAAO,KAAKF,cAAZ;AACH;;AAGuB,YAAbK,aAAa,GAAW;AAC/B,iBAAO,KAAKJ,eAAL,CAAqBK,IAA5B;AACH;;AACuB,YAAbD,aAAa,CAACF,KAAD,EAAe;AACnC,eAAKF,eAAL,CAAqBK,IAArB,GAA4BH,KAA5B;AACH;;AAKMC,QAAAA,MAAM,GAAG;AACZ,cAAI,CAAC,KAAKJ,cAAV,EAA0B;AAE1B,gBAAMO,cAAc,GAAG;AAAA;AAAA,iDAAvB;AACAC,UAAAA,MAAM,CAACC,MAAP,CAAcF,cAAd,EAA8B,KAAKP,cAAL,CAAoBU,IAAlD;AACA,eAAKT,eAAL,GAAuBM,cAAvB;AAEA,eAAKI,IAAL,CAAUC,iBAAV;;AACA,cAAI,KAAKX,eAAL,IAAwB,KAAKA,eAAL,CAAqBY,MAArB,CAA4BC,MAA5B,GAAqC,CAAjE,EAAoE;AAChE,iBAAKb,eAAL,CAAqBY,MAArB,CAA4BE,OAA5B,CAAqCC,KAAD,IAAW;AAC3C,mBAAKC,QAAL,CAAcD,KAAd;AACH,aAFD;AAGH;AACJ;;AAEME,QAAAA,IAAI,GAAW;AAClB;AACA,gBAAML,MAAM,GAAG,KAAKM,uBAAL;AAAA;AAAA,2DAAf;AACA,eAAKlB,eAAL,CAAqBY,MAArB,GAA8BA,MAAM,CAACO,GAAP,CAAYJ,KAAD,IAAWA,KAAK,CAACK,cAA5B,CAA9B;AACA,iBAAOC,IAAI,CAACC,SAAL,CAAe,KAAKtB,eAApB,EAAqC,IAArC,EAA2C,CAA3C,CAAP;AACH;;AAEMgB,QAAAA,QAAQ,CAACD,KAAD,EAAwB;AACnC,gBAAMQ,SAAS,GAAG,IAAIxC,IAAJ,EAAlB;AACAwC,UAAAA,SAAS,CAACC,MAAV,GAAmB,KAAKd,IAAxB;AACA,gBAAMe,WAAW,GAAGF,SAAS,CAACG,YAAV;AAAA;AAAA,2DAApB;AACAD,UAAAA,WAAW,CAACL,cAAZ,GAA6BL,KAA7B;AACH;;AAEMY,QAAAA,WAAW,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AACrC,gBAAMd,KAAK,GAAG;AAAA;AAAA,iDAAd;AACAA,UAAAA,KAAK,CAACa,CAAN,GAAUA,CAAV;AACAb,UAAAA,KAAK,CAACc,CAAN,GAAUA,CAAV;AACA,eAAKb,QAAL,CAAcD,KAAd;AACH;;AAvD0C,O", "sourcesContent": ["import { _decorator, instantiate, Node, Component, JsonAsset, Rect, Vec3, Graphics, assetManager } from 'cc';\r\nconst { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, requireComponent  } = _decorator;\r\nimport { EDITOR } from 'cc/env';\r\n\r\nimport { FormationGroup, FormationPoint } from 'db://assets/bundles/common/script/game/data/WaveData';\r\nimport { FormationPointEditor } from './FormationPointEditor';\r\n\r\n@ccclass('FormationEditor')\r\n@menu(\"怪物/编辑器/阵型\")\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class FormationEditor extends Component {\r\n    @property({type: JsonAsset, displayName: \"阵型数据\"})\r\n    public set formationData(value: JsonAsset) {\r\n        this._formationData = value;\r\n        this.reload();\r\n    }\r\n    public get formationData(): JsonAsset|null {\r\n        return this._formationData;\r\n    }\r\n\r\n    @property({displayName: \"阵型名字\"})\r\n    public get formationName(): string {\r\n        return this._formationGroup.name;\r\n    }\r\n    public set formationName(value:string) {\r\n        this._formationGroup.name = value;\r\n    }\r\n\r\n    private _formationData: JsonAsset|null = null;\r\n    private _formationGroup: FormationGroup = new FormationGroup();\r\n\r\n    public reload() {\r\n        if (!this._formationData) return;\r\n\r\n        const formationGroup = new FormationGroup();\r\n        Object.assign(formationGroup, this._formationData.json);\r\n        this._formationGroup = formationGroup;\r\n        \r\n        this.node.removeAllChildren();\r\n        if (this._formationGroup && this._formationGroup.points.length > 0) {\r\n            this._formationGroup.points.forEach((point) => {\r\n                this.addPoint(point);\r\n            });\r\n        }        \r\n    }\r\n\r\n    public save(): string {\r\n        // save this._formationGroup to this._formationData\r\n        const points = this.getComponentsInChildren(FormationPointEditor);\r\n        this._formationGroup.points = points.map((point) => point.formationPoint);\r\n        return JSON.stringify(this._formationGroup, null, 2);\r\n    }\r\n\r\n    public addPoint(point: FormationPoint) {\r\n        const pointNode = new Node();\r\n        pointNode.parent = this.node;\r\n        const pointEditor = pointNode.addComponent(FormationPointEditor);\r\n        pointEditor.formationPoint = point;\r\n    }\r\n\r\n    public addNewPoint(x: number, y: number) {\r\n        const point = new FormationPoint();\r\n        point.x = x;\r\n        point.y = y;\r\n        this.addPoint(point);\r\n    }\r\n}"]}