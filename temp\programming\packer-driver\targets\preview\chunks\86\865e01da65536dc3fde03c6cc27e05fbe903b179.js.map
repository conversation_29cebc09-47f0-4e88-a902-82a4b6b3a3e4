{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathFollower.ts"], "names": ["_decorator", "Component", "Vec2", "CCFloat", "JsonAsset", "PathData", "ccclass", "property", "PathFollower", "type", "displayName", "tooltip", "range", "slide", "_pathData", "_curvePoints", "_totalDistance", "_distances", "onLoad", "loadPathData", "update", "dt", "autoMove", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathAsset", "Object", "assign", "json", "generateCurveData", "points", "generateCurvePoints", "calculateDistances", "i", "distance", "push", "deltaProgress", "moveSpeed", "progress", "loop", "updatePosition", "targetDistance", "position", "getPositionAtDistance", "node", "setPosition", "x", "y", "autoFacing", "direction", "getDirectionAtDistance", "lengthSqr", "angle", "Math", "atan2", "PI", "clone", "segmentStart", "segmentEnd", "segmentLength", "t", "lerp", "epsilon", "pos1", "pos2", "subtract", "normalize", "setProgress", "max", "min", "resetToStart", "moveToEnd", "startAutoMove", "stopAutoMove", "getCurrentPathPointData", "pointIndex", "floor", "clampedIndex"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;;AACtCC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;AAE9B;AACA;AACA;;8BAEaQ,Y,WADZF,OAAO,CAAC,cAAD,C,UAEHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEL,SAAR;AAAmBM,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,OAAR;AAAiBO,QAAAA,WAAW,EAAE,MAA9B;AAAsCC,QAAAA,OAAO,EAAE;AAA/C,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,OAAR;AAAiBO,QAAAA,WAAW,EAAE,MAA9B;AAAsCE,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAA7C;AAAqDC,QAAAA,KAAK,EAAE,IAA5D;AAAkEF,QAAAA,OAAO,EAAE;AAA3E,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,2BAjBb,MACaF,YADb,SACkCP,SADlC,CAC4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAmBhCa,SAnBgC,GAmBH,IAnBG;AAAA,eAoBhCC,YApBgC,GAoBT,EApBS;AAAA,eAqBhCC,cArBgC,GAqBP,CArBO;AAAA,eAsBhCC,UAtBgC,GAsBT,EAtBS;AAAA;;AAwB9BC,QAAAA,MAAM,GAAG;AACf,eAAKC,YAAL;AACH;;AAESC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACzB,cAAI,KAAKC,QAAL,IAAiB,KAAKP,YAAL,CAAkBQ,MAAlB,GAA2B,CAAhD,EAAmD;AAC/C,iBAAKC,aAAL,CAAmBH,EAAnB;AACH;AACJ;;AAEOF,QAAAA,YAAY,GAAG;AACnB,cAAI,CAAC,KAAKM,SAAV,EAAqB;AAErB,eAAKX,SAAL,GAAiB;AAAA;AAAA,qCAAjB;AACAY,UAAAA,MAAM,CAACC,MAAP,CAAc,KAAKb,SAAnB,EAA8B,KAAKW,SAAL,CAAeG,IAA7C;AAEA,eAAKC,iBAAL;AACH;;AAEOA,QAAAA,iBAAiB,GAAG;AACxB,cAAI,CAAC,KAAKf,SAAN,IAAmB,KAAKA,SAAL,CAAegB,MAAf,CAAsBP,MAAtB,GAA+B,CAAtD,EAAyD,OADjC,CAGxB;;AACA,eAAKR,YAAL,GAAoB,KAAKD,SAAL,CAAeiB,mBAAf,EAApB,CAJwB,CAMxB;;AACA,eAAKC,kBAAL;AACH;;AAEOA,QAAAA,kBAAkB,GAAG;AACzB,eAAKf,UAAL,GAAkB,CAAC,CAAD,CAAlB;AACA,eAAKD,cAAL,GAAsB,CAAtB;;AAEA,eAAK,IAAIiB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlB,YAAL,CAAkBQ,MAAtC,EAA8CU,CAAC,EAA/C,EAAmD;AAC/C,gBAAMC,QAAQ,GAAGhC,IAAI,CAACgC,QAAL,CAAc,KAAKnB,YAAL,CAAkBkB,CAAC,GAAG,CAAtB,CAAd,EAAwC,KAAKlB,YAAL,CAAkBkB,CAAlB,CAAxC,CAAjB;AACA,iBAAKjB,cAAL,IAAuBkB,QAAvB;;AACA,iBAAKjB,UAAL,CAAgBkB,IAAhB,CAAqB,KAAKnB,cAA1B;AACH;AACJ;;AAEOQ,QAAAA,aAAa,CAACH,EAAD,EAAa;AAC9B,cAAI,KAAKL,cAAL,KAAwB,CAA5B,EAA+B,OADD,CAG9B;;AACA,cAAMoB,aAAa,GAAI,KAAKC,SAAL,GAAiBhB,EAAlB,GAAwB,KAAKL,cAAnD;AACA,eAAKsB,QAAL,IAAiBF,aAAjB;;AAEA,cAAI,KAAKE,QAAL,IAAiB,CAArB,EAAwB;AACpB,gBAAI,KAAKC,IAAT,EAAe;AACX,mBAAKD,QAAL,GAAgB,KAAKA,QAAL,GAAgB,CAAhC;AACH,aAFD,MAEO;AACH,mBAAKA,QAAL,GAAgB,CAAhB;AACA,mBAAKhB,QAAL,GAAgB,KAAhB;AACH;AACJ;;AAED,eAAKkB,cAAL;AACH;;AAEOA,QAAAA,cAAc,GAAG;AACrB,cAAI,KAAKzB,YAAL,CAAkBQ,MAAlB,GAA2B,CAA/B,EAAkC;AAElC,cAAMkB,cAAc,GAAG,KAAKH,QAAL,GAAgB,KAAKtB,cAA5C;AACA,cAAM0B,QAAQ,GAAG,KAAKC,qBAAL,CAA2BF,cAA3B,CAAjB;AAEA,eAAKG,IAAL,CAAUC,WAAV,CAAsBH,QAAQ,CAACI,CAA/B,EAAkCJ,QAAQ,CAACK,CAA3C,EAA8C,CAA9C,EANqB,CAQrB;;AACA,cAAI,KAAKC,UAAT,EAAqB;AACjB,gBAAMC,SAAS,GAAG,KAAKC,sBAAL,CAA4BT,cAA5B,CAAlB;;AACA,gBAAIQ,SAAS,CAACE,SAAV,KAAwB,KAA5B,EAAmC;AAC/B,kBAAMC,KAAK,GAAGC,IAAI,CAACC,KAAL,CAAWL,SAAS,CAACF,CAArB,EAAwBE,SAAS,CAACH,CAAlC,IAAuC,GAAvC,GAA6CO,IAAI,CAACE,EAAhE;AACA,mBAAKX,IAAL,CAAUQ,KAAV,GAAkB,CAACA,KAAnB,CAF+B,CAEL;AAC7B;AACJ;AACJ;;AAEOT,QAAAA,qBAAqB,CAACT,QAAD,EAAyB;AAClD,cAAIA,QAAQ,IAAI,CAAhB,EAAmB,OAAO,KAAKnB,YAAL,CAAkB,CAAlB,EAAqByC,KAArB,EAAP;AACnB,cAAItB,QAAQ,IAAI,KAAKlB,cAArB,EAAqC,OAAO,KAAKD,YAAL,CAAkB,KAAKA,YAAL,CAAkBQ,MAAlB,GAA2B,CAA7C,EAAgDiC,KAAhD,EAAP,CAFa,CAIlD;;AACA,eAAK,IAAIvB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhB,UAAL,CAAgBM,MAApC,EAA4CU,CAAC,EAA7C,EAAiD;AAC7C,gBAAIC,QAAQ,IAAI,KAAKjB,UAAL,CAAgBgB,CAAhB,CAAhB,EAAoC;AAChC,kBAAMwB,YAAY,GAAG,KAAKxC,UAAL,CAAgBgB,CAAC,GAAG,CAApB,CAArB;AACA,kBAAMyB,UAAU,GAAG,KAAKzC,UAAL,CAAgBgB,CAAhB,CAAnB;AACA,kBAAM0B,aAAa,GAAGD,UAAU,GAAGD,YAAnC;AAEA,kBAAIE,aAAa,KAAK,CAAtB,EAAyB,OAAO,KAAK5C,YAAL,CAAkBkB,CAAC,GAAG,CAAtB,EAAyBuB,KAAzB,EAAP;AAEzB,kBAAMI,CAAC,GAAG,CAAC1B,QAAQ,GAAGuB,YAAZ,IAA4BE,aAAtC;AACA,qBAAOzD,IAAI,CAAC2D,IAAL,CAAU,IAAI3D,IAAJ,EAAV,EAAsB,KAAKa,YAAL,CAAkBkB,CAAC,GAAG,CAAtB,CAAtB,EAAgD,KAAKlB,YAAL,CAAkBkB,CAAlB,CAAhD,EAAsE2B,CAAtE,CAAP;AACH;AACJ;;AAED,iBAAO,KAAK7C,YAAL,CAAkB,KAAKA,YAAL,CAAkBQ,MAAlB,GAA2B,CAA7C,EAAgDiC,KAAhD,EAAP;AACH;;AAEON,QAAAA,sBAAsB,CAAChB,QAAD,EAAyB;AACnD,cAAM4B,OAAO,GAAG,CAAhB,CADmD,CAChC;;AACnB,cAAMC,IAAI,GAAG,KAAKpB,qBAAL,CAA2BT,QAA3B,CAAb;AACA,cAAM8B,IAAI,GAAG,KAAKrB,qBAAL,CAA2BT,QAAQ,GAAG4B,OAAtC,CAAb;AAEA,iBAAO5D,IAAI,CAAC+D,QAAL,CAAc,IAAI/D,IAAJ,EAAd,EAA0B8D,IAA1B,EAAgCD,IAAhC,EAAsCG,SAAtC,EAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,WAAW,CAAC7B,QAAD,EAAmB;AACjC,eAAKA,QAAL,GAAgBe,IAAI,CAACe,GAAL,CAAS,CAAT,EAAYf,IAAI,CAACgB,GAAL,CAAS,CAAT,EAAY/B,QAAZ,CAAZ,CAAhB;AACA,eAAKE,cAAL;AACH;AAED;AACJ;AACA;;;AACW8B,QAAAA,YAAY,GAAG;AAClB,eAAKH,WAAL,CAAiB,CAAjB;AACH;AAED;AACJ;AACA;;;AACWI,QAAAA,SAAS,GAAG;AACf,eAAKJ,WAAL,CAAiB,CAAjB;AACH;AAED;AACJ;AACA;;;AACWK,QAAAA,aAAa,GAAG;AACnB,eAAKlD,QAAL,GAAgB,IAAhB;AACH;AAED;AACJ;AACA;;;AACWmD,QAAAA,YAAY,GAAG;AAClB,eAAKnD,QAAL,GAAgB,KAAhB;AACH;AAED;AACJ;AACA;;;AACWoD,QAAAA,uBAAuB,GAAG;AAC7B,cAAI,CAAC,KAAK5D,SAAN,IAAmB,KAAKA,SAAL,CAAegB,MAAf,CAAsBP,MAAtB,KAAiC,CAAxD,EAA2D,OAAO,IAAP;AAE3D,cAAMoD,UAAU,GAAGtB,IAAI,CAACuB,KAAL,CAAW,KAAKtC,QAAL,IAAiB,KAAKxB,SAAL,CAAegB,MAAf,CAAsBP,MAAtB,GAA+B,CAAhD,CAAX,CAAnB;AACA,cAAMsD,YAAY,GAAGxB,IAAI,CAACe,GAAL,CAAS,CAAT,EAAYf,IAAI,CAACgB,GAAL,CAAS,KAAKvD,SAAL,CAAegB,MAAf,CAAsBP,MAAtB,GAA+B,CAAxC,EAA2CoD,UAA3C,CAAZ,CAArB;AAEA,iBAAO,KAAK7D,SAAL,CAAegB,MAAf,CAAsB+C,YAAtB,CAAP;AACH;;AAjLuC,O;;;;;iBAEH,I;;;;;;;iBAGV,G;;;;;;;iBAGD,C;;;;;;;iBAGC,I;;;;;;;iBAGJ,K;;;;;;;iBAGM,I", "sourcesContent": ["import { _decorator, Component, Vec2, CCFloat, JsonAsset } from 'cc';\nimport { PathData } from 'db://assets/bundles/common/script/game/data/PathData';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 路径跟随器 - 运行时使用路径数据让对象沿路径移动\n */\n@ccclass('PathFollower')\nexport class PathFollower extends Component {\n    @property({ type: JsonAsset, displayName: \"路径数据\" })\n    public pathAsset: JsonAsset | null = null;\n\n    @property({ type: CCFloat, displayName: \"移动速度\", tooltip: \"沿路径移动的速度\" })\n    public moveSpeed: number = 100;\n\n    @property({ type: CCFloat, displayName: \"当前进度\", range: [0, 1], slide: true, tooltip: \"当前在路径上的位置 [0-1]\" })\n    public progress: number = 0;\n\n    @property({ displayName: \"自动移动\" })\n    public autoMove: boolean = true;\n\n    @property({ displayName: \"循环移动\" })\n    public loop: boolean = false;\n\n    @property({ displayName: \"自动朝向\" })\n    public autoFacing: boolean = true;\n\n    private _pathData: PathData | null = null;\n    private _curvePoints: Vec2[] = [];\n    private _totalDistance: number = 0;\n    private _distances: number[] = [];\n\n    protected onLoad() {\n        this.loadPathData();\n    }\n\n    protected update(dt: number) {\n        if (this.autoMove && this._curvePoints.length > 1) {\n            this.moveAlongPath(dt);\n        }\n    }\n\n    private loadPathData() {\n        if (!this.pathAsset) return;\n\n        this._pathData = new PathData();\n        Object.assign(this._pathData, this.pathAsset.json);\n        \n        this.generateCurveData();\n    }\n\n    private generateCurveData() {\n        if (!this._pathData || this._pathData.points.length < 2) return;\n\n        // 生成曲线点\n        this._curvePoints = this._pathData.generateCurvePoints();\n        \n        // 计算距离信息\n        this.calculateDistances();\n    }\n\n    private calculateDistances() {\n        this._distances = [0];\n        this._totalDistance = 0;\n\n        for (let i = 1; i < this._curvePoints.length; i++) {\n            const distance = Vec2.distance(this._curvePoints[i - 1], this._curvePoints[i]);\n            this._totalDistance += distance;\n            this._distances.push(this._totalDistance);\n        }\n    }\n\n    private moveAlongPath(dt: number) {\n        if (this._totalDistance === 0) return;\n\n        // 根据速度更新进度\n        const deltaProgress = (this.moveSpeed * dt) / this._totalDistance;\n        this.progress += deltaProgress;\n\n        if (this.progress >= 1) {\n            if (this.loop) {\n                this.progress = this.progress - 1;\n            } else {\n                this.progress = 1;\n                this.autoMove = false;\n            }\n        }\n\n        this.updatePosition();\n    }\n\n    private updatePosition() {\n        if (this._curvePoints.length < 2) return;\n\n        const targetDistance = this.progress * this._totalDistance;\n        const position = this.getPositionAtDistance(targetDistance);\n\n        this.node.setPosition(position.x, position.y, 0);\n\n        // 自动朝向\n        if (this.autoFacing) {\n            const direction = this.getDirectionAtDistance(targetDistance);\n            if (direction.lengthSqr() > 0.001) {\n                const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;\n                this.node.angle = -angle; // Cocos Creator的角度系统\n            }\n        }\n    }\n\n    private getPositionAtDistance(distance: number): Vec2 {\n        if (distance <= 0) return this._curvePoints[0].clone();\n        if (distance >= this._totalDistance) return this._curvePoints[this._curvePoints.length - 1].clone();\n\n        // 找到距离对应的线段\n        for (let i = 1; i < this._distances.length; i++) {\n            if (distance <= this._distances[i]) {\n                const segmentStart = this._distances[i - 1];\n                const segmentEnd = this._distances[i];\n                const segmentLength = segmentEnd - segmentStart;\n\n                if (segmentLength === 0) return this._curvePoints[i - 1].clone();\n\n                const t = (distance - segmentStart) / segmentLength;\n                return Vec2.lerp(new Vec2(), this._curvePoints[i - 1], this._curvePoints[i], t);\n            }\n        }\n\n        return this._curvePoints[this._curvePoints.length - 1].clone();\n    }\n\n    private getDirectionAtDistance(distance: number): Vec2 {\n        const epsilon = 1; // 小的距离偏移用于计算方向\n        const pos1 = this.getPositionAtDistance(distance);\n        const pos2 = this.getPositionAtDistance(distance + epsilon);\n\n        return Vec2.subtract(new Vec2(), pos2, pos1).normalize();\n    }\n\n    /**\n     * 设置路径进度\n     * @param progress 进度值 [0-1]\n     */\n    public setProgress(progress: number) {\n        this.progress = Math.max(0, Math.min(1, progress));\n        this.updatePosition();\n    }\n\n    /**\n     * 重置到路径起点\n     */\n    public resetToStart() {\n        this.setProgress(0);\n    }\n\n    /**\n     * 移动到路径终点\n     */\n    public moveToEnd() {\n        this.setProgress(1);\n    }\n\n    /**\n     * 开始自动移动\n     */\n    public startAutoMove() {\n        this.autoMove = true;\n    }\n\n    /**\n     * 停止自动移动\n     */\n    public stopAutoMove() {\n        this.autoMove = false;\n    }\n\n    /**\n     * 获取当前位置对应的路径点属性（速度、角度等）\n     */\n    public getCurrentPathPointData() {\n        if (!this._pathData || this._pathData.points.length === 0) return null;\n\n        const pointIndex = Math.floor(this.progress * (this._pathData.points.length - 1));\n        const clampedIndex = Math.max(0, Math.min(this._pathData.points.length - 1, pointIndex));\n        \n        return this._pathData.points[clampedIndex];\n    }\n}\n"]}