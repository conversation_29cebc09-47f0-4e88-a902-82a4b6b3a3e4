"[\n  {\n    \"__type__\": \"cc.SceneAsset\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_native\": \"\",\n    \"scene\": {\n      \"__id__\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.Scene\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": null,\n    \"_children\": [\n      {\n        \"__id__\": 2\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"autoReleaseAssets\": false,\n    \"_globals\": {\n      \"__id__\": 10\n    },\n    \"_id\": \"85154349-a5a5-4048-b838-c6e3b8087480\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Canvas\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 1\n    },\n    \"_children\": [\n      {\n        \"__id__\": 3\n      },\n      {\n        \"__id__\": 5\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 7\n      },\n      {\n        \"__id__\": 8\n      },\n      {\n        \"__id__\": 9\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 375,\n      \"y\": 667,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"faDU1dP+NH/7LftrOeHnar\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Camera\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 4\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 1000\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"30ULKOv+FFHZjbW134mFVm\"\n  },\n  {\n    \"__type__\": \"cc.Camera\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 3\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_projection\": 0,\n    \"_priority\": 1073741824,\n    \"_fov\": 45,\n    \"_fovAxis\": 0,\n    \"_orthoHeight\": 667,\n    \"_near\": 1,\n    \"_far\": 2000,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_depth\": 1,\n    \"_stencil\": 0,\n    \"_clearFlags\": 6,\n    \"_rect\": {\n      \"__type__\": \"cc.Rect\",\n      \"x\": 0,\n      \"y\": 0,\n      \"width\": 1,\n      \"height\": 1\n    },\n    \"_aperture\": 19,\n    \"_shutter\": 7,\n    \"_iso\": 0,\n    \"_screenScale\": 1,\n    \"_visibility\": 41943040,\n    \"_targetTexture\": null,\n    \"_postProcess\": null,\n    \"_usePostProcess\": false,\n    \"_cameraType\": -1,\n    \"_trackingType\": 0,\n    \"_id\": \"4dddk0EUJMjLKoR1z1H7DR\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"P\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 6\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d8eQW7RQBMSbAN/eEfWQWW\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"72E9gcDKdKQ57rpzbQZNrq\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 750,\n      \"height\": 1334\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"877MyVSH5HT4ua4aw8hv3u\"\n  },\n  {\n    \"__type__\": \"cc.Canvas\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_cameraComponent\": {\n      \"__id__\": 4\n    },\n    \"_alignCanvasWithScreen\": true,\n    \"_id\": \"d62tTthv1JvoSFwcjsZlx9\"\n  },\n  {\n    \"__type__\": \"cc.Widget\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_alignFlags\": 45,\n    \"_target\": null,\n    \"_left\": 0,\n    \"_right\": 0,\n    \"_top\": 0,\n    \"_bottom\": 0,\n    \"_horizontalCenter\": 0,\n    \"_verticalCenter\": 0,\n    \"_isAbsLeft\": true,\n    \"_isAbsRight\": true,\n    \"_isAbsTop\": true,\n    \"_isAbsBottom\": true,\n    \"_isAbsHorizontalCenter\": true,\n    \"_isAbsVerticalCenter\": true,\n    \"_originalWidth\": 0,\n    \"_originalHeight\": 0,\n    \"_alignMode\": 2,\n    \"_lockFlags\": 0,\n    \"_id\": \"85bL1fMaJJ3bPnFIT6qe5w\"\n  },\n  {\n    \"__type__\": \"cc.SceneGlobals\",\n    \"ambient\": {\n      \"__id__\": 11\n    },\n    \"shadows\": {\n      \"__id__\": 12\n    },\n    \"_skybox\": {\n      \"__id__\": 13\n    },\n    \"fog\": {\n      \"__id__\": 14\n    },\n    \"octree\": {\n      \"__id__\": 15\n    },\n    \"skin\": {\n      \"__id__\": 16\n    },\n    \"lightProbeInfo\": {\n      \"__id__\": 17\n    },\n    \"postSettings\": {\n      \"__id__\": 18\n    },\n    \"bakedWithStationaryMainLight\": false,\n    \"bakedWithHighpLightmap\": false\n  },\n  {\n    \"__type__\": \"cc.AmbientInfo\",\n    \"_skyColorHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.5,\n      \"z\": 0.8,\n      \"w\": 0.520833125\n    },\n    \"_skyColor\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.5,\n      \"z\": 0.8,\n      \"w\": 0.520833125\n    },\n    \"_skyIllumHDR\": 20000,\n    \"_skyIllum\": 20000,\n    \"_groundAlbedoHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.2,\n      \"z\": 0.2,\n      \"w\": 1\n    },\n    \"_groundAlbedo\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.2,\n      \"z\": 0.2,\n      \"w\": 1\n    },\n    \"_skyColorLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.452588,\n      \"y\": 0.607642,\n      \"z\": 0.755699,\n      \"w\": 0\n    },\n    \"_skyIllumLDR\": 0.8,\n    \"_groundAlbedoLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.618555,\n      \"y\": 0.577848,\n      \"z\": 0.544564,\n      \"w\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.ShadowsInfo\",\n    \"_enabled\": false,\n    \"_type\": 0,\n    \"_normal\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 1,\n      \"z\": 0\n    },\n    \"_distance\": 0,\n    \"_planeBias\": 1,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 76,\n      \"g\": 76,\n      \"b\": 76,\n      \"a\": 255\n    },\n    \"_maxReceived\": 4,\n    \"_size\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1024,\n      \"y\": 1024\n    }\n  },\n  {\n    \"__type__\": \"cc.SkyboxInfo\",\n    \"_envLightingType\": 0,\n    \"_envmapHDR\": {\n      \"__uuid__\": \"d032ac98-05e1-4090-88bb-eb640dcb5fc1@b47c0\",\n      \"__expectedType__\": \"cc.TextureCube\"\n    },\n    \"_envmap\": {\n      \"__uuid__\": \"d032ac98-05e1-4090-88bb-eb640dcb5fc1@b47c0\",\n      \"__expectedType__\": \"cc.TextureCube\"\n    },\n    \"_envmapLDR\": {\n      \"__uuid__\": \"6f01cf7f-81bf-4a7e-bd5d-0afc19696480@b47c0\",\n      \"__expectedType__\": \"cc.TextureCube\"\n    },\n    \"_diffuseMapHDR\": null,\n    \"_diffuseMapLDR\": null,\n    \"_enabled\": true,\n    \"_useHDR\": true,\n    \"_editableMaterial\": null,\n    \"_reflectionHDR\": null,\n    \"_reflectionLDR\": null,\n    \"_rotationAngle\": 0\n  },\n  {\n    \"__type__\": \"cc.FogInfo\",\n    \"_type\": 0,\n    \"_fogColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 200,\n      \"g\": 200,\n      \"b\": 200,\n      \"a\": 255\n    },\n    \"_enabled\": false,\n    \"_fogDensity\": 0.3,\n    \"_fogStart\": 0.5,\n    \"_fogEnd\": 300,\n    \"_fogAtten\": 5,\n    \"_fogTop\": 1.5,\n    \"_fogRange\": 1.2,\n    \"_accurate\": false\n  },\n  {\n    \"__type__\": \"cc.OctreeInfo\",\n    \"_enabled\": false,\n    \"_minPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -1024,\n      \"y\": -1024,\n      \"z\": -1024\n    },\n    \"_maxPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1024,\n      \"y\": 1024,\n      \"z\": 1024\n    },\n    \"_depth\": 8\n  },\n  {\n    \"__type__\": \"cc.SkinInfo\",\n    \"_enabled\": true,\n    \"_blurRadius\": 0.01,\n    \"_sssIntensity\": 3\n  },\n  {\n    \"__type__\": \"cc.LightProbeInfo\",\n    \"_giScale\": 1,\n    \"_giSamples\": 1024,\n    \"_bounces\": 2,\n    \"_reduceRinging\": 0,\n    \"_showProbe\": true,\n    \"_showWireframe\": true,\n    \"_showConvex\": false,\n    \"_data\": null,\n    \"_lightProbeSphereVolume\": 1\n  },\n  {\n    \"__type__\": \"cc.PostSettingsInfo\",\n    \"_toneMappingType\": 0\n  }\n]"