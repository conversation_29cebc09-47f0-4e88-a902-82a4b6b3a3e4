System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec2, CCFloat, JsonAsset, PathData, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _crd, ccclass, property, PathFollower;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec2 = _cc.Vec2;
      CCFloat = _cc.CCFloat;
      JsonAsset = _cc.JsonAsset;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ae91c4WzuxGp5OcU4R+1uGb", "PathFollower", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Vec2', 'CCFloat', 'JsonAsset']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 路径跟随器 - 运行时使用路径数据让对象沿路径移动
       */

      _export("PathFollower", PathFollower = (_dec = ccclass('PathFollower'), _dec2 = property({
        type: JsonAsset,
        displayName: "路径数据"
      }), _dec3 = property({
        type: CCFloat,
        displayName: "移动速度",
        tooltip: "沿路径移动的速度"
      }), _dec4 = property({
        type: CCFloat,
        displayName: "当前进度",
        range: [0, 1],
        slide: true,
        tooltip: "当前在路径上的位置 [0-1]"
      }), _dec5 = property({
        displayName: "自动移动"
      }), _dec6 = property({
        displayName: "循环移动"
      }), _dec7 = property({
        displayName: "自动朝向"
      }), _dec(_class = (_class2 = class PathFollower extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "pathAsset", _descriptor, this);

          _initializerDefineProperty(this, "moveSpeed", _descriptor2, this);

          _initializerDefineProperty(this, "progress", _descriptor3, this);

          _initializerDefineProperty(this, "autoMove", _descriptor4, this);

          _initializerDefineProperty(this, "loop", _descriptor5, this);

          _initializerDefineProperty(this, "autoFacing", _descriptor6, this);

          this._pathData = null;
          this._curvePoints = [];
          this._totalDistance = 0;
          this._distances = [];
        }

        onLoad() {
          this.loadPathData();
        }

        update(dt) {
          if (this.autoMove && this._curvePoints.length > 1) {
            this.moveAlongPath(dt);
          }
        }

        loadPathData() {
          if (!this.pathAsset) return;
          this._pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          Object.assign(this._pathData, this.pathAsset.json);
          this.generateCurveData();
        }

        generateCurveData() {
          if (!this._pathData || this._pathData.points.length < 2) return; // 生成曲线点

          this._curvePoints = this._pathData.generateCurvePoints(); // 计算距离信息

          this.calculateDistances();
        }

        calculateDistances() {
          this._distances = [0];
          this._totalDistance = 0;

          for (var i = 1; i < this._curvePoints.length; i++) {
            var distance = Vec2.distance(this._curvePoints[i - 1], this._curvePoints[i]);
            this._totalDistance += distance;

            this._distances.push(this._totalDistance);
          }
        }

        moveAlongPath(dt) {
          if (this._totalDistance === 0) return; // 根据速度更新进度

          var deltaProgress = this.moveSpeed * dt / this._totalDistance;
          this.progress += deltaProgress;

          if (this.progress >= 1) {
            if (this.loop) {
              this.progress = this.progress - 1;
            } else {
              this.progress = 1;
              this.autoMove = false;
            }
          }

          this.updatePosition();
        }

        updatePosition() {
          if (this._curvePoints.length < 2) return;
          var targetDistance = this.progress * this._totalDistance;
          var position = this.getPositionAtDistance(targetDistance);
          this.node.setPosition(position.x, position.y, 0); // 自动朝向

          if (this.autoFacing) {
            var direction = this.getDirectionAtDistance(targetDistance);

            if (direction.lengthSqr() > 0.001) {
              var angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
              this.node.angle = -angle; // Cocos Creator的角度系统
            }
          }
        }

        getPositionAtDistance(distance) {
          if (distance <= 0) return this._curvePoints[0].clone();
          if (distance >= this._totalDistance) return this._curvePoints[this._curvePoints.length - 1].clone(); // 找到距离对应的线段

          for (var i = 1; i < this._distances.length; i++) {
            if (distance <= this._distances[i]) {
              var segmentStart = this._distances[i - 1];
              var segmentEnd = this._distances[i];
              var segmentLength = segmentEnd - segmentStart;
              if (segmentLength === 0) return this._curvePoints[i - 1].clone();
              var t = (distance - segmentStart) / segmentLength;
              return Vec2.lerp(new Vec2(), this._curvePoints[i - 1], this._curvePoints[i], t);
            }
          }

          return this._curvePoints[this._curvePoints.length - 1].clone();
        }

        getDirectionAtDistance(distance) {
          var epsilon = 1; // 小的距离偏移用于计算方向

          var pos1 = this.getPositionAtDistance(distance);
          var pos2 = this.getPositionAtDistance(distance + epsilon);
          return Vec2.subtract(new Vec2(), pos2, pos1).normalize();
        }
        /**
         * 设置路径进度
         * @param progress 进度值 [0-1]
         */


        setProgress(progress) {
          this.progress = Math.max(0, Math.min(1, progress));
          this.updatePosition();
        }
        /**
         * 重置到路径起点
         */


        resetToStart() {
          this.setProgress(0);
        }
        /**
         * 移动到路径终点
         */


        moveToEnd() {
          this.setProgress(1);
        }
        /**
         * 开始自动移动
         */


        startAutoMove() {
          this.autoMove = true;
        }
        /**
         * 停止自动移动
         */


        stopAutoMove() {
          this.autoMove = false;
        }
        /**
         * 获取当前位置对应的路径点属性（速度、角度等）
         */


        getCurrentPathPointData() {
          if (!this._pathData || this._pathData.points.length === 0) return null;
          var pointIndex = Math.floor(this.progress * (this._pathData.points.length - 1));
          var clampedIndex = Math.max(0, Math.min(this._pathData.points.length - 1, pointIndex));
          return this._pathData.points[clampedIndex];
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "pathAsset", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "moveSpeed", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 100;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "progress", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "autoMove", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "loop", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "autoFacing", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=865e01da65536dc3fde03c6cc27e05fbe903b179.js.map