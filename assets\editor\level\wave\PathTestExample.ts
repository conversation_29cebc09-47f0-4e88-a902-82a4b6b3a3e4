import { _decorator, Component, Graphics, Color, Vec2 } from 'cc';
import { EDITOR } from 'cc/env';
import { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';

const { ccclass, property, executeInEditMode, menu } = _decorator;

/**
 * 路径测试示例 - 验证Catmull-Rom曲线是否正确经过所有定义点
 */
@ccclass('PathTestExample')
@menu("怪物/编辑器/路径测试示例")
@executeInEditMode(true)
export class PathTestExample extends Component {
    @property({ displayName: "显示测试点" })
    public showTestPoints: boolean = true;

    @property({ displayName: "显示曲线" })
    public showCurve: boolean = true;

    @property({ displayName: "点大小" })
    public pointSize: number = 8;

    private _graphics: Graphics | null = null;
    private _testPath: PathData = new PathData();

    public get graphics(): Graphics {
        if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
        }
        return this._graphics;
    }

    protected onLoad() {
        this.createTestPath();
    }

    private createTestPath() {
        // 创建一个简单的测试路径
        this._testPath.name = "测试路径";
        this._testPath.segments = 30;
        this._testPath.closed = false;

        // 添加几个测试点
        const testPoints = [
            new PathPoint(-200, 0),     // 起点
            new PathPoint(-100, 150),   // 上弯
            new PathPoint(0, 0),        // 中点
            new PathPoint(100, -150),   // 下弯
            new PathPoint(200, 0),      // 终点
        ];

        // 设置不同的平滑程度来测试效果
        testPoints[0].smoothness = 0.5;
        testPoints[1].smoothness = 0.8;  // 高平滑度
        testPoints[2].smoothness = 0.2;  // 低平滑度（更尖锐）
        testPoints[3].smoothness = 0.8;  // 高平滑度
        testPoints[4].smoothness = 0.5;

        this._testPath.points = testPoints;
    }

    public update(_dt: number) {
        if (EDITOR) {
            this.drawTest();
        }
    }

    private drawTest() {
        const graphics = this.graphics;
        graphics.clear();

        // 绘制原始定义点
        if (this.showTestPoints) {
            graphics.fillColor = Color.RED;
            graphics.strokeColor = Color.BLACK;
            graphics.lineWidth = 2;

            this._testPath.points.forEach((point, index) => {
                const pos = point.position;
                graphics.circle(pos.x, pos.y, this.pointSize);
                graphics.fill();
                graphics.stroke();

                // 绘制点的索引
                // 注意：这里只是示意，实际文字绘制需要使用Label组件
            });
        }

        // 绘制Catmull-Rom曲线
        if (this.showCurve) {
            graphics.strokeColor = Color.WHITE;
            graphics.lineWidth = 3;

            const curvePoints = this._testPath.generateCurvePoints();
            if (curvePoints.length > 1) {
                graphics.moveTo(curvePoints[0].x, curvePoints[0].y);
                for (let i = 1; i < curvePoints.length; i++) {
                    graphics.lineTo(curvePoints[i].x, curvePoints[i].y);
                }
                graphics.stroke();
            }

            // 用绿色标记曲线经过的原始定义点
            graphics.fillColor = Color.GREEN;
            this._testPath.points.forEach(point => {
                const pos = point.position;
                graphics.circle(pos.x, pos.y, this.pointSize / 2);
                graphics.fill();
            });
        }

        // 绘制平滑程度指示器
        graphics.strokeColor = Color.YELLOW;
        graphics.lineWidth = 1;
        this._testPath.points.forEach(point => {
            const pos = point.position;
            const radius = this.pointSize + point.smoothness * 20;
            graphics.circle(pos.x, pos.y, radius);
            graphics.stroke();
        });
    }

    /**
     * 验证曲线是否经过定义点
     */
    public validateCurve(): boolean {
        const curvePoints = this._testPath.generateCurvePoints();
        const tolerance = 1.0; // 允许的误差范围

        let allPointsPassed = true;
        
        this._testPath.points.forEach((definedPoint, index) => {
            let foundNearbyPoint = false;
            
            // 检查曲线点中是否有足够接近定义点的点
            for (const curvePoint of curvePoints) {
                const distance = Vec2.distance(definedPoint.position, curvePoint);
                if (distance <= tolerance) {
                    foundNearbyPoint = true;
                    break;
                }
            }
            
            if (!foundNearbyPoint) {
                console.warn(`定义点 ${index} (${definedPoint.x}, ${definedPoint.y}) 没有被曲线经过`);
                allPointsPassed = false;
            }
        });

        if (allPointsPassed) {
            console.log("✅ 验证通过：曲线正确经过所有定义点");
        } else {
            console.log("❌ 验证失败：曲线没有经过某些定义点");
        }

        return allPointsPassed;
    }

    /**
     * 创建不同类型的测试路径
     */
    public createDifferentTestPaths() {
        // 可以在这里添加更多测试用例
        this.createStraightLineTest();
        this.createSharpTurnTest();
        this.createSmoothCurveTest();
    }

    private createStraightLineTest() {
        // 测试直线（所有点在一条线上）
        const points = [
            new PathPoint(-100, 0),
            new PathPoint(0, 0),
            new PathPoint(100, 0),
        ];
        points.forEach(p => p.smoothness = 0.5);
        
        this._testPath.points = points;
        console.log("直线测试:", this.validateCurve());
    }

    private createSharpTurnTest() {
        // 测试尖锐转角
        const points = [
            new PathPoint(-100, 0),
            new PathPoint(0, 100),
            new PathPoint(100, 0),
        ];
        points.forEach(p => p.smoothness = 0.1); // 低平滑度
        
        this._testPath.points = points;
        console.log("尖锐转角测试:", this.validateCurve());
    }

    private createSmoothCurveTest() {
        // 测试平滑曲线
        const points = [
            new PathPoint(-100, 0),
            new PathPoint(0, 100),
            new PathPoint(100, 0),
        ];
        points.forEach(p => p.smoothness = 0.9); // 高平滑度
        
        this._testPath.points = points;
        console.log("平滑曲线测试:", this.validateCurve());
    }
}
