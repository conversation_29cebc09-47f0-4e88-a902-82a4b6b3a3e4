{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/PathMovable.ts"], "names": ["_decorator", "Component", "Enum", "misc", "UITransform", "Vec2", "JsonAsset", "BulletSystem", "PathData", "eSpriteDefaultFacing", "eMoveEvent", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "PathMovable", "type", "displayName", "tooltip", "speedAngle", "accelerationAngle", "_pathData", "_curvePoints", "_totalDistance", "_distances", "_currentDistance", "_isMovable", "_tiltTime", "_selfSize", "_wasVisible", "_isVisible", "_visibility<PERSON><PERSON><PERSON><PERSON>ounter", "_eventListeners", "Map", "isVisible", "isMovable", "onLoad", "uiTransform", "node", "getComponent", "self_size", "contentSize", "width", "height", "set", "loadPathData", "onDestroy", "clear", "pathAsset", "fromJSON", "json", "points", "length", "generateCurvePoints", "calculateDistances", "i", "distance", "push", "tick", "dt", "acceleration", "speed", "Math", "max", "deltaDistance", "reverse", "loop", "updatePosition", "position", "getPositionAtDistance", "tiltSpeed", "tiltOffset", "direction", "getDirectionAtDistance", "lengthSqr", "perpX", "y", "perpY", "x", "tiltAmount", "sin", "setPosition", "isFacingMoveDir", "angle", "atan2", "PI", "finalAngle", "defaultFacing", "setRotationFromEuler", "VISIBILITY_CHECK_INTERVAL", "checkVisibility", "clone", "segmentStart", "segmentEnd", "segmentLength", "t", "lerp", "epsilon", "pos1", "pos2", "subtract", "normalize", "visibleSize", "worldBounds", "worldPosition", "xMin", "xMax", "yMax", "yMin", "setVisible", "visible", "emit", "onBecomeVisible", "onBecomeInvisible", "on", "event", "listener", "has", "listeners", "get", "includes", "off", "index", "indexOf", "splice", "removeAllListeners", "for<PERSON>ach", "setMovable", "movable", "setProgress", "progress", "min", "getProgress", "resetToStart", "moveToEnd", "getCurrentPathPointData", "pointIndex", "floor", "clampedIndex", "Up"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,S,OAAAA,S;;AAClEC,MAAAA,Y,iBAAAA,Y;;AAGAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,oB,iBAAAA,oB;AAAsBC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEzB;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCT,I;OACzC;AAAEU,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;6BAGjBe,W,WADZF,OAAO,CAAC,aAAD,C,UAKHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEV,SAAR;AAAmBW,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEd,IAAI;AAAA;AAAA,yDAAZ;AAAoCe,QAAAA,WAAW,EAAE;AAAjD,OAAD,C,UAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,MAAf;AAAuBC,QAAAA,OAAO,EAAE;AAAhC,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,KAAf;AAAsBC,QAAAA,OAAO,EAAE;AAA/B,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,QAAf;AAAyBC,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,WAGRJ,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,QAAf;AAAyBC,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,sCA7Bb,MACaH,WADb,SACiCd,SADjC,CAC+D;AAAA;AAAA;AAAA,eACpDkB,UADoD,GAC/B,CAD+B;AAAA,eAEpDC,iBAFoD,GAExB,CAFwB;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AA+B3D;AA/B2D,eAgCnDC,SAhCmD,GAgCtB,IAhCsB;AAAA,eAiCnDC,YAjCmD,GAiC5B,EAjC4B;AAAA,eAkCnDC,cAlCmD,GAkC1B,CAlC0B;AAAA,eAmCnDC,UAnCmD,GAmC5B,EAnC4B;AAqC3D;AArC2D,eAsCnDC,gBAtCmD,GAsCxB,CAtCwB;AAAA,eAuCnDC,UAvCmD,GAuC7B,IAvC6B;AAAA,eAwCnDC,SAxCmD,GAwC/B,CAxC+B;AA0C3D;AA1C2D,eA2CnDC,SA3CmD,GA2CjC,IAAIvB,IAAJ,EA3CiC;AAAA,eA4CnDwB,WA5CmD,GA4C5B,KA5C4B;AAAA,eA6CnDC,UA7CmD,GA6C7B,KA7C6B;AAAA,eA8CnDC,uBA9CmD,GA8CjB,CA9CiB;AAiD3D;AAjD2D,eAkDnDC,eAlDmD,GAkDG,IAAIC,GAAJ,EAlDH;AAAA;;AAoDvC,YAATC,SAAS,GAAG;AAAE,iBAAO,KAAKJ,UAAZ;AAAyB;;AAC9B,YAATK,SAAS,GAAG;AAAE,iBAAO,KAAKT,UAAZ;AAAyB;;AAElDU,QAAAA,MAAM,GAAG;AACL,gBAAMC,WAAW,GAAG,KAAKC,IAAL,CAAUC,YAAV,CAAuBnC,WAAvB,CAApB;AACA,gBAAMoC,SAAS,GAAGH,WAAW,GAAGA,WAAW,CAACI,WAAf,GAA6B;AAAEC,YAAAA,KAAK,EAAE,CAAT;AAAYC,YAAAA,MAAM,EAAE;AAApB,WAA1D;;AACA,eAAKf,SAAL,CAAegB,GAAf,CAAmBJ,SAAS,CAACE,KAAV,GAAkB,CAArC,EAAwCF,SAAS,CAACG,MAAV,GAAmB,CAA3D;;AAEA,eAAKE,YAAL;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR,eAAKd,eAAL,CAAqBe,KAArB;AACH;AAED;AACJ;AACA;;;AACYF,QAAAA,YAAY,GAAG;AACnB,cAAI,CAAC,KAAKG,SAAV,EAAqB,OADF,CAGnB;;AACA,eAAK3B,SAAL,GAAiB;AAAA;AAAA,oCAAS4B,QAAT,CAAkB,KAAKD,SAAL,CAAeE,IAAjC,CAAjB;AAEA,cAAI,KAAK7B,SAAL,CAAe8B,MAAf,CAAsBC,MAAtB,GAA+B,CAAnC,EAAsC,OANnB,CAQnB;;AACA,eAAK9B,YAAL,GAAoB,KAAKD,SAAL,CAAegC,mBAAf,EAApB,CATmB,CAWnB;;AACA,eAAKC,kBAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,kBAAkB,GAAG;AACzB,eAAK9B,UAAL,GAAkB,CAAC,CAAD,CAAlB;AACA,eAAKD,cAAL,GAAsB,CAAtB;;AAEA,eAAK,IAAIgC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKjC,YAAL,CAAkB8B,MAAtC,EAA8CG,CAAC,EAA/C,EAAmD;AAC/C,kBAAMC,QAAQ,GAAGnD,IAAI,CAACmD,QAAL,CAAc,KAAKlC,YAAL,CAAkBiC,CAAC,GAAG,CAAtB,CAAd,EAAwC,KAAKjC,YAAL,CAAkBiC,CAAlB,CAAxC,CAAjB;AACA,iBAAKhC,cAAL,IAAuBiC,QAAvB;;AACA,iBAAKhC,UAAL,CAAgBiC,IAAhB,CAAqB,KAAKlC,cAA1B;AACH;AACJ;AAID;AACJ;AACA;;;AACWmC,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKjC,UAAN,IAAoB,KAAKJ,YAAL,CAAkB8B,MAAlB,GAA2B,CAAnD,EAAsD,OAD5B,CAG1B;;AACA,cAAI,KAAKQ,YAAL,KAAsB,CAA1B,EAA6B;AACzB,iBAAKC,KAAL,IAAc,KAAKD,YAAL,GAAoBD,EAAlC;AACA,iBAAKE,KAAL,GAAaC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAKF,KAAjB,CAAb,CAFyB,CAEa;AACzC,WAPyB,CAS1B;;;AACA,gBAAMG,aAAa,GAAG,KAAKH,KAAL,GAAaF,EAAnC;;AAEA,cAAI,KAAKM,OAAT,EAAkB;AACd,iBAAKxC,gBAAL,IAAyBuC,aAAzB;;AACA,gBAAI,KAAKvC,gBAAL,GAAwB,CAA5B,EAA+B;AAC3B,kBAAI,KAAKyC,IAAT,EAAe;AACX,qBAAKzC,gBAAL,GAAwB,KAAKF,cAAL,GAAsB,KAAKE,gBAAnD;AACH,eAFD,MAEO;AACH,qBAAKA,gBAAL,GAAwB,CAAxB;AACH;AACJ;AACJ,WATD,MASO;AACH,iBAAKA,gBAAL,IAAyBuC,aAAzB;;AACA,gBAAI,KAAKvC,gBAAL,GAAwB,KAAKF,cAAjC,EAAiD;AAC7C,kBAAI,KAAK2C,IAAT,EAAe;AACX,qBAAKzC,gBAAL,GAAwB,KAAKA,gBAAL,GAAwB,KAAKF,cAArD;AACH,eAFD,MAEO;AACH,qBAAKE,gBAAL,GAAwB,KAAKF,cAA7B;AACH;AACJ;AACJ;;AAED,eAAK4C,cAAL,CAAoBR,EAApB;AACH;AAED;AACJ;AACA;;;AACYQ,QAAAA,cAAc,CAACR,EAAD,EAAa;AAC/B,gBAAMS,QAAQ,GAAG,KAAKC,qBAAL,CAA2B,KAAK5C,gBAAhC,CAAjB,CAD+B,CAG/B;;AACA,cAAI,KAAK6C,SAAL,GAAiB,CAAjB,IAAsB,KAAKC,UAAL,GAAkB,CAA5C,EAA+C;AAC3C,iBAAK5C,SAAL,IAAkBgC,EAAlB;AAEA,kBAAMa,SAAS,GAAG,KAAKC,sBAAL,CAA4B,KAAKhD,gBAAjC,CAAlB;;AACA,gBAAI+C,SAAS,CAACE,SAAV,KAAwB,KAA5B,EAAmC;AAC/B;AACA,oBAAMC,KAAK,GAAG,CAACH,SAAS,CAACI,CAAzB;AACA,oBAAMC,KAAK,GAAGL,SAAS,CAACM,CAAxB,CAH+B,CAK/B;;AACA,oBAAMC,UAAU,GAAGjB,IAAI,CAACkB,GAAL,CAAS,KAAKrD,SAAL,GAAiB,KAAK2C,SAA/B,IAA4C,KAAKC,UAApE;AAEAH,cAAAA,QAAQ,CAACU,CAAT,IAAcH,KAAK,GAAGI,UAAtB;AACAX,cAAAA,QAAQ,CAACQ,CAAT,IAAcC,KAAK,GAAGE,UAAtB;AACH;AACJ;;AAED,eAAKzC,IAAL,CAAU2C,WAAV,CAAsBb,QAAQ,CAACU,CAA/B,EAAkCV,QAAQ,CAACQ,CAA3C,EAA8C,CAA9C,EArB+B,CAuB/B;;AACA,cAAI,KAAKM,eAAT,EAA0B;AACtB,kBAAMV,SAAS,GAAG,KAAKC,sBAAL,CAA4B,KAAKhD,gBAAjC,CAAlB;;AACA,gBAAI+C,SAAS,CAACE,SAAV,KAAwB,KAA5B,EAAmC;AAC/B,oBAAMS,KAAK,GAAGrB,IAAI,CAACsB,KAAL,CAAWZ,SAAS,CAACI,CAArB,EAAwBJ,SAAS,CAACM,CAAlC,IAAuC,GAAvC,GAA6ChB,IAAI,CAACuB,EAAhE;AACA,oBAAMC,UAAU,GAAGH,KAAK,GAAG,KAAKI,aAAhC;AACA,mBAAKjD,IAAL,CAAUkD,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCF,UAArC;AACH;AACJ,WA/B8B,CAiC/B;;;AACA,cAAI,EAAE,KAAKvD,uBAAP,IAAkChB,WAAW,CAAC0E,yBAAlD,EAA6E;AACzE,iBAAK1D,uBAAL,GAA+B,CAA/B;AACA,iBAAK2D,eAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACYrB,QAAAA,qBAAqB,CAACb,QAAD,EAAyB;AAClD,cAAIA,QAAQ,IAAI,CAAhB,EAAmB,OAAO,KAAKlC,YAAL,CAAkB,CAAlB,EAAqBqE,KAArB,EAAP;AACnB,cAAInC,QAAQ,IAAI,KAAKjC,cAArB,EAAqC,OAAO,KAAKD,YAAL,CAAkB,KAAKA,YAAL,CAAkB8B,MAAlB,GAA2B,CAA7C,EAAgDuC,KAAhD,EAAP;;AAErC,eAAK,IAAIpC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/B,UAAL,CAAgB4B,MAApC,EAA4CG,CAAC,EAA7C,EAAiD;AAC7C,gBAAIC,QAAQ,IAAI,KAAKhC,UAAL,CAAgB+B,CAAhB,CAAhB,EAAoC;AAChC,oBAAMqC,YAAY,GAAG,KAAKpE,UAAL,CAAgB+B,CAAC,GAAG,CAApB,CAArB;AACA,oBAAMsC,UAAU,GAAG,KAAKrE,UAAL,CAAgB+B,CAAhB,CAAnB;AACA,oBAAMuC,aAAa,GAAGD,UAAU,GAAGD,YAAnC;AAEA,kBAAIE,aAAa,KAAK,CAAtB,EAAyB,OAAO,KAAKxE,YAAL,CAAkBiC,CAAC,GAAG,CAAtB,EAAyBoC,KAAzB,EAAP;AAEzB,oBAAMI,CAAC,GAAG,CAACvC,QAAQ,GAAGoC,YAAZ,IAA4BE,aAAtC;AACA,qBAAOzF,IAAI,CAAC2F,IAAL,CAAU,IAAI3F,IAAJ,EAAV,EAAsB,KAAKiB,YAAL,CAAkBiC,CAAC,GAAG,CAAtB,CAAtB,EAAgD,KAAKjC,YAAL,CAAkBiC,CAAlB,CAAhD,EAAsEwC,CAAtE,CAAP;AACH;AACJ;;AAED,iBAAO,KAAKzE,YAAL,CAAkB,KAAKA,YAAL,CAAkB8B,MAAlB,GAA2B,CAA7C,EAAgDuC,KAAhD,EAAP;AACH;AAED;AACJ;AACA;;;AACYlB,QAAAA,sBAAsB,CAACjB,QAAD,EAAyB;AACnD,gBAAMyC,OAAO,GAAG,CAAhB;AACA,gBAAMC,IAAI,GAAG,KAAK7B,qBAAL,CAA2Bb,QAA3B,CAAb;AACA,gBAAM2C,IAAI,GAAG,KAAK9B,qBAAL,CAA2Bb,QAAQ,GAAGyC,OAAtC,CAAb;AAEA,iBAAO5F,IAAI,CAAC+F,QAAL,CAAc,IAAI/F,IAAJ,EAAd,EAA0B8F,IAA1B,EAAgCD,IAAhC,EAAsCG,SAAtC,EAAP;AACH;AAED;AACJ;AACA;;;AACWX,QAAAA,eAAe,GAAS;AAC3B,gBAAMY,WAAW,GAAG;AAAA;AAAA,4CAAaC,WAAjC;AACA,gBAAMnC,QAAQ,GAAG,KAAK9B,IAAL,CAAUkE,aAA3B;AACA,gBAAMtE,SAAS,GAAIkC,QAAQ,CAACU,CAAT,GAAa,KAAKlD,SAAL,CAAekD,CAA7B,IAAmCwB,WAAW,CAACG,IAA/C,IACbrC,QAAQ,CAACU,CAAT,GAAa,KAAKlD,SAAL,CAAekD,CAA7B,IAAmCwB,WAAW,CAACI,IADjC,IAEbtC,QAAQ,CAACQ,CAAT,GAAa,KAAKhD,SAAL,CAAegD,CAA7B,IAAmC0B,WAAW,CAACK,IAFjC,IAGbvC,QAAQ,CAACQ,CAAT,GAAa,KAAKhD,SAAL,CAAegD,CAA7B,IAAmC0B,WAAW,CAACM,IAHnD;AAKA,eAAKC,UAAL,CAAgB3E,SAAhB;AACH;;AAEO2E,QAAAA,UAAU,CAACC,OAAD,EAAmB;AACjC,cAAIA,OAAJ,EAAa;AACT,gBAAI,CAAC,KAAKjF,WAAV,EACI,KAAKkF,IAAL,CAAU;AAAA;AAAA,0CAAWC,eAArB;AACP,WAHD,MAGO;AACH,gBAAI,KAAKnF,WAAT,EACI,KAAKkF,IAAL,CAAU;AAAA;AAAA,0CAAWE,iBAArB;AACP;;AACD,eAAKpF,WAAL,GAAmB,KAAKC,UAAxB;AACA,eAAKA,UAAL,GAAkBgF,OAAlB;AACH,SAhP0D,CAkP3D;;;AACOI,QAAAA,EAAE,CAACC,KAAD,EAAoBC,QAApB,EAAgD;AACrD,cAAI,CAAC,KAAKpF,eAAL,CAAqBqF,GAArB,CAAyBF,KAAzB,CAAL,EAAsC;AAClC,iBAAKnF,eAAL,CAAqBY,GAArB,CAAyBuE,KAAzB,EAAgC,EAAhC;AACH;;AACD,gBAAMG,SAAS,GAAG,KAAKtF,eAAL,CAAqBuF,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAI,CAACG,SAAS,CAACE,QAAV,CAAmBJ,QAAnB,CAAL,EAAmC;AAC/BE,YAAAA,SAAS,CAAC7D,IAAV,CAAe2D,QAAf;AACH;AACJ;;AAEMK,QAAAA,GAAG,CAACN,KAAD,EAAoBC,QAApB,EAAgD;AACtD,gBAAME,SAAS,GAAG,KAAKtF,eAAL,CAAqBuF,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAIG,SAAJ,EAAe;AACX,kBAAMI,KAAK,GAAGJ,SAAS,CAACK,OAAV,CAAkBP,QAAlB,CAAd;;AACA,gBAAIM,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdJ,cAAAA,SAAS,CAACM,MAAV,CAAiBF,KAAjB,EAAwB,CAAxB;AACH;AACJ;AACJ;;AAEMG,QAAAA,kBAAkB,GAAS;AAC9B,eAAK7F,eAAL,CAAqBe,KAArB;AACH;;AAEOgE,QAAAA,IAAI,CAACI,KAAD,EAA0B;AAClC,gBAAMG,SAAS,GAAG,KAAKtF,eAAL,CAAqBuF,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAIG,SAAS,IAAIA,SAAS,CAAClE,MAAV,GAAmB,CAApC,EAAuC;AACnCkE,YAAAA,SAAS,CAACQ,OAAV,CAAkBV,QAAQ,IAAIA,QAAQ,EAAtC;AACH;AACJ,SAhR0D,CAkR3D;;;AACOW,QAAAA,UAAU,CAACC,OAAD,EAAgC;AAC7C,eAAKtG,UAAL,GAAkBsG,OAAlB;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,WAAW,CAACC,QAAD,EAAgC;AAC9C,eAAKzG,gBAAL,GAAwBqC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACqE,GAAL,CAAS,CAAT,EAAYD,QAAZ,CAAZ,IAAqC,KAAK3G,cAAlE;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACW6G,QAAAA,WAAW,GAAW;AACzB,iBAAO,KAAK7G,cAAL,GAAsB,CAAtB,GAA0B,KAAKE,gBAAL,GAAwB,KAAKF,cAAvD,GAAwE,CAA/E;AACH;AAED;AACJ;AACA;;;AACW8G,QAAAA,YAAY,GAAgB;AAC/B,eAAK5G,gBAAL,GAAwB,CAAxB;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACW6G,QAAAA,SAAS,GAAgB;AAC5B,eAAK7G,gBAAL,GAAwB,KAAKF,cAA7B;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWgH,QAAAA,uBAAuB,GAAG;AAC7B,cAAI,CAAC,KAAKlH,SAAN,IAAmB,KAAKA,SAAL,CAAe8B,MAAf,CAAsBC,MAAtB,KAAiC,CAAxD,EAA2D,OAAO,IAAP;AAE3D,gBAAM8E,QAAQ,GAAG,KAAKE,WAAL,EAAjB;AACA,gBAAMI,UAAU,GAAG1E,IAAI,CAAC2E,KAAL,CAAWP,QAAQ,IAAI,KAAK7G,SAAL,CAAe8B,MAAf,CAAsBC,MAAtB,GAA+B,CAAnC,CAAnB,CAAnB;AACA,gBAAMsF,YAAY,GAAG5E,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACqE,GAAL,CAAS,KAAK9G,SAAL,CAAe8B,MAAf,CAAsBC,MAAtB,GAA+B,CAAxC,EAA2CoF,UAA3C,CAAZ,CAArB;AAEA,iBAAO,KAAKnH,SAAL,CAAe8B,MAAf,CAAsBuF,YAAtB,CAAP;AACH;;AAlU0D,O,UA+CnCjD,yB,GAA4B,C;;;;;iBA1Cf,I;;;;;;;iBAGQ;AAAA;AAAA,4DAAqBkD,E;;;;;;;iBAGhC,I;;;;;;;iBAGX,G;;;;;;;iBAGO,C;;;;;;;iBAGP,K;;;;;;;iBAGG,K;;;;;;;iBAGC,C;;;;;;;iBAGC,C", "sourcesContent": ["import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';\nimport { BulletSystem } from '../bullet/BulletSystem';\nimport { IMovable } from './IMovable';\nimport Entity from '../ui/base/Entity';\nimport { PathData } from '../data/PathData';\nimport { eSpriteDefaultFacing, eMoveEvent } from './Movable';\n\nconst { degreesToRadians, radiansToDegrees } = misc;\nconst { ccclass, property } = _decorator;\n\n@ccclass('PathMovable')\nexport class PathMovable extends Component implements IMovable {\n    public speedAngle: number = 0;\n    public accelerationAngle: number = 0;\n\n    @property({ type: JsonAsset, displayName: \"路径数据\" })\n    public pathAsset: JsonAsset | null = null;\n\n    @property({ type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向' })\n    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;\n\n    @property({ displayName: \"是否朝向移动方向\" })\n    public isFacingMoveDir: boolean = true;\n\n    @property({ displayName: \"移动速度\", tooltip: \"沿路径移动的速度(像素/秒)\" })\n    public speed: number = 100;\n\n    @property({ displayName: \"加速度\", tooltip: \"速度变化率(像素/秒²)\" })\n    public acceleration: number = 0;\n\n    @property({ displayName: \"循环移动\" })\n    public loop: boolean = false;\n\n    @property({ displayName: \"反向移动\" })\n    public reverse: boolean = false;\n\n    @property({ displayName: \"振荡偏移速度\", tooltip: \"控制倾斜振荡的频率\" })\n    public tiltSpeed: number = 0;\n\n    @property({ displayName: \"振荡偏移幅度\", tooltip: \"控制倾斜振荡的幅度\" })\n    public tiltOffset: number = 0;\n\n    // 路径相关数据\n    private _pathData: PathData | null = null;\n    private _curvePoints: Vec2[] = [];\n    private _totalDistance: number = 0;\n    private _distances: number[] = [];\n\n    // 移动状态\n    private _currentDistance: number = 0;\n    private _isMovable: boolean = true;\n    private _tiltTime: number = 0;\n\n    // 可见性检查\n    private _selfSize: Vec2 = new Vec2();\n    private _wasVisible: boolean = false;\n    private _isVisible: boolean = false;\n    private _visibilityCheckCounter: number = 0;\n    private static readonly VISIBILITY_CHECK_INTERVAL = 5;\n\n    // 事件系统\n    private _eventListeners: Map<eMoveEvent, Array<() => void>> = new Map();\n\n    public get isVisible() { return this._isVisible; }\n    public get isMovable() { return this._isMovable; }\n\n    onLoad() {\n        const uiTransform = this.node.getComponent(UITransform);\n        const self_size = uiTransform ? uiTransform.contentSize : { width: 0, height: 0 };\n        this._selfSize.set(self_size.width / 2, self_size.height / 2);\n\n        this.loadPathData();\n    }\n\n    onDestroy() {\n        this._eventListeners.clear();\n    }\n\n    /**\n     * 加载路径数据（使用PathData内置缓存）\n     */\n    private loadPathData() {\n        if (!this.pathAsset) return;\n\n        // 创建PathData实例并加载数据\n        this._pathData = PathData.fromJSON(this.pathAsset.json);\n\n        if (this._pathData.points.length < 2) return;\n\n        // 使用PathData的内置缓存获取曲线点\n        this._curvePoints = this._pathData.generateCurvePoints();\n\n        // 计算距离信息\n        this.calculateDistances();\n    }\n\n    /**\n     * 计算距离信息\n     */\n    private calculateDistances() {\n        this._distances = [0];\n        this._totalDistance = 0;\n\n        for (let i = 1; i < this._curvePoints.length; i++) {\n            const distance = Vec2.distance(this._curvePoints[i - 1], this._curvePoints[i]);\n            this._totalDistance += distance;\n            this._distances.push(this._totalDistance);\n        }\n    }\n\n\n\n    /**\n     * 主要的移动更新逻辑\n     */\n    public tick(dt: number): void {\n        if (!this._isMovable || this._curvePoints.length < 2) return;\n\n        // 应用加速度\n        if (this.acceleration !== 0) {\n            this.speed += this.acceleration * dt;\n            this.speed = Math.max(0, this.speed); // 确保速度不为负\n        }\n\n        // 更新沿路径的距离\n        const deltaDistance = this.speed * dt;\n        \n        if (this.reverse) {\n            this._currentDistance -= deltaDistance;\n            if (this._currentDistance < 0) {\n                if (this.loop) {\n                    this._currentDistance = this._totalDistance + this._currentDistance;\n                } else {\n                    this._currentDistance = 0;\n                }\n            }\n        } else {\n            this._currentDistance += deltaDistance;\n            if (this._currentDistance > this._totalDistance) {\n                if (this.loop) {\n                    this._currentDistance = this._currentDistance - this._totalDistance;\n                } else {\n                    this._currentDistance = this._totalDistance;\n                }\n            }\n        }\n\n        this.updatePosition(dt);\n    }\n\n    /**\n     * 更新节点位置和朝向\n     */\n    private updatePosition(dt: number) {\n        const position = this.getPositionAtDistance(this._currentDistance);\n        \n        // 应用倾斜偏移\n        if (this.tiltSpeed > 0 && this.tiltOffset > 0) {\n            this._tiltTime += dt;\n            \n            const direction = this.getDirectionAtDistance(this._currentDistance);\n            if (direction.lengthSqr() > 0.001) {\n                // 计算垂直于移动方向的向量\n                const perpX = -direction.y;\n                const perpY = direction.x;\n                \n                // 计算倾斜偏移\n                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;\n                \n                position.x += perpX * tiltAmount;\n                position.y += perpY * tiltAmount;\n            }\n        }\n\n        this.node.setPosition(position.x, position.y, 0);\n\n        // 更新朝向\n        if (this.isFacingMoveDir) {\n            const direction = this.getDirectionAtDistance(this._currentDistance);\n            if (direction.lengthSqr() > 0.001) {\n                const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;\n                const finalAngle = angle + this.defaultFacing;\n                this.node.setRotationFromEuler(0, 0, finalAngle);\n            }\n        }\n\n        // 可见性检查\n        if (++this._visibilityCheckCounter >= PathMovable.VISIBILITY_CHECK_INTERVAL) {\n            this._visibilityCheckCounter = 0;\n            this.checkVisibility();\n        }\n    }\n\n    /**\n     * 根据距离获取位置\n     */\n    private getPositionAtDistance(distance: number): Vec2 {\n        if (distance <= 0) return this._curvePoints[0].clone();\n        if (distance >= this._totalDistance) return this._curvePoints[this._curvePoints.length - 1].clone();\n\n        for (let i = 1; i < this._distances.length; i++) {\n            if (distance <= this._distances[i]) {\n                const segmentStart = this._distances[i - 1];\n                const segmentEnd = this._distances[i];\n                const segmentLength = segmentEnd - segmentStart;\n\n                if (segmentLength === 0) return this._curvePoints[i - 1].clone();\n\n                const t = (distance - segmentStart) / segmentLength;\n                return Vec2.lerp(new Vec2(), this._curvePoints[i - 1], this._curvePoints[i], t);\n            }\n        }\n\n        return this._curvePoints[this._curvePoints.length - 1].clone();\n    }\n\n    /**\n     * 根据距离获取移动方向\n     */\n    private getDirectionAtDistance(distance: number): Vec2 {\n        const epsilon = 1;\n        const pos1 = this.getPositionAtDistance(distance);\n        const pos2 = this.getPositionAtDistance(distance + epsilon);\n\n        return Vec2.subtract(new Vec2(), pos2, pos1).normalize();\n    }\n\n    /**\n     * 可见性检查\n     */\n    public checkVisibility(): void {\n        const visibleSize = BulletSystem.worldBounds;\n        const position = this.node.worldPosition;\n        const isVisible = (position.x + this._selfSize.x) >= visibleSize.xMin &&\n            (position.x - this._selfSize.x) <= visibleSize.xMax &&\n            (position.y - this._selfSize.y) <= visibleSize.yMax &&\n            (position.y + this._selfSize.y) >= visibleSize.yMin;\n\n        this.setVisible(isVisible);\n    }\n\n    private setVisible(visible: boolean) {\n        if (visible) {\n            if (!this._wasVisible)\n                this.emit(eMoveEvent.onBecomeVisible);\n        } else {\n            if (this._wasVisible)\n                this.emit(eMoveEvent.onBecomeInvisible);\n        }\n        this._wasVisible = this._isVisible;\n        this._isVisible = visible;\n    }\n\n    // 事件系统方法\n    public on(event: eMoveEvent, listener: () => void): void {\n        if (!this._eventListeners.has(event)) {\n            this._eventListeners.set(event, []);\n        }\n        const listeners = this._eventListeners.get(event)!;\n        if (!listeners.includes(listener)) {\n            listeners.push(listener);\n        }\n    }\n\n    public off(event: eMoveEvent, listener: () => void): void {\n        const listeners = this._eventListeners.get(event);\n        if (listeners) {\n            const index = listeners.indexOf(listener);\n            if (index !== -1) {\n                listeners.splice(index, 1);\n            }\n        }\n    }\n\n    public removeAllListeners(): void {\n        this._eventListeners.clear();\n    }\n\n    private emit(event: eMoveEvent): void {\n        const listeners = this._eventListeners.get(event);\n        if (listeners && listeners.length > 0) {\n            listeners.forEach(listener => listener());\n        }\n    }\n\n    // 公共API方法\n    public setMovable(movable: boolean): PathMovable {\n        this._isMovable = movable;\n        return this;\n    }\n\n    /**\n     * 设置路径进度 [0-1]\n     */\n    public setProgress(progress: number): PathMovable {\n        this._currentDistance = Math.max(0, Math.min(1, progress)) * this._totalDistance;\n        return this;\n    }\n\n    /**\n     * 获取当前进度 [0-1]\n     */\n    public getProgress(): number {\n        return this._totalDistance > 0 ? this._currentDistance / this._totalDistance : 0;\n    }\n\n    /**\n     * 重置到路径起点\n     */\n    public resetToStart(): PathMovable {\n        this._currentDistance = 0;\n        return this;\n    }\n\n    /**\n     * 移动到路径终点\n     */\n    public moveToEnd(): PathMovable {\n        this._currentDistance = this._totalDistance;\n        return this;\n    }\n\n    /**\n     * 获取当前位置对应的路径点数据\n     */\n    public getCurrentPathPointData() {\n        if (!this._pathData || this._pathData.points.length === 0) return null;\n\n        const progress = this.getProgress();\n        const pointIndex = Math.floor(progress * (this._pathData.points.length - 1));\n        const clampedIndex = Math.max(0, Math.min(this._pathData.points.length - 1, pointIndex));\n        \n        return this._pathData.points[clampedIndex];\n    }\n\n\n}\n"]}