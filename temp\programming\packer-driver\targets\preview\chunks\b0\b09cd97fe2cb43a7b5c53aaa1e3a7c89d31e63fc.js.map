{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts"], "names": ["_decorator", "Node", "Color", "Component", "JsonAsset", "Graphics", "PathData", "PathPoint", "PathPointEditor", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "requireComponent", "PathEditor", "type", "displayName", "_graphics", "_pathData", "_pathDataObj", "_cachedChildrenCount", "graphics", "node", "getComponent", "addComponent", "pathData", "value", "reload", "pathName", "name", "Object", "assign", "json", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "points", "length", "for<PERSON>ach", "point", "addPoint", "updateCurve", "save", "pointEditors", "getComponentsInChildren", "map", "editor", "pathPoint", "JSON", "stringify", "pointNode", "parent", "setPosition", "position", "x", "y", "pointEditor", "addNewPoint", "showCurve", "drawPath", "clear", "showControlPoints", "strokeColor", "controlLineColor", "lineWidth", "i", "p1", "p2", "moveTo", "lineTo", "stroke", "curveColor", "curvePoints", "generateCurvePoints", "update", "_dt", "childrenCount", "children", "WHITE", "GRAY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,Q,OAAAA,Q;;AAGrDC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;AACVC,MAAAA,e,iBAAAA,e;;;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA,IAA1D;AAAgEC,QAAAA;AAAhE,O,GAAqFd,U;;4BAU9Ee,U,WALZN,OAAO,CAAC,YAAD,C,UACPI,IAAI,CAAC,aAAD,C,UACJC,gBAAgB,CAACT,QAAD,C,UAChBK,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAUZD,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEZ,SAAR;AAAmBa,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UASRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAQRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,uFAxCb,MAKaF,UALb,SAKgCZ,SALhC,CAK0C;AAAA;AAAA;AAAA,eAC9Be,SAD8B,GACD,IADC;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAsC9BC,SAtC8B,GAsCA,IAtCA;AAAA,eAuC9BC,YAvC8B,GAuCL;AAAA;AAAA,qCAvCK;AAAA,eAwC9BC,oBAxC8B,GAwCC,CAxCD;AAAA;;AAEnB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKJ,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKK,IAAL,CAAUC,YAAV,CAAuBnB,QAAvB,KAAoC,KAAKkB,IAAL,CAAUE,YAAV,CAAuBpB,QAAvB,CAArD;AACH;;AACD,iBAAO,KAAKa,SAAZ;AACH;;AAGkB,YAARQ,QAAQ,CAACC,KAAD,EAAmB;AAClC,eAAKR,SAAL,GAAiBQ,KAAjB;AACA,eAAKC,MAAL;AACH;;AACkB,YAARF,QAAQ,GAAqB;AACpC,iBAAO,KAAKP,SAAZ;AACH;;AAGkB,YAARU,QAAQ,GAAW;AAC1B,iBAAO,KAAKT,YAAL,CAAkBU,IAAzB;AACH;;AACkB,YAARD,QAAQ,CAACF,KAAD,EAAgB;AAC/B,eAAKP,YAAL,CAAkBU,IAAlB,GAAyBH,KAAzB;AACH;;AAkBMC,QAAAA,MAAM,GAAG;AACZ,cAAI,CAAC,KAAKT,SAAV,EAAqB;AAErB,cAAMO,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAK,UAAAA,MAAM,CAACC,MAAP,CAAcN,QAAd,EAAwB,KAAKP,SAAL,CAAec,IAAvC;AACA,eAAKb,YAAL,GAAoBM,QAApB;AAEA,eAAKH,IAAL,CAAUW,iBAAV;;AACA,cAAI,KAAKd,YAAL,IAAqB,KAAKA,YAAL,CAAkBe,MAAlB,CAAyBC,MAAzB,GAAkC,CAA3D,EAA8D;AAC1D,iBAAKhB,YAAL,CAAkBe,MAAlB,CAAyBE,OAAzB,CAAkCC,KAAD,IAAW;AACxC,mBAAKC,QAAL,CAAcD,KAAd;AACH,aAFD;AAGH;;AACD,eAAKE,WAAL;AACH;;AAEMC,QAAAA,IAAI,GAAW;AAClB;AACA,cAAMC,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAKvB,YAAL,CAAkBe,MAAlB,GAA2BO,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACA,iBAAOC,IAAI,CAACC,SAAL,CAAe,KAAK5B,YAApB,EAAkC,IAAlC,EAAwC,CAAxC,CAAP;AACH;;AAEMmB,QAAAA,QAAQ,CAACD,KAAD,EAAmB;AAC9B,cAAMW,SAAS,GAAG,IAAIhD,IAAJ,EAAlB;AACAgD,UAAAA,SAAS,CAACC,MAAV,GAAmB,KAAK3B,IAAxB;AACA0B,UAAAA,SAAS,CAACE,WAAV,CAAsBb,KAAK,CAACc,QAAN,CAAeC,CAArC,EAAwCf,KAAK,CAACc,QAAN,CAAeE,CAAvD,EAA0D,CAA1D;AAEA,cAAMC,WAAW,GAAGN,SAAS,CAACxB,YAAV;AAAA;AAAA,iDAApB;AACA8B,UAAAA,WAAW,CAACT,SAAZ,GAAwBR,KAAxB;AACH;;AAEMkB,QAAAA,WAAW,CAACH,CAAD,EAAYC,CAAZ,EAAuB;AACrC,cAAMhB,KAAK,GAAG;AAAA;AAAA,sCAAce,CAAd,EAAiBC,CAAjB,CAAd;AACA,eAAKf,QAAL,CAAcD,KAAd;AACA,eAAKE,WAAL;AACH;;AAEMA,QAAAA,WAAW,GAAG;AACjB,cAAI,CAAC,KAAKiB,SAAV,EAAqB,OADJ,CAGjB;;AACA,cAAMf,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAKvB,YAAL,CAAkBe,MAAlB,GAA2BO,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACH;;AAEOY,QAAAA,QAAQ,GAAG;AACf,cAAMpC,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACqC,KAAT;AAEA,cAAI,KAAKvC,YAAL,CAAkBe,MAAlB,CAAyBC,MAAzB,GAAkC,CAAtC,EAAyC,OAJ1B,CAMf;;AACA,cAAI,KAAKwB,iBAAT,EAA4B;AACxBtC,YAAAA,QAAQ,CAACuC,WAAT,GAAuB,KAAKC,gBAA5B;AACAxC,YAAAA,QAAQ,CAACyC,SAAT,GAAqB,CAArB;;AAEA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK5C,YAAL,CAAkBe,MAAlB,CAAyBC,MAAzB,GAAkC,CAAtD,EAAyD4B,CAAC,EAA1D,EAA8D;AAC1D,kBAAMC,EAAE,GAAG,KAAK7C,YAAL,CAAkBe,MAAlB,CAAyB6B,CAAzB,EAA4BZ,QAAvC;AACA,kBAAMc,EAAE,GAAG,KAAK9C,YAAL,CAAkBe,MAAlB,CAAyB6B,CAAC,GAAG,CAA7B,EAAgCZ,QAA3C;AAEA9B,cAAAA,QAAQ,CAAC6C,MAAT,CAAgBF,EAAE,CAACZ,CAAnB,EAAsBY,EAAE,CAACX,CAAzB;AACAhC,cAAAA,QAAQ,CAAC8C,MAAT,CAAgBF,EAAE,CAACb,CAAnB,EAAsBa,EAAE,CAACZ,CAAzB;AACH;;AACDhC,YAAAA,QAAQ,CAAC+C,MAAT;AACH,WAnBc,CAqBf;;;AACA/C,UAAAA,QAAQ,CAACuC,WAAT,GAAuB,KAAKS,UAA5B;AACAhD,UAAAA,QAAQ,CAACyC,SAAT,GAAqB,CAArB;;AAEA,cAAMQ,WAAW,GAAG,KAAKnD,YAAL,CAAkBoD,mBAAlB,EAApB;;AACA,cAAID,WAAW,CAACnC,MAAZ,GAAqB,CAAzB,EAA4B;AACxBd,YAAAA,QAAQ,CAAC6C,MAAT,CAAgBI,WAAW,CAAC,CAAD,CAAX,CAAelB,CAA/B,EAAkCkB,WAAW,CAAC,CAAD,CAAX,CAAejB,CAAjD;;AACA,iBAAK,IAAIU,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGO,WAAW,CAACnC,MAAhC,EAAwC4B,EAAC,EAAzC,EAA6C;AACzC1C,cAAAA,QAAQ,CAAC8C,MAAT,CAAgBG,WAAW,CAACP,EAAD,CAAX,CAAeX,CAA/B,EAAkCkB,WAAW,CAACP,EAAD,CAAX,CAAeV,CAAjD;AACH;;AACDhC,YAAAA,QAAQ,CAAC+C,MAAT;AACH;AACJ;;AAEMI,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB,cAAMC,aAAa,GAAG,KAAKpD,IAAL,CAAUqD,QAAV,CAAmBxC,MAAzC;;AACA,cAAIuC,aAAa,KAAK,KAAKtD,oBAA3B,EAAiD;AAC7C,iBAAKA,oBAAL,GAA4BsD,aAA5B;AACH;;AACD,eAAKnC,WAAL;AACA,eAAKkB,QAAL;AACH;;AAlIqC,O;;;;;iBA2BV,I;;;;;;;iBAGQ,I;;;;;;;iBAGTxD,KAAK,CAAC2E,K;;;;;;;iBAGA3E,KAAK,CAAC4E,I", "sourcesContent": ["import { _decorator, Node, Color, Component, JsonAsset, Vec3, Graphics, input, Input, EventMouse } from 'cc';\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent } = _decorator;\r\nimport { EDITOR } from 'cc/env';\r\nimport { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { PathPointEditor } from './PathPointEditor';\r\n\r\n@ccclass('PathEditor')\r\n@menu(\"怪物/编辑器/路径编辑\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathEditor extends Component {\r\n    private _graphics: Graphics | null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n        return this._graphics;\r\n    }\r\n\r\n    @property({ type: JsonAsset, displayName: \"路径数据\" })\r\n    public set pathData(value: JsonAsset) {\r\n        this._pathData = value;\r\n        this.reload();\r\n    }\r\n    public get pathData(): JsonAsset | null {\r\n        return this._pathData;\r\n    }\r\n\r\n    @property({ displayName: \"路径名称\" })\r\n    public get pathName(): string {\r\n        return this._pathDataObj.name;\r\n    }\r\n    public set pathName(value: string) {\r\n        this._pathDataObj.name = value;\r\n    }\r\n\r\n    @property({ displayName: \"显示曲线\" })\r\n    public showCurve: boolean = true;\r\n\r\n    @property({ displayName: \"显示控制点\" })\r\n    public showControlPoints: boolean = true;\r\n\r\n    @property({ displayName: \"曲线颜色\" })\r\n    public curveColor: Color = Color.WHITE;\r\n\r\n    @property({ displayName: \"控制线颜色\" })\r\n    public controlLineColor: Color = Color.GRAY;\r\n\r\n    private _pathData: JsonAsset | null = null;\r\n    private _pathDataObj: PathData = new PathData();\r\n    private _cachedChildrenCount: number = 0;\r\n\r\n    public reload() {\r\n        if (!this._pathData) return;\r\n\r\n        const pathData = new PathData();\r\n        Object.assign(pathData, this._pathData.json);\r\n        this._pathDataObj = pathData;\r\n\r\n        this.node.removeAllChildren();\r\n        if (this._pathDataObj && this._pathDataObj.points.length > 0) {\r\n            this._pathDataObj.points.forEach((point) => {\r\n                this.addPoint(point);\r\n            });\r\n        }\r\n        this.updateCurve();\r\n    }\r\n\r\n    public save(): string {\r\n        // 收集所有路径点数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n        return JSON.stringify(this._pathDataObj, null, 2);\r\n    }\r\n\r\n    public addPoint(point: PathPoint) {\r\n        const pointNode = new Node();\r\n        pointNode.parent = this.node;\r\n        pointNode.setPosition(point.position.x, point.position.y, 0);\r\n\r\n        const pointEditor = pointNode.addComponent(PathPointEditor);\r\n        pointEditor.pathPoint = point;\r\n    }\r\n\r\n    public addNewPoint(x: number, y: number) {\r\n        const point = new PathPoint(x, y);\r\n        this.addPoint(point);\r\n        this.updateCurve();\r\n    }\r\n\r\n    public updateCurve() {\r\n        if (!this.showCurve) return;\r\n\r\n        // 收集当前所有点的数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n    }\r\n\r\n    private drawPath() {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n\r\n        if (this._pathDataObj.points.length < 2) return;\r\n\r\n        // 绘制控制线（连接原始控制点）\r\n        if (this.showControlPoints) {\r\n            graphics.strokeColor = this.controlLineColor;\r\n            graphics.lineWidth = 5;\r\n\r\n            for (let i = 0; i < this._pathDataObj.points.length - 1; i++) {\r\n                const p1 = this._pathDataObj.points[i].position;\r\n                const p2 = this._pathDataObj.points[i + 1].position;\r\n\r\n                graphics.moveTo(p1.x, p1.y);\r\n                graphics.lineTo(p2.x, p2.y);\r\n            }\r\n            graphics.stroke();\r\n        }\r\n\r\n        // 绘制Catmull-Rom曲线\r\n        graphics.strokeColor = this.curveColor;\r\n        graphics.lineWidth = 5;\r\n\r\n        const curvePoints = this._pathDataObj.generateCurvePoints();\r\n        if (curvePoints.length > 1) {\r\n            graphics.moveTo(curvePoints[0].x, curvePoints[0].y);\r\n            for (let i = 1; i < curvePoints.length; i++) {\r\n                graphics.lineTo(curvePoints[i].x, curvePoints[i].y);\r\n            }\r\n            graphics.stroke();\r\n        }\r\n    }\r\n\r\n    public update(_dt: number) {\r\n        const childrenCount = this.node.children.length;\r\n        if (childrenCount !== this._cachedChildrenCount) {\r\n            this._cachedChildrenCount = childrenCount;\r\n        }\r\n        this.updateCurve();\r\n        this.drawPath();\r\n    }\r\n}"]}