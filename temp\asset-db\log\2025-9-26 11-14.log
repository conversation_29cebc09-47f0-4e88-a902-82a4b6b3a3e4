2025-9-26 11:14:06-debug: start **** info
2025-9-26 11:14:06-log: Cannot access game frame or container.
2025-9-26 11:14:06-debug: asset-db:require-engine-code (428ms)
2025-9-26 11:14:06-log: meshopt wasm decoder initialized
2025-9-26 11:14:06-log: [box2d]:box2d wasm lib loaded.
2025-9-26 11:14:06-log: [bullet]:bullet wasm lib loaded.
2025-9-26 11:14:06-log: Cocos Creator v3.8.6
2025-9-26 11:14:06-log: Forward render pipeline initialized.
2025-9-26 11:14:06-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.88MB, end 79.84MB, increase: 48.95MB
2025-9-26 11:14:07-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.18MB, end 224.60MB, increase: 140.41MB
2025-9-26 11:14:06-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.99MB, end 84.14MB, increase: 3.15MB
2025-9-26 11:14:07-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.84MB, end 228.23MB, increase: 147.38MB
2025-9-26 11:14:07-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:224.85MB, end 228.01MB, increase: 3.16MB
2025-9-26 11:14:06-log: Using legacy pipeline
2025-9-26 11:14:07-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:79.85MB, end 228.25MB, increase: 148.40MB
2025-9-26 11:14:07-debug: run package(harmonyos-next) handler(enable) start
2025-9-26 11:14:07-debug: run package(honor-mini-game) handler(enable) success!
2025-9-26 11:14:07-debug: run package(honor-mini-game) handler(enable) start
2025-9-26 11:14:07-debug: run package(harmonyos-next) handler(enable) success!
2025-9-26 11:14:07-debug: run package(huawei-agc) handler(enable) start
2025-9-26 11:14:07-debug: run package(huawei-quick-game) handler(enable) start
2025-9-26 11:14:07-debug: run package(huawei-agc) handler(enable) success!
2025-9-26 11:14:07-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-26 11:14:07-debug: run package(ios) handler(enable) start
2025-9-26 11:14:07-debug: run package(ios) handler(enable) success!
2025-9-26 11:14:07-debug: run package(linux) handler(enable) start
2025-9-26 11:14:07-debug: run package(mac) handler(enable) start
2025-9-26 11:14:07-debug: run package(migu-mini-game) handler(enable) start
2025-9-26 11:14:07-debug: run package(linux) handler(enable) success!
2025-9-26 11:14:07-debug: run package(native) handler(enable) start
2025-9-26 11:14:07-debug: run package(mac) handler(enable) success!
2025-9-26 11:14:07-debug: run package(migu-mini-game) handler(enable) success!
2025-9-26 11:14:07-debug: run package(native) handler(enable) success!
2025-9-26 11:14:07-debug: run package(ohos) handler(enable) success!
2025-9-26 11:14:07-debug: run package(oppo-mini-game) handler(enable) start
2025-9-26 11:14:07-debug: run package(ohos) handler(enable) start
2025-9-26 11:14:07-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-26 11:14:07-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-26 11:14:07-debug: run package(taobao-mini-game) handler(enable) start
2025-9-26 11:14:07-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-26 11:14:07-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-26 11:14:07-debug: run package(vivo-mini-game) handler(enable) start
2025-9-26 11:14:07-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-26 11:14:07-debug: run package(web-desktop) handler(enable) success!
2025-9-26 11:14:07-debug: run package(web-desktop) handler(enable) start
2025-9-26 11:14:07-debug: run package(web-mobile) handler(enable) start
2025-9-26 11:14:07-debug: run package(web-mobile) handler(enable) success!
2025-9-26 11:14:07-debug: run package(wechatprogram) handler(enable) start
2025-9-26 11:14:07-debug: run package(wechatgame) handler(enable) success!
2025-9-26 11:14:07-debug: run package(wechatprogram) handler(enable) success!
2025-9-26 11:14:07-debug: run package(wechatgame) handler(enable) start
2025-9-26 11:14:07-debug: run package(windows) handler(enable) start
2025-9-26 11:14:07-debug: run package(windows) handler(enable) success!
2025-9-26 11:14:07-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-26 11:14:07-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-26 11:14:07-debug: run package(cocos-service) handler(enable) success!
2025-9-26 11:14:07-debug: run package(im-plugin) handler(enable) success!
2025-9-26 11:14:07-debug: run package(cocos-service) handler(enable) start
2025-9-26 11:14:07-debug: run package(im-plugin) handler(enable) start
2025-9-26 11:14:07-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-26 11:14:07-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-26 11:14:07-debug: run package(emitter-editor) handler(enable) success!
2025-9-26 11:14:07-debug: run package(emitter-editor) handler(enable) start
2025-9-26 11:14:07-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-9-26 11:14:07-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-9-26 11:14:07-debug: run package(i18n) handler(enable) start
2025-9-26 11:14:07-debug: refresh asset db://assets/editor/enum-gen success
2025-9-26 11:14:07-debug: run package(i18n) handler(enable) success!
2025-9-26 11:14:07-debug: refresh asset db://assets/editor/enum-gen success
2025-9-26 11:14:07-debug: asset-db:worker-init: initPlugin (966ms)
2025-9-26 11:14:07-debug: run package(level-editor) handler(enable) start
2025-9-26 11:14:07-debug: run package(level-editor) handler(enable) success!
2025-9-26 11:14:07-debug: [Assets Memory track]: asset-db:worker-init start:30.87MB, end 225.66MB, increase: 194.78MB
2025-9-26 11:14:07-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-26 11:14:07-debug: Run asset db hook programming:beforePreStart success!
2025-9-26 11:14:07-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-26 11:14:07-debug: Run asset db hook programming:beforePreStart ...
2025-9-26 11:14:07-debug: Preimport db internal success
2025-9-26 11:14:07-debug: run package(localization-editor) handler(enable) start
2025-9-26 11:14:07-debug: run package(localization-editor) handler(enable) success!
2025-9-26 11:14:07-debug: asset-db:worker-init (1560ms)
2025-9-26 11:14:07-debug: asset-db-hook-engine-extends-beforePreStart (101ms)
2025-9-26 11:14:07-debug: asset-db-hook-programming-beforePreStart (101ms)
2025-9-26 11:14:07-debug: run package(placeholder) handler(enable) start
2025-9-26 11:14:07-debug: run package(wave-editor) handler(enable) success!
2025-9-26 11:14:07-debug: run package(wave-editor) handler(enable) start
2025-9-26 11:14:07-debug: run package(placeholder) handler(enable) success!
2025-9-26 11:14:07-debug: Preimport db assets success
2025-9-26 11:14:07-debug: %cImport%c: E:\M2Game\Client\extensions\i18n\assets\LocalizedLabel.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:14:07-debug: %cImport%c: E:\M2Game\Client\extensions\i18n\assets\LocalizedSprite.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:14:07-debug: %cImport%c: E:\M2Game\Client\extensions\i18n\assets\LanguageData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:14:07-debug: Preimport db i18n success
2025-9-26 11:14:07-debug: Run asset db hook programming:afterPreStart ...
2025-9-26 11:14:07-debug: starting packer-driver...
2025-9-26 11:14:14-debug: initialize scripting environment...
2025-9-26 11:14:14-debug: [[Executor]] prepare before lock
2025-9-26 11:14:14-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-26 11:14:14-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-26 11:14:14-debug: Run asset db hook programming:afterPreStart success!
2025-9-26 11:14:14-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-26 11:14:14-debug: [Assets Memory track]: asset-db:worker-init: preStart start:225.67MB, end 246.14MB, increase: 20.47MB
2025-9-26 11:14:14-debug: Start up the 'internal' database...
2025-9-26 11:14:14-debug: [[Executor]] prepare after unlock
2025-9-26 11:14:15-debug: asset-db-hook-programming-afterPreStart (7303ms)
2025-9-26 11:14:15-debug: asset-db:worker-effect-data-processing (216ms)
2025-9-26 11:14:15-debug: asset-db-hook-engine-extends-afterPreStart (216ms)
2025-9-26 11:14:15-debug: Start up the 'assets' database...
2025-9-26 11:14:15-debug: asset-db:worker-startup-database[internal] (7568ms)
2025-9-26 11:14:15-debug: Start up the 'i18n' database...
2025-9-26 11:14:15-debug: asset-db:worker-startup-database[assets] (7536ms)
2025-9-26 11:14:15-debug: lazy register asset handler *
2025-9-26 11:14:15-debug: lazy register asset handler directory
2025-9-26 11:14:15-debug: lazy register asset handler spine-data
2025-9-26 11:14:15-debug: lazy register asset handler json
2025-9-26 11:14:15-debug: lazy register asset handler dragonbones
2025-9-26 11:14:15-debug: lazy register asset handler dragonbones-atlas
2025-9-26 11:14:15-debug: lazy register asset handler javascript
2025-9-26 11:14:15-debug: lazy register asset handler text
2025-9-26 11:14:15-debug: lazy register asset handler terrain
2025-9-26 11:14:15-debug: lazy register asset handler typescript
2025-9-26 11:14:15-debug: lazy register asset handler sprite-frame
2025-9-26 11:14:15-debug: lazy register asset handler scene
2025-9-26 11:14:15-debug: lazy register asset handler prefab
2025-9-26 11:14:15-debug: lazy register asset handler tiled-map
2025-9-26 11:14:15-debug: lazy register asset handler image
2025-9-26 11:14:15-debug: lazy register asset handler sign-image
2025-9-26 11:14:15-debug: lazy register asset handler alpha-image
2025-9-26 11:14:15-debug: lazy register asset handler texture-cube
2025-9-26 11:14:15-debug: lazy register asset handler buffer
2025-9-26 11:14:15-debug: lazy register asset handler texture
2025-9-26 11:14:15-debug: lazy register asset handler render-texture
2025-9-26 11:14:15-debug: lazy register asset handler erp-texture-cube
2025-9-26 11:14:15-debug: lazy register asset handler texture-cube-face
2025-9-26 11:14:15-debug: lazy register asset handler rt-sprite-frame
2025-9-26 11:14:15-debug: lazy register asset handler gltf
2025-9-26 11:14:15-debug: lazy register asset handler gltf-material
2025-9-26 11:14:15-debug: lazy register asset handler gltf-mesh
2025-9-26 11:14:15-debug: lazy register asset handler gltf-scene
2025-9-26 11:14:15-debug: lazy register asset handler gltf-skeleton
2025-9-26 11:14:15-debug: lazy register asset handler gltf-animation
2025-9-26 11:14:15-debug: lazy register asset handler gltf-embeded-image
2025-9-26 11:14:15-debug: lazy register asset handler physics-material
2025-9-26 11:14:15-debug: lazy register asset handler fbx
2025-9-26 11:14:15-debug: lazy register asset handler material
2025-9-26 11:14:15-debug: lazy register asset handler audio-clip
2025-9-26 11:14:15-debug: lazy register asset handler animation-clip
2025-9-26 11:14:15-debug: lazy register asset handler effect-header
2025-9-26 11:14:15-debug: lazy register asset handler effect
2025-9-26 11:14:15-debug: lazy register asset handler animation-graph-variant
2025-9-26 11:14:15-debug: lazy register asset handler animation-graph
2025-9-26 11:14:15-debug: lazy register asset handler animation-mask
2025-9-26 11:14:15-debug: lazy register asset handler ttf-font
2025-9-26 11:14:15-debug: lazy register asset handler bitmap-font
2025-9-26 11:14:15-debug: lazy register asset handler sprite-atlas
2025-9-26 11:14:15-debug: lazy register asset handler auto-atlas
2025-9-26 11:14:15-debug: lazy register asset handler label-atlas
2025-9-26 11:14:15-debug: lazy register asset handler particle
2025-9-26 11:14:15-debug: lazy register asset handler render-stage
2025-9-26 11:14:15-debug: lazy register asset handler render-pipeline
2025-9-26 11:14:15-debug: lazy register asset handler render-flow
2025-9-26 11:14:15-debug: lazy register asset handler instantiation-material
2025-9-26 11:14:15-debug: lazy register asset handler instantiation-skeleton
2025-9-26 11:14:15-debug: lazy register asset handler instantiation-animation
2025-9-26 11:14:15-debug: lazy register asset handler video-clip
2025-9-26 11:14:15-debug: lazy register asset handler instantiation-mesh
2025-9-26 11:14:15-debug: asset-db:worker-startup-database[i18n] (7494ms)
2025-9-26 11:14:15-debug: asset-db:start-database (7656ms)
2025-9-26 11:14:15-debug: asset-db:ready (10667ms)
2025-9-26 11:14:15-debug: fix the bug of updateDefaultUserData
2025-9-26 11:14:15-debug: init worker message success
2025-9-26 11:14:15-debug: programming:execute-script (4ms)
2025-9-26 11:14:15-debug: [Build Memory track]: builder:worker-init start:199.25MB, end 211.60MB, increase: 12.36MB
2025-9-26 11:14:15-debug: builder:worker-init (270ms)
2025-9-26 11:14:22-debug: refresh db internal success
2025-9-26 11:14:22-debug: refresh db assets success
2025-9-26 11:14:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:14:22-debug: refresh db i18n success
2025-9-26 11:14:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:14:22-debug: asset-db:refresh-all-database (158ms)
2025-9-26 11:14:22-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 11:14:22-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 11:15:27-debug: refresh db internal success
2025-9-26 11:15:27-debug: refresh db assets success
2025-9-26 11:15:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:15:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:15:27-debug: refresh db i18n success
2025-9-26 11:15:27-debug: asset-db:refresh-all-database (173ms)
2025-9-26 11:15:58-debug: refresh db internal success
2025-9-26 11:15:58-debug: refresh db assets success
2025-9-26 11:15:58-debug: refresh db i18n success
2025-9-26 11:15:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:15:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:15:58-debug: asset-db:refresh-all-database (198ms)
2025-9-26 11:15:59-debug: refresh db internal success
2025-9-26 11:15:59-debug: refresh db assets success
2025-9-26 11:15:59-debug: refresh db i18n success
2025-9-26 11:15:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:15:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:15:59-debug: asset-db:refresh-all-database (159ms)
2025-9-26 11:17:07-debug: refresh db internal success
2025-9-26 11:17:07-debug: refresh db assets success
2025-9-26 11:17:07-debug: refresh db i18n success
2025-9-26 11:17:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:17:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:17:07-debug: asset-db:refresh-all-database (117ms)
2025-9-26 11:17:51-debug: refresh db internal success
2025-9-26 11:17:51-debug: refresh db assets success
2025-9-26 11:17:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:17:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:17:51-debug: refresh db i18n success
2025-9-26 11:17:51-debug: asset-db:refresh-all-database (146ms)
2025-9-26 11:19:45-debug: refresh db internal success
2025-9-26 11:19:45-debug: refresh db assets success
2025-9-26 11:19:45-debug: refresh db i18n success
2025-9-26 11:19:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:19:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:19:45-debug: asset-db:refresh-all-database (124ms)
2025-9-26 11:19:56-debug: refresh db internal success
2025-9-26 11:19:57-debug: refresh db assets success
2025-9-26 11:19:57-debug: refresh db i18n success
2025-9-26 11:19:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:19:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:19:57-debug: asset-db:refresh-all-database (121ms)
2025-9-26 11:25:37-debug: refresh db internal success
2025-9-26 11:25:37-debug: refresh db assets success
2025-9-26 11:25:37-debug: refresh db i18n success
2025-9-26 11:25:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:25:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:25:37-debug: asset-db:refresh-all-database (166ms)
2025-9-26 11:25:37-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 11:25:37-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 11:28:23-debug: refresh db internal success
2025-9-26 11:28:23-debug: refresh db assets success
2025-9-26 11:28:23-debug: refresh db i18n success
2025-9-26 11:28:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:28:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:28:23-debug: asset-db:refresh-all-database (123ms)
2025-9-26 11:28:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:29:04-debug: refresh db internal success
2025-9-26 11:29:04-debug: refresh db assets success
2025-9-26 11:29:04-debug: refresh db i18n success
2025-9-26 11:29:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:29:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:29:04-debug: asset-db:refresh-all-database (154ms)
2025-9-26 11:29:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:29:04-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 11:29:08-debug: refresh db internal success
2025-9-26 11:29:08-debug: refresh db assets success
2025-9-26 11:29:08-debug: refresh db i18n success
2025-9-26 11:29:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:29:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:29:08-debug: asset-db:refresh-all-database (146ms)
2025-9-26 11:29:08-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 11:29:08-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 11:29:31-debug: refresh db internal success
2025-9-26 11:29:31-debug: refresh db assets success
2025-9-26 11:29:31-debug: refresh db i18n success
2025-9-26 11:29:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:29:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:29:31-debug: asset-db:refresh-all-database (121ms)
2025-9-26 11:29:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:30:05-debug: refresh db internal success
2025-9-26 11:30:05-debug: refresh db assets success
2025-9-26 11:30:05-debug: refresh db i18n success
2025-9-26 11:30:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:30:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:30:05-debug: asset-db:refresh-all-database (145ms)
2025-9-26 11:30:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:30:27-debug: refresh db internal success
2025-9-26 11:30:27-debug: refresh db assets success
2025-9-26 11:30:27-debug: refresh db i18n success
2025-9-26 11:30:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:30:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:30:27-debug: asset-db:refresh-all-database (155ms)
2025-9-26 11:30:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:30:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:30:52-debug: refresh db internal success
2025-9-26 11:30:52-debug: refresh db assets success
2025-9-26 11:30:52-debug: refresh db i18n success
2025-9-26 11:30:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:30:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:30:52-debug: asset-db:refresh-all-database (152ms)
2025-9-26 11:30:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:33:57-debug: refresh db internal success
2025-9-26 11:33:57-debug: refresh db assets success
2025-9-26 11:33:57-debug: refresh db i18n success
2025-9-26 11:33:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:33:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:33:57-debug: asset-db:refresh-all-database (147ms)
2025-9-26 11:34:01-debug: refresh db internal success
2025-9-26 11:34:01-debug: refresh db assets success
2025-9-26 11:34:01-debug: refresh db i18n success
2025-9-26 11:34:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:34:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:34:01-debug: asset-db:refresh-all-database (140ms)
2025-9-26 11:34:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:34:01-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 11:34:25-debug: refresh db internal success
2025-9-26 11:34:25-debug: refresh db assets success
2025-9-26 11:34:25-debug: refresh db i18n success
2025-9-26 11:34:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:34:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:34:25-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 11:34:25-debug: asset-db:refresh-all-database (150ms)
2025-9-26 11:34:25-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 11:45:55-debug: refresh db internal success
2025-9-26 11:45:55-debug: refresh db assets success
2025-9-26 11:45:55-debug: refresh db i18n success
2025-9-26 11:45:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:45:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:45:55-debug: asset-db:refresh-all-database (155ms)
2025-9-26 11:45:55-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 11:45:55-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 11:45:59-debug: refresh db internal success
2025-9-26 11:45:59-debug: refresh db assets success
2025-9-26 11:45:59-debug: refresh db i18n success
2025-9-26 11:45:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:45:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:45:59-debug: asset-db:refresh-all-database (144ms)
2025-9-26 11:45:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:45:59-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 11:46:02-debug: refresh db internal success
2025-9-26 11:46:02-debug: refresh db assets success
2025-9-26 11:46:02-debug: refresh db i18n success
2025-9-26 11:46:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:46:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:46:02-debug: asset-db:refresh-all-database (128ms)
2025-9-26 11:46:02-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 11:46:02-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 11:46:38-debug: refresh db internal success
2025-9-26 11:46:38-debug: refresh db assets success
2025-9-26 11:46:38-debug: refresh db i18n success
2025-9-26 11:46:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:46:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:46:38-debug: asset-db:refresh-all-database (122ms)
2025-9-26 11:46:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:46:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:47:50-debug: refresh db internal success
2025-9-26 11:47:50-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:47:50-debug: refresh db assets success
2025-9-26 11:47:50-debug: refresh db i18n success
2025-9-26 11:47:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:47:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:47:50-debug: asset-db:refresh-all-database (165ms)
2025-9-26 11:49:15-debug: refresh db internal success
2025-9-26 11:49:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:49:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:49:15-debug: refresh db assets success
2025-9-26 11:49:15-debug: refresh db i18n success
2025-9-26 11:49:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:49:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:49:15-debug: asset-db:refresh-all-database (149ms)
2025-9-26 11:49:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:49:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:49:15-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json...
2025-9-26 11:49:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:49:15-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave\formation success
2025-9-26 11:49:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:51:41-debug: refresh db internal success
2025-9-26 11:51:41-debug: refresh db assets success
2025-9-26 11:51:41-debug: refresh db i18n success
2025-9-26 11:51:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:51:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:51:41-debug: asset-db:refresh-all-database (149ms)
2025-9-26 11:51:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:51:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:51:49-debug: %cImport%c: E:\M2Game\Client\assets\scenes\FormationEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:51:49-debug: asset-db:reimport-assetaa842f3a-8aea-42c2-a480-968aade3dfef (3ms)
2025-9-26 11:51:49-debug: refresh db internal success
2025-9-26 11:51:50-debug: refresh db assets success
2025-9-26 11:51:50-debug: refresh db i18n success
2025-9-26 11:51:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:51:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:51:50-debug: asset-db:refresh-all-database (184ms)
2025-9-26 11:51:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:51:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:52:05-debug: refresh db internal success
2025-9-26 11:52:05-debug: refresh db assets success
2025-9-26 11:52:05-debug: refresh db i18n success
2025-9-26 11:52:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:52:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:52:05-debug: asset-db:refresh-all-database (130ms)
2025-9-26 11:52:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:52:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:52:12-debug: refresh db internal success
2025-9-26 11:52:12-debug: refresh db assets success
2025-9-26 11:52:12-debug: refresh db i18n success
2025-9-26 11:52:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:52:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:52:12-debug: asset-db:refresh-all-database (120ms)
2025-9-26 11:53:55-debug: refresh db internal success
2025-9-26 11:53:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:53:55-debug: refresh db assets success
2025-9-26 11:53:55-debug: refresh db i18n success
2025-9-26 11:53:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:53:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:53:55-debug: asset-db:refresh-all-database (160ms)
2025-9-26 11:53:59-debug: refresh db internal success
2025-9-26 11:53:59-debug: refresh db assets success
2025-9-26 11:53:59-debug: refresh db i18n success
2025-9-26 11:53:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:53:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:53:59-debug: asset-db:refresh-all-database (159ms)
2025-9-26 11:53:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:53:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:54:02-debug: refresh db internal success
2025-9-26 11:54:02-debug: refresh db assets success
2025-9-26 11:54:02-debug: refresh db i18n success
2025-9-26 11:54:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:54:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:54:02-debug: asset-db:refresh-all-database (116ms)
2025-9-26 11:54:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:54:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:56:21-debug: refresh db internal success
2025-9-26 11:56:21-debug: refresh db assets success
2025-9-26 11:56:21-debug: refresh db i18n success
2025-9-26 11:56:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:56:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:56:21-debug: asset-db:refresh-all-database (151ms)
2025-9-26 11:56:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:56:45-debug: refresh db internal success
2025-9-26 11:56:45-debug: refresh db assets success
2025-9-26 11:56:45-debug: refresh db i18n success
2025-9-26 11:56:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:56:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:56:45-debug: asset-db:refresh-all-database (156ms)
2025-9-26 11:56:48-debug: refresh db internal success
2025-9-26 11:56:48-debug: refresh db assets success
2025-9-26 11:56:48-debug: refresh db i18n success
2025-9-26 11:56:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:56:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:56:48-debug: asset-db:refresh-all-database (159ms)
2025-9-26 11:57:12-debug: refresh db internal success
2025-9-26 11:57:12-debug: refresh db assets success
2025-9-26 11:57:12-debug: refresh db i18n success
2025-9-26 11:57:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:57:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:57:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:57:12-debug: asset-db:refresh-all-database (123ms)
2025-9-26 11:57:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:57:12-debug: refresh db internal success
2025-9-26 11:57:12-debug: refresh db assets success
2025-9-26 11:57:12-debug: refresh db i18n success
2025-9-26 11:57:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:57:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:57:12-debug: asset-db:refresh-all-database (116ms)
2025-9-26 11:57:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:57:14-debug: refresh db internal success
2025-9-26 11:57:14-debug: refresh db assets success
2025-9-26 11:57:14-debug: refresh db i18n success
2025-9-26 11:57:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:57:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:57:14-debug: asset-db:refresh-all-database (118ms)
2025-9-26 11:57:16-debug: refresh db internal success
2025-9-26 11:57:16-debug: refresh db assets success
2025-9-26 11:57:16-debug: refresh db i18n success
2025-9-26 11:57:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:57:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:57:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:57:16-debug: asset-db:refresh-all-database (117ms)
2025-9-26 11:57:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:57:57-debug: refresh db internal success
2025-9-26 11:57:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:57:58-debug: refresh db assets success
2025-9-26 11:57:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:57:58-debug: refresh db i18n success
2025-9-26 11:57:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:57:58-debug: asset-db:refresh-all-database (154ms)
2025-9-26 11:57:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:58:03-debug: refresh db internal success
2025-9-26 11:58:03-debug: refresh db assets success
2025-9-26 11:58:03-debug: refresh db i18n success
2025-9-26 11:58:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:58:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:58:03-debug: asset-db:refresh-all-database (116ms)
2025-9-26 11:58:04-debug: refresh db internal success
2025-9-26 11:58:04-debug: refresh db assets success
2025-9-26 11:58:04-debug: refresh db i18n success
2025-9-26 11:58:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:58:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:58:04-debug: asset-db:refresh-all-database (116ms)
2025-9-26 11:58:06-debug: refresh db internal success
2025-9-26 11:58:06-debug: refresh db assets success
2025-9-26 11:58:06-debug: refresh db i18n success
2025-9-26 11:58:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:58:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:58:06-debug: asset-db:refresh-all-database (112ms)
2025-9-26 11:58:07-debug: refresh db internal success
2025-9-26 11:58:07-debug: refresh db assets success
2025-9-26 11:58:07-debug: refresh db i18n success
2025-9-26 11:58:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:58:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:58:07-debug: asset-db:refresh-all-database (117ms)
2025-9-26 11:58:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:58:12-debug: %cImport%c: E:\M2Game\Client\assets\scenes\FormationEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:58:12-debug: asset-db:reimport-assetaa842f3a-8aea-42c2-a480-968aade3dfef (2ms)
2025-9-26 11:58:21-debug: refresh db internal success
2025-9-26 11:58:21-debug: refresh db assets success
2025-9-26 11:58:21-debug: refresh db i18n success
2025-9-26 11:58:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:58:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:58:21-debug: asset-db:refresh-all-database (119ms)
2025-9-26 11:58:23-debug: refresh db internal success
2025-9-26 11:58:23-debug: refresh db assets success
2025-9-26 11:58:23-debug: refresh db i18n success
2025-9-26 11:58:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:58:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:58:23-debug: asset-db:refresh-all-database (117ms)
2025-9-26 11:58:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:58:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:58:25-debug: refresh db internal success
2025-9-26 11:58:25-debug: refresh db assets success
2025-9-26 11:58:25-debug: refresh db i18n success
2025-9-26 11:58:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:58:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:58:25-debug: asset-db:refresh-all-database (119ms)
2025-9-26 11:58:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:58:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:58:26-debug: refresh db internal success
2025-9-26 11:58:26-debug: refresh db assets success
2025-9-26 11:58:26-debug: refresh db i18n success
2025-9-26 11:58:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:58:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:58:26-debug: asset-db:refresh-all-database (114ms)
2025-9-26 11:58:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:58:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:59:45-debug: refresh db internal success
2025-9-26 11:59:45-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 11:59:45-debug: refresh db assets success
2025-9-26 11:59:45-debug: refresh db i18n success
2025-9-26 11:59:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:59:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:59:45-debug: asset-db:refresh-all-database (162ms)
2025-9-26 11:59:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:59:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:59:50-debug: refresh db internal success
2025-9-26 11:59:50-debug: refresh db assets success
2025-9-26 11:59:50-debug: refresh db i18n success
2025-9-26 11:59:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:59:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:59:50-debug: asset-db:refresh-all-database (122ms)
2025-9-26 11:59:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 11:59:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:59:51-debug: refresh db internal success
2025-9-26 11:59:51-debug: refresh db assets success
2025-9-26 11:59:51-debug: refresh db i18n success
2025-9-26 11:59:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 11:59:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 11:59:51-debug: asset-db:refresh-all-database (124ms)
2025-9-26 11:59:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 11:59:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:00:00-debug: refresh db internal success
2025-9-26 12:00:00-debug: refresh db assets success
2025-9-26 12:00:00-debug: refresh db i18n success
2025-9-26 12:00:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:00:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:00:00-debug: asset-db:refresh-all-database (113ms)
2025-9-26 12:00:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:00:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:00:16-debug: refresh db internal success
2025-9-26 12:00:16-debug: refresh db assets success
2025-9-26 12:00:16-debug: refresh db i18n success
2025-9-26 12:00:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:00:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:00:16-debug: asset-db:refresh-all-database (150ms)
2025-9-26 12:00:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:00:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:00:44-debug: %cImport%c: E:\M2Game\Client\assets\scenes\FormationEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:00:44-debug: asset-db:reimport-assetaa842f3a-8aea-42c2-a480-968aade3dfef (2ms)
2025-9-26 12:02:07-debug: refresh db internal success
2025-9-26 12:02:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:02:08-debug: refresh db assets success
2025-9-26 12:02:08-debug: refresh db i18n success
2025-9-26 12:02:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:02:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:02:08-debug: asset-db:refresh-all-database (166ms)
2025-9-26 12:02:10-debug: refresh db internal success
2025-9-26 12:02:10-debug: refresh db assets success
2025-9-26 12:02:10-debug: refresh db i18n success
2025-9-26 12:02:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:02:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:02:10-debug: asset-db:refresh-all-database (147ms)
2025-9-26 12:02:12-debug: refresh db internal success
2025-9-26 12:02:12-debug: refresh db assets success
2025-9-26 12:02:12-debug: refresh db i18n success
2025-9-26 12:02:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:02:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:02:12-debug: asset-db:refresh-all-database (141ms)
2025-9-26 12:02:32-debug: refresh db internal success
2025-9-26 12:02:32-debug: refresh db assets success
2025-9-26 12:02:32-debug: refresh db i18n success
2025-9-26 12:02:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:02:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:02:32-debug: asset-db:refresh-all-database (122ms)
2025-9-26 12:03:37-debug: refresh db internal success
2025-9-26 12:03:37-debug: refresh db assets success
2025-9-26 12:03:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:03:37-debug: refresh db i18n success
2025-9-26 12:03:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:03:37-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 12:03:37-debug: asset-db:refresh-all-database (126ms)
2025-9-26 12:03:37-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 12:03:43-debug: refresh db internal success
2025-9-26 12:03:43-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:03:43-debug: refresh db assets success
2025-9-26 12:03:43-debug: refresh db i18n success
2025-9-26 12:03:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:03:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:03:43-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 12:03:43-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 12:03:43-debug: asset-db:refresh-all-database (132ms)
2025-9-26 12:04:31-debug: refresh db internal success
2025-9-26 12:04:31-debug: refresh db assets success
2025-9-26 12:04:31-debug: refresh db i18n success
2025-9-26 12:04:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:04:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:04:31-debug: asset-db:refresh-all-database (123ms)
2025-9-26 12:04:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:04:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:04:36-debug: refresh db internal success
2025-9-26 12:04:36-debug: refresh db assets success
2025-9-26 12:04:36-debug: refresh db i18n success
2025-9-26 12:04:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:04:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:04:36-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 12:04:36-debug: asset-db:refresh-all-database (115ms)
2025-9-26 12:04:36-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 12:04:43-debug: refresh db internal success
2025-9-26 12:04:43-debug: refresh db assets success
2025-9-26 12:04:43-debug: refresh db i18n success
2025-9-26 12:04:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:04:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:04:43-debug: asset-db:refresh-all-database (122ms)
2025-9-26 12:04:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:04:43-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 12:05:15-debug: refresh db internal success
2025-9-26 12:05:15-debug: refresh db assets success
2025-9-26 12:05:15-debug: refresh db i18n success
2025-9-26 12:05:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:05:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:05:15-debug: asset-db:refresh-all-database (149ms)
2025-9-26 12:05:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:05:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:06:11-debug: refresh db internal success
2025-9-26 12:06:11-debug: refresh db assets success
2025-9-26 12:06:11-debug: refresh db i18n success
2025-9-26 12:06:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:06:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:06:11-debug: asset-db:refresh-all-database (152ms)
2025-9-26 12:06:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:06:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:06:46-debug: refresh db internal success
2025-9-26 12:06:46-debug: refresh db assets success
2025-9-26 12:06:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:06:46-debug: refresh db i18n success
2025-9-26 12:06:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:06:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:06:46-debug: asset-db:refresh-all-database (157ms)
2025-9-26 12:06:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:07:31-debug: refresh db internal success
2025-9-26 12:07:31-debug: refresh db assets success
2025-9-26 12:07:31-debug: refresh db i18n success
2025-9-26 12:07:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:07:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:07:31-debug: asset-db:refresh-all-database (120ms)
2025-9-26 12:07:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:07:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:08:18-debug: refresh db internal success
2025-9-26 12:08:18-debug: refresh db assets success
2025-9-26 12:08:18-debug: refresh db i18n success
2025-9-26 12:08:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:08:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:08:18-debug: asset-db:refresh-all-database (147ms)
2025-9-26 12:13:21-debug: refresh db internal success
2025-9-26 12:13:22-debug: refresh db assets success
2025-9-26 12:13:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:13:22-debug: refresh db i18n success
2025-9-26 12:13:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:13:22-debug: asset-db:refresh-all-database (154ms)
2025-9-26 12:13:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:13:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:14:30-debug: refresh db internal success
2025-9-26 12:14:30-debug: refresh db assets success
2025-9-26 12:14:30-debug: refresh db i18n success
2025-9-26 12:14:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:14:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:14:30-debug: asset-db:refresh-all-database (144ms)
2025-9-26 12:14:33-debug: refresh db internal success
2025-9-26 12:14:33-debug: refresh db assets success
2025-9-26 12:14:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:14:33-debug: refresh db i18n success
2025-9-26 12:14:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:14:33-debug: asset-db:refresh-all-database (136ms)
2025-9-26 12:14:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:14:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:14:53-debug: refresh db internal success
2025-9-26 12:14:53-debug: refresh db assets success
2025-9-26 12:14:53-debug: refresh db i18n success
2025-9-26 12:14:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:14:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:14:53-debug: asset-db:refresh-all-database (126ms)
2025-9-26 12:14:55-debug: refresh db internal success
2025-9-26 12:14:55-debug: refresh db assets success
2025-9-26 12:14:55-debug: refresh db i18n success
2025-9-26 12:14:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:14:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:14:55-debug: asset-db:refresh-all-database (115ms)
2025-9-26 12:15:00-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:15:00-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-26 12:17:08-debug: refresh db internal success
2025-9-26 12:17:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:17:08-debug: refresh db assets success
2025-9-26 12:17:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:17:08-debug: refresh db i18n success
2025-9-26 12:17:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:17:08-debug: asset-db:refresh-all-database (161ms)
2025-9-26 12:17:16-debug: refresh db internal success
2025-9-26 12:17:16-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:17:16-debug: refresh db assets success
2025-9-26 12:17:16-debug: refresh db i18n success
2025-9-26 12:17:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:17:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:17:16-debug: asset-db:refresh-all-database (153ms)
2025-9-26 12:17:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:17:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:17:45-debug: refresh db internal success
2025-9-26 12:17:45-debug: refresh db assets success
2025-9-26 12:17:45-debug: refresh db i18n success
2025-9-26 12:17:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:17:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:17:45-debug: asset-db:refresh-all-database (117ms)
2025-9-26 12:17:50-debug: refresh db internal success
2025-9-26 12:17:51-debug: refresh db assets success
2025-9-26 12:17:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:17:51-debug: refresh db i18n success
2025-9-26 12:17:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:17:51-debug: asset-db:refresh-all-database (129ms)
2025-9-26 12:17:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:17:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:17:59-debug: refresh db internal success
2025-9-26 12:17:59-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:17:59-debug: refresh db assets success
2025-9-26 12:17:59-debug: refresh db i18n success
2025-9-26 12:17:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:17:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:17:59-debug: asset-db:refresh-all-database (157ms)
2025-9-26 12:18:03-debug: refresh db internal success
2025-9-26 12:18:03-debug: refresh db assets success
2025-9-26 12:18:03-debug: refresh db i18n success
2025-9-26 12:18:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:18:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:18:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:18:03-debug: asset-db:refresh-all-database (144ms)
2025-9-26 12:18:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:18:09-debug: refresh db internal success
2025-9-26 12:18:09-debug: refresh db assets success
2025-9-26 12:18:09-debug: refresh db i18n success
2025-9-26 12:18:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:18:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:18:09-debug: asset-db:refresh-all-database (116ms)
2025-9-26 12:18:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:18:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:20:26-debug: refresh db internal success
2025-9-26 12:20:26-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:20:26-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:20:26-debug: refresh db assets success
2025-9-26 12:20:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:20:26-debug: refresh db i18n success
2025-9-26 12:20:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:20:26-debug: asset-db:refresh-all-database (164ms)
2025-9-26 12:20:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:36:43-debug: refresh db internal success
2025-9-26 12:36:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:36:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:36:43-debug: refresh db assets success
2025-9-26 12:36:43-debug: refresh db i18n success
2025-9-26 12:36:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:36:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:36:43-debug: asset-db:refresh-all-database (193ms)
2025-9-26 12:36:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:36:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:37:01-debug: refresh db internal success
2025-9-26 12:37:01-debug: refresh db assets success
2025-9-26 12:37:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:37:01-debug: refresh db i18n success
2025-9-26 12:37:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:37:01-debug: asset-db:refresh-all-database (126ms)
2025-9-26 12:37:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:37:17-debug: refresh db internal success
2025-9-26 12:37:17-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:37:17-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-9-26 12:37:17-debug: refresh db assets success
2025-9-26 12:37:17-debug: refresh db i18n success
2025-9-26 12:37:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:37:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:37:17-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\formation\f_02.json...
2025-9-26 12:37:17-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_02.json
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:37:17-debug: asset-db:refresh-all-database (144ms)
2025-9-26 12:37:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:37:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:37:17-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave\formation success
2025-9-26 12:37:17-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:37:37-debug: refresh db internal success
2025-9-26 12:37:37-debug: refresh db assets success
2025-9-26 12:37:37-debug: refresh db i18n success
2025-9-26 12:37:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:37:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:37:37-debug: asset-db:refresh-all-database (135ms)
2025-9-26 12:38:10-debug: refresh db internal success
2025-9-26 12:38:10-debug: refresh db assets success
2025-9-26 12:38:10-debug: refresh db i18n success
2025-9-26 12:38:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:38:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:38:10-debug: asset-db:refresh-all-database (187ms)
2025-9-26 12:38:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:38:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:44:58-debug: refresh db internal success
2025-9-26 12:44:58-debug: refresh db assets success
2025-9-26 12:44:58-debug: refresh db i18n success
2025-9-26 12:44:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:44:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:44:58-debug: asset-db:refresh-all-database (114ms)
2025-9-26 12:45:04-debug: refresh db internal success
2025-9-26 12:45:04-debug: refresh db assets success
2025-9-26 12:45:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:45:04-debug: refresh db i18n success
2025-9-26 12:45:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:45:04-debug: asset-db:refresh-all-database (135ms)
2025-9-26 12:45:09-debug: refresh db internal success
2025-9-26 12:45:10-debug: refresh db assets success
2025-9-26 12:45:10-debug: refresh db i18n success
2025-9-26 12:45:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:45:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:45:10-debug: asset-db:refresh-all-database (116ms)
2025-9-26 12:45:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:45:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:45:23-debug: refresh db internal success
2025-9-26 12:45:23-debug: refresh db assets success
2025-9-26 12:45:23-debug: refresh db i18n success
2025-9-26 12:45:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:45:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:45:23-debug: asset-db:refresh-all-database (145ms)
2025-9-26 12:45:23-debug: asset-db:worker-effect-data-processing (20ms)
2025-9-26 12:45:23-debug: asset-db-hook-engine-extends-afterRefresh (20ms)
2025-9-26 12:45:25-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json...
2025-9-26 12:45:25-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:45:25-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave\formation success
2025-9-26 12:45:25-debug: refresh db internal success
2025-9-26 12:45:25-debug: refresh db assets success
2025-9-26 12:45:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:45:25-debug: refresh db i18n success
2025-9-26 12:45:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:45:25-debug: asset-db:refresh-all-database (117ms)
2025-9-26 12:45:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:45:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:45:58-debug: refresh db internal success
2025-9-26 12:45:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:45:58-debug: refresh db assets success
2025-9-26 12:45:58-debug: refresh db i18n success
2025-9-26 12:45:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:45:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:45:58-debug: asset-db:refresh-all-database (165ms)
2025-9-26 12:46:28-debug: refresh db internal success
2025-9-26 12:46:28-debug: refresh db assets success
2025-9-26 12:46:28-debug: refresh db i18n success
2025-9-26 12:46:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:46:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:46:28-debug: asset-db:refresh-all-database (123ms)
2025-9-26 12:46:28-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-26 12:46:28-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-26 12:46:30-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json...
2025-9-26 12:46:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:46:30-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave\formation success
2025-9-26 12:46:30-debug: refresh db internal success
2025-9-26 12:46:30-debug: refresh db assets success
2025-9-26 12:46:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-26 12:46:30-debug: refresh db i18n success
2025-9-26 12:46:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-26 12:46:30-debug: asset-db:refresh-all-database (118ms)
2025-9-26 12:46:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-26 12:46:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-26 12:46:42-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-26 12:46:42-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
