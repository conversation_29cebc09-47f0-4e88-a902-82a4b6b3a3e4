import { _decorator, Component, Vec2 } from 'cc';
import { EDITOR } from 'cc/env';
import { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';

const { ccclass, executeInEditMode, menu } = _decorator;

/**
 * 路径段优化测试 - 验证直线段的点数优化
 */
@ccclass('PathSegmentOptimizationTest')
@menu("怪物/编辑器/路径段优化测试")
@executeInEditMode(true)
export class PathSegmentOptimizationTest extends Component {

    protected onLoad() {
        if (EDITOR) {
            this.runOptimizationTests();
        }
    }

    private runOptimizationTests() {
        console.log("=== 路径段优化测试开始 ===");
        
        this.testLinearSegmentOptimization();
        this.testMixedSegmentOptimization();
        this.testCurveSegmentNormal();
        
        console.log("=== 路径段优化测试结束 ===");
    }

    /**
     * 测试纯直线段的优化
     */
    private testLinearSegmentOptimization() {
        console.log("\n测试1: 纯直线段优化");
        
        const pathData = new PathData();
        pathData.segments = 20; // 设置较大的段数
        
        // 创建3个点，全部设置为直线
        pathData.points = [
            new PathPoint(-100, 0),
            new PathPoint(0, 100),
            new PathPoint(100, 0),
        ];
        pathData.points.forEach(p => p.smoothness = 0);
        
        const curvePoints = pathData.generateCurvePoints();
        
        console.log(`segments设置: ${pathData.segments}`);
        console.log(`生成的点数: ${curvePoints.length}`);
        console.log(`理论最少点数: ${pathData.points.length} (起点 + 各段终点)`);
        
        // 验证点数是否优化
        const expectedPoints = pathData.points.length;
        if (curvePoints.length === expectedPoints) {
            console.log("✅ 直线段优化成功：点数已最小化");
        } else {
            console.log(`❌ 直线段优化失败：期望${expectedPoints}个点，实际${curvePoints.length}个点`);
        }
        
        // 验证点的位置是否正确
        this.validateLinearPoints(curvePoints, pathData.points);
    }

    /**
     * 测试混合段的优化（部分直线，部分曲线）
     */
    private testMixedSegmentOptimization() {
        console.log("\n测试2: 混合段优化");
        
        const pathData = new PathData();
        pathData.segments = 20;
        
        // 创建4个点：直线-曲线-直线
        pathData.points = [
            new PathPoint(-150, 0),   // smoothness = 0 (直线)
            new PathPoint(-50, 100),  // smoothness = 0 (直线)
            new PathPoint(50, -100),  // smoothness = 1 (曲线)
            new PathPoint(150, 0),    // smoothness = 1 (曲线)
        ];
        pathData.points[0].smoothness = 0;  // 第一段：直线
        pathData.points[1].smoothness = 0;  // 第一段：直线
        pathData.points[2].smoothness = 1;  // 第二段：曲线
        pathData.points[3].smoothness = 1;  // 第二段：曲线
        
        const curvePoints = pathData.generateCurvePoints();
        
        console.log(`segments设置: ${pathData.segments}`);
        console.log(`生成的点数: ${curvePoints.length}`);
        
        // 分析点数分布
        // 第一段（直线）：应该只有2个点（起点+终点）
        // 第二段（曲线）：应该有segments+1个点
        const expectedLinearPoints = 2; // 起点 + 第一段终点
        const expectedCurvePoints = pathData.segments; // 第二段的segments个点
        const expectedTotal = expectedLinearPoints + expectedCurvePoints;
        
        console.log(`期望点数分布: 直线段2个点 + 曲线段${expectedCurvePoints}个点 = ${expectedTotal}个点`);
        
        if (curvePoints.length === expectedTotal) {
            console.log("✅ 混合段优化成功");
        } else {
            console.log(`❌ 混合段优化异常：期望${expectedTotal}个点，实际${curvePoints.length}个点`);
        }
    }

    /**
     * 测试纯曲线段（确保曲线段点数正常）
     */
    private testCurveSegmentNormal() {
        console.log("\n测试3: 纯曲线段正常生成");
        
        const pathData = new PathData();
        pathData.segments = 10;
        
        // 创建3个点，全部设置为曲线
        pathData.points = [
            new PathPoint(-100, 0),
            new PathPoint(0, 100),
            new PathPoint(100, 0),
        ];
        pathData.points.forEach(p => p.smoothness = 1);
        
        const curvePoints = pathData.generateCurvePoints();
        
        console.log(`segments设置: ${pathData.segments}`);
        console.log(`生成的点数: ${curvePoints.length}`);
        
        // 对于曲线，期望点数 = 起点 + 每段的segments个点
        const expectedPoints = 1 + (pathData.points.length - 1) * pathData.segments;
        console.log(`期望点数: 1(起点) + ${pathData.points.length - 1}段 × ${pathData.segments}点/段 = ${expectedPoints}个点`);
        
        if (curvePoints.length === expectedPoints) {
            console.log("✅ 曲线段点数正常");
        } else {
            console.log(`❌ 曲线段点数异常：期望${expectedPoints}个点，实际${curvePoints.length}个点`);
        }
    }

    /**
     * 验证直线点的位置是否正确
     */
    private validateLinearPoints(curvePoints: Vec2[], originalPoints: PathPoint[]) {
        console.log("验证直线点位置:");
        
        for (let i = 0; i < originalPoints.length; i++) {
            const originalPos = originalPoints[i].position;
            const curvePos = curvePoints[i];
            
            const distance = Vec2.distance(originalPos, curvePos);
            
            if (distance < 0.001) {
                console.log(`  点${i}: ✅ 位置正确 (${curvePos.x.toFixed(1)}, ${curvePos.y.toFixed(1)})`);
            } else {
                console.log(`  点${i}: ❌ 位置偏差 ${distance.toFixed(3)} - 期望(${originalPos.x}, ${originalPos.y}), 实际(${curvePos.x.toFixed(1)}, ${curvePos.y.toFixed(1)})`);
            }
        }
    }

    /**
     * 性能对比测试
     */
    public performanceTest() {
        console.log("\n=== 性能对比测试 ===");
        
        // 创建一个包含多个直线段的路径
        const pathData = new PathData();
        pathData.segments = 50; // 大段数
        
        // 创建10个点的直线路径
        pathData.points = [];
        for (let i = 0; i < 10; i++) {
            const point = new PathPoint(i * 100, Math.sin(i) * 50);
            point.smoothness = 0; // 全部设为直线
            pathData.points.push(point);
        }
        
        const startTime = performance.now();
        const curvePoints = pathData.generateCurvePoints();
        const endTime = performance.now();
        
        console.log(`路径点数: ${pathData.points.length}`);
        console.log(`segments设置: ${pathData.segments}`);
        console.log(`生成的曲线点数: ${curvePoints.length}`);
        console.log(`生成时间: ${(endTime - startTime).toFixed(2)}ms`);
        
        // 计算优化效果
        const unoptimizedPoints = 1 + (pathData.points.length - 1) * pathData.segments;
        const optimizedPoints = curvePoints.length;
        const reduction = ((unoptimizedPoints - optimizedPoints) / unoptimizedPoints * 100).toFixed(1);
        
        console.log(`未优化点数: ${unoptimizedPoints}`);
        console.log(`优化后点数: ${optimizedPoints}`);
        console.log(`点数减少: ${reduction}%`);
    }
}
