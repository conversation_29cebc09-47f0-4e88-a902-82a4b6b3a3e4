{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/PathData.ts"], "names": ["_decorator", "Vec2", "CCFloat", "CCInteger", "Enum", "eOrientationType", "ccclass", "property", "PathPoint", "type", "displayName", "range", "slide", "tooltip", "constructor", "x", "y", "position", "value", "PathData", "editor<PERSON><PERSON><PERSON>", "catmullRomPoint", "t", "p0", "p1", "p2", "p3", "smoothness", "t2", "t3", "tension", "result", "generateCurvePoints", "points", "length", "map", "p", "curvePoints", "pointCount", "i", "getControlPoint", "j", "segments", "point", "push", "index", "closed", "wrappedIndex", "subtract", "add"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACtCC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;AAE9B;AACA;AACA;;2BAEaQ,S,WADZF,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE,MAA9B;AAAsCC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAA7C;AAAqDC,QAAAA,KAAK,EAAE,IAA5D;AAAkEC,QAAAA,OAAO,EAAE;AAA3E,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,IAAhC;AAAsCG,QAAAA,OAAO,EAAE;AAA/C,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEL,IAAI;AAAA;AAAA,iDAAZ;AAAgCM,QAAAA,WAAW,EAAE,MAA7C;AAAqDG,QAAAA,OAAO,EAAE;AAA9D,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,MAAhC;AAAwCG,QAAAA,OAAO,EAAE;AAAjD,OAAD,C,2BAjBb,MACaL,SADb,CACuB;AAmBnBM,QAAAA,WAAW,CAACC,CAAS,GAAG,CAAb,EAAgBC,CAAS,GAAG,CAA5B,EAA+B;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AACtC,eAAKD,CAAL,GAASA,CAAT;AACA,eAAKC,CAAL,GAASA,CAAT;AACH;;AAEkB,YAARC,QAAQ,GAAS;AACxB,iBAAO,IAAIhB,IAAJ,CAAS,KAAKc,CAAd,EAAiB,KAAKC,CAAtB,CAAP;AACH;;AAEkB,YAARC,QAAQ,CAACC,KAAD,EAAc;AAC7B,eAAKH,CAAL,GAASG,KAAK,CAACH,CAAf;AACA,eAAKC,CAAL,GAASE,KAAK,CAACF,CAAf;AACH;;AA/BkB,O;;;;;iBAEA,C;;;;;;;iBAGA,C;;;;;;;iBAGS,G;;;;;;;iBAGL,G;;;;;;;iBAGoB,C;;;;;;;iBAGT,C;;;AAiBtC;AACA;AACA;;;0BAEaG,Q,YADZb,OAAO,CAAC,UAAD,C,UAEHC,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,MAAf;AAAuBU,QAAAA,UAAU,EAAE;AAAnC,OAAD,C,WAGRb,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAE,CAACD,SAAD,CAAR;AAAqBE,QAAAA,WAAW,EAAE;AAAlC,OAAD,C,WAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,OAAhC;AAAyCG,QAAAA,OAAO,EAAE;AAAlD,OAAD,C,WAGRN,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,QAAf;AAAyBG,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,6BAXb,MACaM,QADb,CACsB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAalB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACiC,eAAfE,eAAe,CAACC,CAAD,EAAYC,EAAZ,EAAsBC,EAAtB,EAAgCC,EAAhC,EAA0CC,EAA1C,EAAoDC,UAAkB,GAAG,GAAzE,EAAoF;AAC7G,gBAAMC,EAAE,GAAGN,CAAC,GAAGA,CAAf;AACA,gBAAMO,EAAE,GAAGD,EAAE,GAAGN,CAAhB,CAF6G,CAI7G;;AACA,gBAAMQ,OAAO,GAAG,CAAC,IAAIH,UAAL,IAAmB,GAAnC;AAEA,gBAAMI,MAAM,GAAG,IAAI9B,IAAJ,EAAf;AACA8B,UAAAA,MAAM,CAAChB,CAAP,GAAWe,OAAO,IAAI,CAAC,CAACD,EAAD,GAAM,IAAID,EAAV,GAAeN,CAAhB,IAAqBC,EAAE,CAACR,CAAxB,GAA4B,CAAC,IAAIc,EAAJ,GAAS,IAAID,EAAb,GAAkB,CAAnB,IAAwBJ,EAAE,CAACT,CAAvD,GAA2D,CAAC,CAAC,CAAD,GAAKc,EAAL,GAAU,IAAID,EAAd,GAAmBN,CAApB,IAAyBG,EAAE,CAACV,CAAvF,GAA2F,CAACc,EAAE,GAAGD,EAAN,IAAYF,EAAE,CAACX,CAA9G,CAAlB;AACAgB,UAAAA,MAAM,CAACf,CAAP,GAAWc,OAAO,IAAI,CAAC,CAACD,EAAD,GAAM,IAAID,EAAV,GAAeN,CAAhB,IAAqBC,EAAE,CAACP,CAAxB,GAA4B,CAAC,IAAIa,EAAJ,GAAS,IAAID,EAAb,GAAkB,CAAnB,IAAwBJ,EAAE,CAACR,CAAvD,GAA2D,CAAC,CAAC,CAAD,GAAKa,EAAL,GAAU,IAAID,EAAd,GAAmBN,CAApB,IAAyBG,EAAE,CAACT,CAAvF,GAA2F,CAACa,EAAE,GAAGD,EAAN,IAAYF,EAAE,CAACV,CAA9G,CAAlB;AAEA,iBAAOe,MAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,mBAAmB,GAAW;AACjC,cAAI,KAAKC,MAAL,CAAYC,MAAZ,GAAqB,CAAzB,EAA4B;AACxB,mBAAO,KAAKD,MAAL,CAAYE,GAAZ,CAAgBC,CAAC,IAAIA,CAAC,CAACnB,QAAvB,CAAP;AACH;;AAED,gBAAMoB,WAAmB,GAAG,EAA5B;AACA,gBAAMC,UAAU,GAAG,KAAKL,MAAL,CAAYC,MAA/B;;AAEA,eAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,UAAU,GAAG,CAAjC,EAAoCC,CAAC,EAArC,EAAyC;AACrC,kBAAMhB,EAAE,GAAG,KAAKiB,eAAL,CAAqBD,CAAC,GAAG,CAAzB,CAAX;AACA,kBAAMf,EAAE,GAAG,KAAKS,MAAL,CAAYM,CAAZ,EAAetB,QAA1B;AACA,kBAAMQ,EAAE,GAAG,KAAKQ,MAAL,CAAYM,CAAC,GAAG,CAAhB,EAAmBtB,QAA9B;AACA,kBAAMS,EAAE,GAAG,KAAKc,eAAL,CAAqBD,CAAC,GAAG,CAAzB,CAAX,CAJqC,CAMrC;;AACA,kBAAMZ,UAAU,GAAG,CAAC,KAAKM,MAAL,CAAYM,CAAZ,EAAeZ,UAAf,GAA4B,KAAKM,MAAL,CAAYM,CAAC,GAAG,CAAhB,EAAmBZ,UAAhD,IAA8D,GAAjF;;AAEA,iBAAK,IAAIc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmCD,CAAC,EAApC,EAAwC;AACpC,oBAAMnB,CAAC,GAAGmB,CAAC,GAAG,KAAKC,QAAnB;AACA,oBAAMC,KAAK,GAAGxB,QAAQ,CAACE,eAAT,CAAyBC,CAAzB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CC,UAA5C,CAAd;AACAU,cAAAA,WAAW,CAACO,IAAZ,CAAiBD,KAAjB;AACH;AACJ,WAtBgC,CAwBjC;;;AACAN,UAAAA,WAAW,CAACO,IAAZ,CAAiB,KAAKX,MAAL,CAAYK,UAAU,GAAG,CAAzB,EAA4BrB,QAA7C;AAEA,iBAAOoB,WAAP;AACH;AAED;AACJ;AACA;;;AACYG,QAAAA,eAAe,CAACK,KAAD,EAAsB;AACzC,gBAAMP,UAAU,GAAG,KAAKL,MAAL,CAAYC,MAA/B;;AAEA,cAAI,KAAKY,MAAT,EAAiB;AACb;AACA,kBAAMC,YAAY,GAAG,CAAEF,KAAK,GAAGP,UAAT,GAAuBA,UAAxB,IAAsCA,UAA3D;AACA,mBAAO,KAAKL,MAAL,CAAYc,YAAZ,EAA0B9B,QAAjC;AACH,WAJD,MAIO;AACH;AACA,gBAAI4B,KAAK,GAAG,CAAZ,EAAe;AACX;AACA,oBAAMtB,EAAE,GAAG,KAAKU,MAAL,CAAY,CAAZ,EAAehB,QAA1B;AACA,oBAAMO,EAAE,GAAG,KAAKS,MAAL,CAAY,CAAZ,EAAehB,QAA1B;AACA,qBAAOhB,IAAI,CAAC+C,QAAL,CAAc,IAAI/C,IAAJ,EAAd,EAA0BsB,EAA1B,EAA8BtB,IAAI,CAAC+C,QAAL,CAAc,IAAI/C,IAAJ,EAAd,EAA0BuB,EAA1B,EAA8BD,EAA9B,CAA9B,CAAP;AACH,aALD,MAKO,IAAIsB,KAAK,IAAIP,UAAb,EAAyB;AAC5B;AACA,oBAAMf,EAAE,GAAG,KAAKU,MAAL,CAAYK,UAAU,GAAG,CAAzB,EAA4BrB,QAAvC;AACA,oBAAMO,EAAE,GAAG,KAAKS,MAAL,CAAYK,UAAU,GAAG,CAAzB,EAA4BrB,QAAvC;AACA,qBAAOhB,IAAI,CAACgD,GAAL,CAAS,IAAIhD,IAAJ,EAAT,EAAqBuB,EAArB,EAAyBvB,IAAI,CAAC+C,QAAL,CAAc,IAAI/C,IAAJ,EAAd,EAA0BuB,EAA1B,EAA8BD,EAA9B,CAAzB,CAAP;AACH,aALM,MAKA;AACH,qBAAO,KAAKU,MAAL,CAAYY,KAAZ,EAAmB5B,QAA1B;AACH;AACJ;AACJ;;AA/FiB,O;;;;;iBAEI,E;;;;;;;iBAGO,E;;;;;;;iBAGH,E;;;;;;;iBAGD,K", "sourcesContent": ["import { _decorator, Vec2, CCFloat, CC<PERSON><PERSON>ger, Enum } from 'cc';\r\nimport { eOrientationType } from './WaveData';\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 路径点数据\r\n */\r\n@ccclass(\"PathPoint\")\r\nexport class PathPoint {\r\n    @property({ type: CCFloat, displayName: \"X坐标\" })\r\n    public x: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"Y坐标\" })\r\n    public y: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"平滑程度\", range: [0, 1], slide: true, tooltip: \"0=尖锐转角，1=最大平滑\" })\r\n    public smoothness: number = 0.5;\r\n\r\n    @property({ type: CCInteger, displayName: \"速度\", tooltip: \"飞机在此点的速度\" })\r\n    public speed: number = 500;\r\n\r\n    @property({ type: Enum(eOrientationType), displayName: \"朝向类型\", tooltip: \"飞机在此点的朝向\" })\r\n    public orientationType: eOrientationType = 0;\r\n\r\n    @property({ type: CCInteger, displayName: \"朝向参数\", tooltip: \"根据朝向类型不同而不同\" })\r\n    public orientationParam: number = 0;\r\n\r\n    constructor(x: number = 0, y: number = 0) {\r\n        this.x = x;\r\n        this.y = y;\r\n    }\r\n\r\n    public get position(): Vec2 {\r\n        return new Vec2(this.x, this.y);\r\n    }\r\n\r\n    public set position(value: Vec2) {\r\n        this.x = value.x;\r\n        this.y = value.y;\r\n    }\r\n}\r\n\r\n/**\r\n * 路径数据\r\n */\r\n@ccclass(\"PathData\")\r\nexport class PathData {\r\n    @property({ displayName: '路径名称', editorOnly: true })\r\n    public name: string = \"\";\r\n\r\n    @property({ type: [PathPoint], displayName: '路径点' })\r\n    public points: PathPoint[] = [];\r\n\r\n    @property({ type: CCInteger, displayName: \"曲线分段数\", tooltip: \"每段曲线的细分数量，影响曲线平滑度\" })\r\n    public segments: number = 20;\r\n\r\n    @property({ displayName: \"是否闭合路径\", tooltip: \"路径是否形成闭环\" })\r\n    public closed: boolean = false;\r\n\r\n    /**\r\n     * 获取Catmull-Rom曲线上的点\r\n     * @param t 参数值 [0, 1]\r\n     * @param p0 控制点0\r\n     * @param p1 控制点1\r\n     * @param p2 控制点2\r\n     * @param p3 控制点3\r\n     * @param smoothness 平滑程度\r\n     */\r\n    public static catmullRomPoint(t: number, p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number = 0.5): Vec2 {\r\n        const t2 = t * t;\r\n        const t3 = t2 * t;\r\n\r\n        // Catmull-Rom基础矩阵，通过smoothness调整张力\r\n        const tension = (1 - smoothness) * 0.5;\r\n\r\n        const result = new Vec2();\r\n        result.x = tension * ((-t3 + 2 * t2 - t) * p0.x + (3 * t3 - 5 * t2 + 2) * p1.x + (-3 * t3 + 4 * t2 + t) * p2.x + (t3 - t2) * p3.x);\r\n        result.y = tension * ((-t3 + 2 * t2 - t) * p0.y + (3 * t3 - 5 * t2 + 2) * p1.y + (-3 * t3 + 4 * t2 + t) * p2.y + (t3 - t2) * p3.y);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * 生成完整的曲线路径点\r\n     */\r\n    public generateCurvePoints(): Vec2[] {\r\n        if (this.points.length < 2) {\r\n            return this.points.map(p => p.position);\r\n        }\r\n\r\n        const curvePoints: Vec2[] = [];\r\n        const pointCount = this.points.length;\r\n\r\n        for (let i = 0; i < pointCount - 1; i++) {\r\n            const p0 = this.getControlPoint(i - 1);\r\n            const p1 = this.points[i].position;\r\n            const p2 = this.points[i + 1].position;\r\n            const p3 = this.getControlPoint(i + 2);\r\n\r\n            // 使用当前段的平滑程度（取两个端点的平均值）\r\n            const smoothness = (this.points[i].smoothness + this.points[i + 1].smoothness) * 0.5;\r\n\r\n            for (let j = 0; j < this.segments; j++) {\r\n                const t = j / this.segments;\r\n                const point = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);\r\n                curvePoints.push(point);\r\n            }\r\n        }\r\n\r\n        // 添加最后一个点\r\n        curvePoints.push(this.points[pointCount - 1].position);\r\n\r\n        return curvePoints;\r\n    }\r\n\r\n    /**\r\n     * 获取控制点（处理边界情况）\r\n     */\r\n    private getControlPoint(index: number): Vec2 {\r\n        const pointCount = this.points.length;\r\n\r\n        if (this.closed) {\r\n            // 闭合路径，使用循环索引\r\n            const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;\r\n            return this.points[wrappedIndex].position;\r\n        } else {\r\n            // 开放路径，边界处理\r\n            if (index < 0) {\r\n                // 延伸第一个点\r\n                const p0 = this.points[0].position;\r\n                const p1 = this.points[1].position;\r\n                return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else if (index >= pointCount) {\r\n                // 延伸最后一个点\r\n                const p0 = this.points[pointCount - 2].position;\r\n                const p1 = this.points[pointCount - 1].position;\r\n                return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else {\r\n                return this.points[index].position;\r\n            }\r\n        }\r\n    }\r\n}"]}