{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/PathData.ts"], "names": ["_decorator", "Vec2", "CCFloat", "CCInteger", "Enum", "eOrientationType", "ccclass", "property", "PathPoint", "type", "displayName", "range", "slide", "tooltip", "constructor", "x", "y", "position", "value", "PathData", "editor<PERSON><PERSON><PERSON>", "_cachedCurvePoints", "catmullRomPoint", "t", "p0", "p1", "p2", "p3", "smoothness", "lerp", "t2", "t3", "catmullRom", "linear", "generateCurvePoints", "regen", "generateCurvePointsInternal", "points", "length", "map", "p", "curvePoints", "pointCount", "push", "segmentCount", "closed", "i", "getControlPoint", "startSmoothness", "endSmoothness", "Math", "min", "j", "segments", "point", "firstPoint", "lastPoint", "distance", "pop", "index", "wrappedIndex", "subtract", "add", "toJSON", "name", "startIdx", "endIdx", "fromJSON", "data", "pathData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACtCC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;AAE9B;AACA;AACA;;2BAEaQ,S,WADZF,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE,MAA9B;AAAsCC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAA7C;AAAqDC,QAAAA,KAAK,EAAE,IAA5D;AAAkEC,QAAAA,OAAO,EAAE;AAA3E,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,IAAhC;AAAsCG,QAAAA,OAAO,EAAE;AAA/C,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEL,IAAI;AAAA;AAAA,iDAAZ;AAAgCM,QAAAA,WAAW,EAAE,MAA7C;AAAqDG,QAAAA,OAAO,EAAE;AAA9D,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,MAAhC;AAAwCG,QAAAA,OAAO,EAAE;AAAjD,OAAD,C,2BAjBb,MACaL,SADb,CACuB;AAmBnBM,QAAAA,WAAW,CAACC,CAAS,GAAG,CAAb,EAAgBC,CAAS,GAAG,CAA5B,EAA+B;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AACtC,eAAKD,CAAL,GAASA,CAAT;AACA,eAAKC,CAAL,GAASA,CAAT;AACH;;AAEkB,YAARC,QAAQ,GAAS;AACxB,iBAAO,IAAIhB,IAAJ,CAAS,KAAKc,CAAd,EAAiB,KAAKC,CAAtB,CAAP;AACH;;AAEkB,YAARC,QAAQ,CAACC,KAAD,EAAc;AAC7B,eAAKH,CAAL,GAASG,KAAK,CAACH,CAAf;AACA,eAAKC,CAAL,GAASE,KAAK,CAACF,CAAf;AACH;;AA/BkB,O;;;;;iBAEA,C;;;;;;;iBAGA,C;;;;;;;iBAGS,C;;;;;;;iBAGL,G;;;;;;;iBAGoB,C;;;;;;;iBAGT,C;;;AAiBtC;AACA;AACA;;;0BAEaG,Q,YADZb,OAAO,CAAC,UAAD,C,UAEHC,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,MAAf;AAAuBU,QAAAA,UAAU,EAAE;AAAnC,OAAD,C,WAGRb,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,WAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,WAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAE,CAACD,SAAD,CAAR;AAAqBE,QAAAA,WAAW,EAAE;AAAlC,OAAD,C,WAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,OAAhC;AAAyCG,QAAAA,OAAO,EAAE;AAAlD,OAAD,C,WAGRN,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,QAAf;AAAyBG,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,6BAjBb,MACaM,QADb,CACsB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAmBlB;AAnBkB,eAoBVE,kBApBU,GAoB0B,IApB1B;AAAA;;AAsBlB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACiC,eAAfC,eAAe,CAACC,CAAD,EAAYC,EAAZ,EAAsBC,EAAtB,EAAgCC,EAAhC,EAA0CC,EAA1C,EAAoDC,UAAkB,GAAG,GAAzE,EAAoF;AAC7G;AACA,cAAIA,UAAU,KAAK,CAAnB,EAAsB;AAClB,mBAAO3B,IAAI,CAAC4B,IAAL,CAAU,IAAI5B,IAAJ,EAAV,EAAsBwB,EAAtB,EAA0BC,EAA1B,EAA8BH,CAA9B,CAAP;AACH;;AAED,gBAAMO,EAAE,GAAGP,CAAC,GAAGA,CAAf;AACA,gBAAMQ,EAAE,GAAGD,EAAE,GAAGP,CAAhB,CAP6G,CAS7G;;AACA,gBAAMS,UAAU,GAAG,IAAI/B,IAAJ,EAAnB;AACA+B,UAAAA,UAAU,CAACjB,CAAX,GAAe,OACV,IAAIU,EAAE,CAACV,CAAR,GACA,CAAC,CAACS,EAAE,CAACT,CAAJ,GAAQW,EAAE,CAACX,CAAZ,IAAiBQ,CADjB,GAEA,CAAC,IAAIC,EAAE,CAACT,CAAP,GAAW,IAAIU,EAAE,CAACV,CAAlB,GAAsB,IAAIW,EAAE,CAACX,CAA7B,GAAiCY,EAAE,CAACZ,CAArC,IAA0Ce,EAF1C,GAGA,CAAC,CAACN,EAAE,CAACT,CAAJ,GAAQ,IAAIU,EAAE,CAACV,CAAf,GAAmB,IAAIW,EAAE,CAACX,CAA1B,GAA8BY,EAAE,CAACZ,CAAlC,IAAuCgB,EAJ5B,CAAf;AAOAC,UAAAA,UAAU,CAAChB,CAAX,GAAe,OACV,IAAIS,EAAE,CAACT,CAAR,GACA,CAAC,CAACQ,EAAE,CAACR,CAAJ,GAAQU,EAAE,CAACV,CAAZ,IAAiBO,CADjB,GAEA,CAAC,IAAIC,EAAE,CAACR,CAAP,GAAW,IAAIS,EAAE,CAACT,CAAlB,GAAsB,IAAIU,EAAE,CAACV,CAA7B,GAAiCW,EAAE,CAACX,CAArC,IAA0Cc,EAF1C,GAGA,CAAC,CAACN,EAAE,CAACR,CAAJ,GAAQ,IAAIS,EAAE,CAACT,CAAf,GAAmB,IAAIU,EAAE,CAACV,CAA1B,GAA8BW,EAAE,CAACX,CAAlC,IAAuCe,EAJ5B,CAAf,CAlB6G,CAyB7G;;AACA,cAAIH,UAAU,GAAG,CAAjB,EAAoB;AAChB,kBAAMK,MAAM,GAAGhC,IAAI,CAAC4B,IAAL,CAAU,IAAI5B,IAAJ,EAAV,EAAsBwB,EAAtB,EAA0BC,EAA1B,EAA8BH,CAA9B,CAAf;AACA,mBAAOtB,IAAI,CAAC4B,IAAL,CAAU,IAAI5B,IAAJ,EAAV,EAAsBgC,MAAtB,EAA8BD,UAA9B,EAA0CJ,UAA1C,CAAP;AACH;;AAED,iBAAOI,UAAP;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,mBAAmB,CAACC,KAAc,GAAG,KAAlB,EAAiC;AACvD;AACA,cAAI,CAAC,KAAKd,kBAAN,IAA4Bc,KAAhC,EAAuC;AACnC,iBAAKd,kBAAL,GAA0B,KAAKe,2BAAL,EAA1B;AACH;;AACD,iBAAO,KAAKf,kBAAZ;AACH;AAED;AACJ;AACA;;;AACYe,QAAAA,2BAA2B,GAAW;AAC1C,cAAI,KAAKC,MAAL,CAAYC,MAAZ,GAAqB,CAAzB,EAA4B;AACxB,mBAAO,KAAKD,MAAL,CAAYE,GAAZ,CAAgBC,CAAC,IAAIA,CAAC,CAACvB,QAAvB,CAAP;AACH;;AAED,gBAAMwB,WAAmB,GAAG,EAA5B;AACA,gBAAMC,UAAU,GAAG,KAAKL,MAAL,CAAYC,MAA/B,CAN0C,CAQ1C;;AACAG,UAAAA,WAAW,CAACE,IAAZ,CAAiB,KAAKN,MAAL,CAAY,CAAZ,EAAepB,QAAhC,EAT0C,CAW1C;;AACA,gBAAM2B,YAAY,GAAG,KAAKC,MAAL,GAAcH,UAAd,GAA2BA,UAAU,GAAG,CAA7D,CAZ0C,CAc1C;;AACA,eAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,YAApB,EAAkCE,CAAC,EAAnC,EAAuC;AACnC,kBAAMtB,EAAE,GAAG,KAAKuB,eAAL,CAAqBD,CAAC,GAAG,CAAzB,CAAX;AACA,kBAAMrB,EAAE,GAAG,KAAKY,MAAL,CAAYS,CAAZ,EAAe7B,QAA1B;AACA,kBAAMS,EAAE,GAAG,KAAKqB,eAAL,CAAqBD,CAAC,GAAG,CAAzB,CAAX;AACA,kBAAMnB,EAAE,GAAG,KAAKoB,eAAL,CAAqBD,CAAC,GAAG,CAAzB,CAAX,CAJmC,CAMnC;;AACA,kBAAME,eAAe,GAAG,KAAKX,MAAL,CAAYS,CAAZ,EAAelB,UAAvC;AACA,kBAAMqB,aAAa,GAAG,KAAKZ,MAAL,CAAY,CAACS,CAAC,GAAG,CAAL,IAAUJ,UAAtB,EAAkCd,UAAxD,CARmC,CAUnC;;AACA,gBAAIoB,eAAe,KAAK,CAApB,IAAyBC,aAAa,KAAK,CAA/C,EAAkD;AAC9C;AACAR,cAAAA,WAAW,CAACE,IAAZ,CAAiBjB,EAAjB;AACH,aAHD,MAGO;AACH;AACA,oBAAME,UAAU,GAAGsB,IAAI,CAACC,GAAL,CAASH,eAAT,EAA0BC,aAA1B,CAAnB,CAFG,CAIH;;AACA,mBAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,KAAKC,QAA1B,EAAoCD,CAAC,EAArC,EAAyC;AACrC,sBAAM7B,CAAC,GAAG6B,CAAC,GAAG,KAAKC,QAAnB;AACA,sBAAMC,KAAK,GAAGnC,QAAQ,CAACG,eAAT,CAAyBC,CAAzB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CC,UAA5C,CAAd;AACAa,gBAAAA,WAAW,CAACE,IAAZ,CAAiBW,KAAjB;AACH;AACJ;AACJ,WAxCyC,CA0C1C;;;AACA,cAAI,KAAKT,MAAL,IAAeJ,WAAW,CAACH,MAAZ,GAAqB,CAAxC,EAA2C;AACvC,kBAAMiB,UAAU,GAAGd,WAAW,CAAC,CAAD,CAA9B;AACA,kBAAMe,SAAS,GAAGf,WAAW,CAACA,WAAW,CAACH,MAAZ,GAAqB,CAAtB,CAA7B;AACA,kBAAMmB,QAAQ,GAAGxD,IAAI,CAACwD,QAAL,CAAcF,UAAd,EAA0BC,SAA1B,CAAjB,CAHuC,CAKvC;;AACA,gBAAIC,QAAQ,GAAG,GAAf,EAAoB;AAChBhB,cAAAA,WAAW,CAACiB,GAAZ;AACH;AACJ;;AAED,iBAAOjB,WAAP;AACH;AAED;AACJ;AACA;;;AACYM,QAAAA,eAAe,CAACY,KAAD,EAAsB;AACzC,gBAAMjB,UAAU,GAAG,KAAKL,MAAL,CAAYC,MAA/B;;AAEA,cAAI,KAAKO,MAAT,EAAiB;AACb;AACA,kBAAMe,YAAY,GAAG,CAAED,KAAK,GAAGjB,UAAT,GAAuBA,UAAxB,IAAsCA,UAA3D;AACA,mBAAO,KAAKL,MAAL,CAAYuB,YAAZ,EAA0B3C,QAAjC;AACH,WAJD,MAIO;AACH;AACA,gBAAI0C,KAAK,GAAG,CAAZ,EAAe;AACX;AACA,oBAAMnC,EAAE,GAAG,KAAKa,MAAL,CAAY,CAAZ,EAAepB,QAA1B;AACA,oBAAMQ,EAAE,GAAG,KAAKY,MAAL,CAAY,CAAZ,EAAepB,QAA1B;AACA,qBAAOhB,IAAI,CAAC4D,QAAL,CAAc,IAAI5D,IAAJ,EAAd,EAA0BuB,EAA1B,EAA8BvB,IAAI,CAAC4D,QAAL,CAAc,IAAI5D,IAAJ,EAAd,EAA0BwB,EAA1B,EAA8BD,EAA9B,CAA9B,CAAP;AACH,aALD,MAKO,IAAImC,KAAK,IAAIjB,UAAb,EAAyB;AAC5B;AACA,oBAAMlB,EAAE,GAAG,KAAKa,MAAL,CAAYK,UAAU,GAAG,CAAzB,EAA4BzB,QAAvC;AACA,oBAAMQ,EAAE,GAAG,KAAKY,MAAL,CAAYK,UAAU,GAAG,CAAzB,EAA4BzB,QAAvC;AACA,qBAAOhB,IAAI,CAAC6D,GAAL,CAAS,IAAI7D,IAAJ,EAAT,EAAqBwB,EAArB,EAAyBxB,IAAI,CAAC4D,QAAL,CAAc,IAAI5D,IAAJ,EAAd,EAA0BwB,EAA1B,EAA8BD,EAA9B,CAAzB,CAAP;AACH,aALM,MAKA;AACH,qBAAO,KAAKa,MAAL,CAAYsB,KAAZ,EAAmB1C,QAA1B;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACW8C,QAAAA,MAAM,GAAQ;AACjB,iBAAO;AACHC,YAAAA,IAAI,EAAE,KAAKA,IADR;AAEHC,YAAAA,QAAQ,EAAE,KAAKA,QAFZ;AAGHC,YAAAA,MAAM,EAAE,KAAKA,MAHV;AAIH7B,YAAAA,MAAM,EAAE,KAAKA,MAJV;AAKHgB,YAAAA,QAAQ,EAAE,KAAKA,QALZ;AAMHR,YAAAA,MAAM,EAAE,KAAKA;AANV,WAAP;AAQH;AAED;AACJ;AACA;;;AACWsB,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AAC7B,eAAKJ,IAAL,GAAYI,IAAI,CAACJ,IAAL,IAAa,EAAzB;AACA,eAAKC,QAAL,GAAgBG,IAAI,CAACH,QAAL,IAAiB,CAAjC;AACA,eAAKC,MAAL,GAAcE,IAAI,CAACF,MAAL,IAAe,CAAC,CAA9B;AACA,eAAK7B,MAAL,GAAc+B,IAAI,CAAC/B,MAAL,IAAe,EAA7B;AACA,eAAKgB,QAAL,GAAgBe,IAAI,CAACf,QAAL,IAAiB,EAAjC;AACA,eAAKR,MAAL,GAAcuB,IAAI,CAACvB,MAAL,IAAe,KAA7B,CAN6B,CAQ7B;;AACA,eAAKxB,kBAAL,GAA0B,IAA1B;AACH;AAED;AACJ;AACA;;;AAC0B,eAAR8C,QAAQ,CAACC,IAAD,EAAsB;AACxC,gBAAMC,QAAQ,GAAG,IAAIlD,QAAJ,EAAjB;AACAkD,UAAAA,QAAQ,CAACF,QAAT,CAAkBC,IAAlB;AACA,iBAAOC,QAAP;AACH;;AAxMiB,O;;;;;iBAEI,E;;;;;;;iBAGI,C;;;;;;;iBAGF,CAAC,C;;;;;;;iBAGI,E;;;;;;;iBAGH,E;;;;;;;iBAGD,K", "sourcesContent": ["import { _decorator, Vec2, CCFloat, CCInteger, Enum } from 'cc';\r\nimport { eOrientationType } from './WaveData';\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 路径点数据\r\n */\r\n@ccclass(\"PathPoint\")\r\nexport class PathPoint {\r\n    @property({ type: CCFloat, displayName: \"X坐标\" })\r\n    public x: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"Y坐标\" })\r\n    public y: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"平滑程度\", range: [0, 1], slide: true, tooltip: \"0=直线连接, 1=最大平滑曲线\" })\r\n    public smoothness: number = 1;\r\n\r\n    @property({ type: CCInteger, displayName: \"速度\", tooltip: \"飞机在此点的速度\" })\r\n    public speed: number = 500;\r\n\r\n    @property({ type: Enum(eOrientationType), displayName: \"朝向类型\", tooltip: \"飞机在此点的朝向\" })\r\n    public orientationType: eOrientationType = 0;\r\n\r\n    @property({ type: CCInteger, displayName: \"朝向参数\", tooltip: \"根据朝向类型不同而不同\" })\r\n    public orientationParam: number = 0;\r\n\r\n    constructor(x: number = 0, y: number = 0) {\r\n        this.x = x;\r\n        this.y = y;\r\n    }\r\n\r\n    public get position(): Vec2 {\r\n        return new Vec2(this.x, this.y);\r\n    }\r\n\r\n    public set position(value: Vec2) {\r\n        this.x = value.x;\r\n        this.y = value.y;\r\n    }\r\n}\r\n\r\n/**\r\n * 路径数据\r\n */\r\n@ccclass(\"PathData\")\r\nexport class PathData {\r\n    @property({ displayName: '路径名称', editorOnly: true })\r\n    public name: string = \"\";\r\n\r\n    @property({ type: CCInteger, displayName: '起始点(默认0)'})\r\n    public startIdx: number = 0;\r\n\r\n    @property({ type: CCInteger, displayName: '结束点(-1代表使用路径终点)'})\r\n    public endIdx: number = -1;\r\n\r\n    @property({ type: [PathPoint], displayName: '路径点' })\r\n    public points: PathPoint[] = [];\r\n\r\n    @property({ type: CCInteger, displayName: \"曲线分段数\", tooltip: \"每段曲线的细分数量，影响曲线平滑度\" })\r\n    public segments: number = 20;\r\n\r\n    @property({ displayName: \"是否闭合路径\", tooltip: \"路径是否形成闭环\" })\r\n    public closed: boolean = false;\r\n\r\n    // 缓存的路径数据（不参与序列化）\r\n    private _cachedCurvePoints: Vec2[] | null = null;\r\n\r\n    /**\r\n     * 获取Catmull-Rom曲线上的点\r\n     * @param t 参数值 [0, 1]\r\n     * @param p0 前一个控制点（用于计算切线）\r\n     * @param p1 起始点（曲线经过此点）\r\n     * @param p2 结束点（曲线经过此点）\r\n     * @param p3 后一个控制点（用于计算切线）\r\n     * @param smoothness 平滑程度 [0, 1]，0=直线，1=最平滑曲线\r\n     */\r\n    public static catmullRomPoint(t: number, p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number = 0.5): Vec2 {\r\n        // 当smoothness为0时，直接返回线性插值（直线）\r\n        if (smoothness === 0) {\r\n            return Vec2.lerp(new Vec2(), p1, p2, t);\r\n        }\r\n\r\n        const t2 = t * t;\r\n        const t3 = t2 * t;\r\n\r\n        // 标准Catmull-Rom插值公式\r\n        const catmullRom = new Vec2();\r\n        catmullRom.x = 0.5 * (\r\n            (2 * p1.x) +\r\n            (-p0.x + p2.x) * t +\r\n            (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 +\r\n            (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3\r\n        );\r\n\r\n        catmullRom.y = 0.5 * (\r\n            (2 * p1.y) +\r\n            (-p0.y + p2.y) * t +\r\n            (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 +\r\n            (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3\r\n        );\r\n\r\n        // 当smoothness不为1时，在线性插值和Catmull-Rom之间混合\r\n        if (smoothness < 1) {\r\n            const linear = Vec2.lerp(new Vec2(), p1, p2, t);\r\n            return Vec2.lerp(new Vec2(), linear, catmullRom, smoothness);\r\n        }\r\n\r\n        return catmullRom;\r\n    }\r\n\r\n    /**\r\n     * 生成完整的曲线路径点（带缓存）\r\n     */\r\n    public generateCurvePoints(regen: boolean = false): Vec2[] {\r\n        // 生成并缓存曲线点\r\n        if (!this._cachedCurvePoints || regen) {\r\n            this._cachedCurvePoints = this.generateCurvePointsInternal();\r\n        }\r\n        return this._cachedCurvePoints;\r\n    }\r\n\r\n    /**\r\n     * 内部方法：实际生成曲线点\r\n     */\r\n    private generateCurvePointsInternal(): Vec2[] {\r\n        if (this.points.length < 2) {\r\n            return this.points.map(p => p.position);\r\n        }\r\n\r\n        const curvePoints: Vec2[] = [];\r\n        const pointCount = this.points.length;\r\n\r\n        // 添加第一个点（确保曲线经过起点）\r\n        curvePoints.push(this.points[0].position);\r\n\r\n        // 计算需要处理的段数\r\n        const segmentCount = this.closed ? pointCount : pointCount - 1;\r\n\r\n        // 为每一段生成曲线点\r\n        for (let i = 0; i < segmentCount; i++) {\r\n            const p0 = this.getControlPoint(i - 1);\r\n            const p1 = this.points[i].position;\r\n            const p2 = this.getControlPoint(i + 1);\r\n            const p3 = this.getControlPoint(i + 2);\r\n\r\n            // 检查是否有任何一个端点要求直线连接\r\n            const startSmoothness = this.points[i].smoothness;\r\n            const endSmoothness = this.points[(i + 1) % pointCount].smoothness;\r\n\r\n            // 如果任一端点的smoothness为0，则整段使用直线\r\n            if (startSmoothness === 0 || endSmoothness === 0) {\r\n                // 直线连接：只需要添加终点即可（起点已经添加过了）\r\n                curvePoints.push(p2);\r\n            } else {\r\n                // 使用平滑程度的最小值（更保守的方法）\r\n                const smoothness = Math.min(startSmoothness, endSmoothness);\r\n\r\n                // 生成这一段的曲线点（不包括起点，因为已经添加过了）\r\n                for (let j = 1; j <= this.segments; j++) {\r\n                    const t = j / this.segments;\r\n                    const point = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);\r\n                    curvePoints.push(point);\r\n                }\r\n            }\r\n        }\r\n\r\n        // 对于闭合路径，移除可能重复的最后一个点（如果它与第一个点太接近）\r\n        if (this.closed && curvePoints.length > 1) {\r\n            const firstPoint = curvePoints[0];\r\n            const lastPoint = curvePoints[curvePoints.length - 1];\r\n            const distance = Vec2.distance(firstPoint, lastPoint);\r\n\r\n            // 如果最后一个点与第一个点距离很近，移除最后一个点\r\n            if (distance < 0.1) {\r\n                curvePoints.pop();\r\n            }\r\n        }\r\n\r\n        return curvePoints;\r\n    }\r\n\r\n    /**\r\n     * 获取控制点（处理边界情况）\r\n     */\r\n    private getControlPoint(index: number): Vec2 {\r\n        const pointCount = this.points.length;\r\n\r\n        if (this.closed) {\r\n            // 闭合路径，使用循环索引\r\n            const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;\r\n            return this.points[wrappedIndex].position;\r\n        } else {\r\n            // 开放路径，边界处理\r\n            if (index < 0) {\r\n                // 延伸第一个点\r\n                const p0 = this.points[0].position;\r\n                const p1 = this.points[1].position;\r\n                return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else if (index >= pointCount) {\r\n                // 延伸最后一个点\r\n                const p0 = this.points[pointCount - 2].position;\r\n                const p1 = this.points[pointCount - 1].position;\r\n                return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else {\r\n                return this.points[index].position;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 自定义序列化 - 排除缓存数据\r\n     */\r\n    public toJSON(): any {\r\n        return {\r\n            name: this.name,\r\n            startIdx: this.startIdx,\r\n            endIdx: this.endIdx,\r\n            points: this.points,\r\n            segments: this.segments,\r\n            closed: this.closed\r\n        };\r\n    }\r\n\r\n    /**\r\n     * 自定义反序列化 - 清除缓存确保重新计算\r\n     */\r\n    public fromJSON(data: any): void {\r\n        this.name = data.name || \"\";\r\n        this.startIdx = data.startIdx || 0;\r\n        this.endIdx = data.endIdx || -1;\r\n        this.points = data.points || [];\r\n        this.segments = data.segments || 20;\r\n        this.closed = data.closed || false;\r\n\r\n        // 清除缓存，确保使用新数据重新计算\r\n        this._cachedCurvePoints = null;\r\n    }\r\n\r\n    /**\r\n     * 静态工厂方法 - 从JSON创建PathData实例\r\n     */\r\n    public static fromJSON(data: any): PathData {\r\n        const pathData = new PathData();\r\n        pathData.fromJSON(data);\r\n        return pathData;\r\n    }\r\n}"]}