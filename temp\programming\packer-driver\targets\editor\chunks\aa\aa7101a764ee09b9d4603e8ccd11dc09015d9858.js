System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Enum, misc, UITransform, Vec2, JsonAsset, BulletSystem, PathData, eSpriteDefaultFacing, eMoveEvent, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _class3, _crd, degreesToRadians, radiansToDegrees, ccclass, property, PathMovable;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "../bullet/BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIMovable(extras) {
    _reporterNs.report("IMovable", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "../data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeSpriteDefaultFacing(extras) {
    _reporterNs.report("eSpriteDefaultFacing", "./Movable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeMoveEvent(extras) {
    _reporterNs.report("eMoveEvent", "./Movable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Enum = _cc.Enum;
      misc = _cc.misc;
      UITransform = _cc.UITransform;
      Vec2 = _cc.Vec2;
      JsonAsset = _cc.JsonAsset;
    }, function (_unresolved_2) {
      BulletSystem = _unresolved_2.BulletSystem;
    }, function (_unresolved_3) {
      PathData = _unresolved_3.PathData;
    }, function (_unresolved_4) {
      eSpriteDefaultFacing = _unresolved_4.eSpriteDefaultFacing;
      eMoveEvent = _unresolved_4.eMoveEvent;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0793c9EHOFKg6LLrnZPLKct", "PathMovable", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Enum', 'misc', 'Node', 'UITransform', 'Vec2', 'Vec3', 'JsonAsset']);

      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);
      ({
        ccclass,
        property
      } = _decorator);

      _export("PathMovable", PathMovable = (_dec = ccclass('PathMovable'), _dec2 = property({
        type: JsonAsset,
        displayName: "路径数据"
      }), _dec3 = property({
        type: Enum(_crd && eSpriteDefaultFacing === void 0 ? (_reportPossibleCrUseOfeSpriteDefaultFacing({
          error: Error()
        }), eSpriteDefaultFacing) : eSpriteDefaultFacing),
        displayName: '图片默认朝向'
      }), _dec4 = property({
        displayName: "是否朝向移动方向"
      }), _dec5 = property({
        displayName: "移动速度",
        tooltip: "沿路径移动的速度(像素/秒)"
      }), _dec6 = property({
        displayName: "加速度",
        tooltip: "速度变化率(像素/秒²)"
      }), _dec7 = property({
        displayName: "循环移动"
      }), _dec8 = property({
        displayName: "反向移动"
      }), _dec9 = property({
        displayName: "振荡偏移速度",
        tooltip: "控制倾斜振荡的频率"
      }), _dec10 = property({
        displayName: "振荡偏移幅度",
        tooltip: "控制倾斜振荡的幅度"
      }), _dec(_class = (_class2 = (_class3 = class PathMovable extends Component {
        constructor(...args) {
          super(...args);
          this.speedAngle = 0;
          this.accelerationAngle = 0;

          _initializerDefineProperty(this, "pathAsset", _descriptor, this);

          _initializerDefineProperty(this, "defaultFacing", _descriptor2, this);

          _initializerDefineProperty(this, "isFacingMoveDir", _descriptor3, this);

          _initializerDefineProperty(this, "speed", _descriptor4, this);

          _initializerDefineProperty(this, "acceleration", _descriptor5, this);

          _initializerDefineProperty(this, "loop", _descriptor6, this);

          _initializerDefineProperty(this, "reverse", _descriptor7, this);

          _initializerDefineProperty(this, "tiltSpeed", _descriptor8, this);

          _initializerDefineProperty(this, "tiltOffset", _descriptor9, this);

          // 路径相关数据
          this._pathData = null;
          this._curvePoints = [];
          this._totalDistance = 0;
          this._distances = [];
          // 移动状态
          this._currentDistance = 0;
          this._isMovable = true;
          this._tiltTime = 0;
          // 可见性检查
          this._selfSize = new Vec2();
          this._wasVisible = false;
          this._isVisible = false;
          this._visibilityCheckCounter = 0;
          // 事件系统
          this._eventListeners = new Map();
        }

        get isVisible() {
          return this._isVisible;
        }

        get isMovable() {
          return this._isMovable;
        }

        onLoad() {
          const uiTransform = this.node.getComponent(UITransform);
          const self_size = uiTransform ? uiTransform.contentSize : {
            width: 0,
            height: 0
          };

          this._selfSize.set(self_size.width / 2, self_size.height / 2);

          this.loadPathData();
        }

        onDestroy() {
          this._eventListeners.clear();
        }
        /**
         * 加载路径数据（使用PathData内置缓存）
         */


        loadPathData() {
          if (!this.pathAsset) return; // 创建PathData实例并加载数据

          this._pathData = (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData).fromJSON(this.pathAsset.json);
          if (this._pathData.points.length < 2) return; // 使用PathData的内置缓存获取曲线点

          this._curvePoints = this._pathData.generateCurvePoints(); // 计算距离信息

          this.calculateDistances();
        }
        /**
         * 计算距离信息
         */


        calculateDistances() {
          this._distances = [0];
          this._totalDistance = 0;

          for (let i = 1; i < this._curvePoints.length; i++) {
            const distance = Vec2.distance(this._curvePoints[i - 1], this._curvePoints[i]);
            this._totalDistance += distance;

            this._distances.push(this._totalDistance);
          }
        }
        /**
         * 主要的移动更新逻辑
         */


        tick(dt) {
          if (!this._isMovable || this._curvePoints.length < 2) return; // 应用加速度

          if (this.acceleration !== 0) {
            this.speed += this.acceleration * dt;
            this.speed = Math.max(0, this.speed); // 确保速度不为负
          } // 更新沿路径的距离


          const deltaDistance = this.speed * dt;

          if (this.reverse) {
            this._currentDistance -= deltaDistance;

            if (this._currentDistance < 0) {
              if (this.loop) {
                this._currentDistance = this._totalDistance + this._currentDistance;
              } else {
                this._currentDistance = 0;
              }
            }
          } else {
            this._currentDistance += deltaDistance;

            if (this._currentDistance > this._totalDistance) {
              if (this.loop) {
                this._currentDistance = this._currentDistance - this._totalDistance;
              } else {
                this._currentDistance = this._totalDistance;
              }
            }
          }

          this.updatePosition(dt);
        }
        /**
         * 更新节点位置和朝向
         */


        updatePosition(dt) {
          const position = this.getPositionAtDistance(this._currentDistance); // 应用倾斜偏移

          if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
            this._tiltTime += dt;
            const direction = this.getDirectionAtDistance(this._currentDistance);

            if (direction.lengthSqr() > 0.001) {
              // 计算垂直于移动方向的向量
              const perpX = -direction.y;
              const perpY = direction.x; // 计算倾斜偏移

              const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;
              position.x += perpX * tiltAmount;
              position.y += perpY * tiltAmount;
            }
          }

          this.node.setPosition(position.x, position.y, 0); // 更新朝向

          if (this.isFacingMoveDir) {
            const direction = this.getDirectionAtDistance(this._currentDistance);

            if (direction.lengthSqr() > 0.001) {
              const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
              const finalAngle = angle + this.defaultFacing;
              this.node.setRotationFromEuler(0, 0, finalAngle);
            }
          } // 可见性检查


          if (++this._visibilityCheckCounter >= PathMovable.VISIBILITY_CHECK_INTERVAL) {
            this._visibilityCheckCounter = 0;
            this.checkVisibility();
          }
        }
        /**
         * 根据距离获取位置
         */


        getPositionAtDistance(distance) {
          if (distance <= 0) return this._curvePoints[0].clone();
          if (distance >= this._totalDistance) return this._curvePoints[this._curvePoints.length - 1].clone();

          for (let i = 1; i < this._distances.length; i++) {
            if (distance <= this._distances[i]) {
              const segmentStart = this._distances[i - 1];
              const segmentEnd = this._distances[i];
              const segmentLength = segmentEnd - segmentStart;
              if (segmentLength === 0) return this._curvePoints[i - 1].clone();
              const t = (distance - segmentStart) / segmentLength;
              return Vec2.lerp(new Vec2(), this._curvePoints[i - 1], this._curvePoints[i], t);
            }
          }

          return this._curvePoints[this._curvePoints.length - 1].clone();
        }
        /**
         * 根据距离获取移动方向
         */


        getDirectionAtDistance(distance) {
          const epsilon = 1;
          const pos1 = this.getPositionAtDistance(distance);
          const pos2 = this.getPositionAtDistance(distance + epsilon);
          return Vec2.subtract(new Vec2(), pos2, pos1).normalize();
        }
        /**
         * 可见性检查
         */


        checkVisibility() {
          const visibleSize = (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).worldBounds;
          const position = this.node.worldPosition;
          const isVisible = position.x + this._selfSize.x >= visibleSize.xMin && position.x - this._selfSize.x <= visibleSize.xMax && position.y - this._selfSize.y <= visibleSize.yMax && position.y + this._selfSize.y >= visibleSize.yMin;
          this.setVisible(isVisible);
        }

        setVisible(visible) {
          if (visible) {
            if (!this._wasVisible) this.emit((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
              error: Error()
            }), eMoveEvent) : eMoveEvent).onBecomeVisible);
          } else {
            if (this._wasVisible) this.emit((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
              error: Error()
            }), eMoveEvent) : eMoveEvent).onBecomeInvisible);
          }

          this._wasVisible = this._isVisible;
          this._isVisible = visible;
        } // 事件系统方法


        on(event, listener) {
          if (!this._eventListeners.has(event)) {
            this._eventListeners.set(event, []);
          }

          const listeners = this._eventListeners.get(event);

          if (!listeners.includes(listener)) {
            listeners.push(listener);
          }
        }

        off(event, listener) {
          const listeners = this._eventListeners.get(event);

          if (listeners) {
            const index = listeners.indexOf(listener);

            if (index !== -1) {
              listeners.splice(index, 1);
            }
          }
        }

        removeAllListeners() {
          this._eventListeners.clear();
        }

        emit(event) {
          const listeners = this._eventListeners.get(event);

          if (listeners && listeners.length > 0) {
            listeners.forEach(listener => listener());
          }
        } // 公共API方法


        setMovable(movable) {
          this._isMovable = movable;
          return this;
        }
        /**
         * 设置路径进度 [0-1]
         */


        setProgress(progress) {
          this._currentDistance = Math.max(0, Math.min(1, progress)) * this._totalDistance;
          return this;
        }
        /**
         * 获取当前进度 [0-1]
         */


        getProgress() {
          return this._totalDistance > 0 ? this._currentDistance / this._totalDistance : 0;
        }
        /**
         * 重置到路径起点
         */


        resetToStart() {
          this._currentDistance = 0;
          return this;
        }
        /**
         * 移动到路径终点
         */


        moveToEnd() {
          this._currentDistance = this._totalDistance;
          return this;
        }
        /**
         * 获取当前位置对应的路径点数据
         */


        getCurrentPathPointData() {
          if (!this._pathData || this._pathData.points.length === 0) return null;
          const progress = this.getProgress();
          const pointIndex = Math.floor(progress * (this._pathData.points.length - 1));
          const clampedIndex = Math.max(0, Math.min(this._pathData.points.length - 1, pointIndex));
          return this._pathData.points[clampedIndex];
        }

      }, _class3.VISIBILITY_CHECK_INTERVAL = 5, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "pathAsset", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "defaultFacing", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && eSpriteDefaultFacing === void 0 ? (_reportPossibleCrUseOfeSpriteDefaultFacing({
            error: Error()
          }), eSpriteDefaultFacing) : eSpriteDefaultFacing).Up;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "isFacingMoveDir", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 100;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "acceleration", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "loop", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "reverse", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "tiltSpeed", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "tiltOffset", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=aa7101a764ee09b9d4603e8ccd11dc09015d9858.js.map