{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts"], "names": ["_decorator", "Node", "Color", "Component", "JsonAsset", "Graphics", "PathData", "PathPoint", "PathPointEditor", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "requireComponent", "PathEditor", "type", "displayName", "_graphics", "_pathData", "_pathDataObj", "_cachedChildrenCount", "graphics", "node", "getComponent", "addComponent", "pathData", "value", "reload", "pathName", "name", "isClosed", "closed", "Object", "assign", "json", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "points", "length", "for<PERSON>ach", "point", "addPoint", "updateCurve", "save", "pointEditors", "getComponentsInChildren", "map", "editor", "pathPoint", "JSON", "stringify", "pointNode", "parent", "setPosition", "position", "x", "y", "pointEditor", "addNewPoint", "drawPath", "clear", "strokeColor", "curveColor", "lineWidth", "curvePoints", "generateCurvePoints", "moveTo", "i", "lineTo", "stroke", "showDirectionArrow", "drawPathDirectionArrow", "update", "_dt", "childrenCount", "children", "endPoint", "prevPoint", "direction", "Math", "atan2", "<PERSON><PERSON><PERSON><PERSON>", "arrowHeadLength", "arrowHeadAngle", "PI", "RED", "fillColor", "arrowStartX", "arrowStartY", "arrowEndX", "cos", "arrowEndY", "sin", "leftX", "leftY", "rightX", "rightY", "close", "fill", "WHITE", "GRAY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,Q,OAAAA,Q;;AAGrDC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;AACVC,MAAAA,e,iBAAAA,e;;;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA,IAA1D;AAAgEC,QAAAA;AAAhE,O,GAAqFd,U;;4BAU9Ee,U,WALZN,OAAO,CAAC,YAAD,C,UACPI,IAAI,CAAC,aAAD,C,UACJC,gBAAgB,CAACT,QAAD,C,UAChBK,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAUZD,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEZ,SAAR;AAAmBa,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UASRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAQRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAQRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,uFA7Cb,MAKaF,UALb,SAKgCZ,SALhC,CAK0C;AAAA;AAAA;AAAA,eAC9Be,SAD8B,GACD,IADC;;AAAA;;AAAA;;AAAA;;AAAA,eA2C9BC,SA3C8B,GA2CA,IA3CA;AAAA,eA4C9BC,YA5C8B,GA4CL;AAAA;AAAA,qCA5CK;AAAA,eA6C9BC,oBA7C8B,GA6CC,CA7CD;AAAA;;AAEnB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKJ,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKK,IAAL,CAAUC,YAAV,CAAuBnB,QAAvB,KAAoC,KAAKkB,IAAL,CAAUE,YAAV,CAAuBpB,QAAvB,CAArD;AACH;;AACD,iBAAO,KAAKa,SAAZ;AACH;;AAGkB,YAARQ,QAAQ,CAACC,KAAD,EAAmB;AAClC,eAAKR,SAAL,GAAiBQ,KAAjB;AACA,eAAKC,MAAL;AACH;;AACkB,YAARF,QAAQ,GAAqB;AACpC,iBAAO,KAAKP,SAAZ;AACH;;AAGkB,YAARU,QAAQ,GAAW;AAC1B,iBAAO,KAAKT,YAAL,CAAkBU,IAAzB;AACH;;AACkB,YAARD,QAAQ,CAACF,KAAD,EAAgB;AAC/B,eAAKP,YAAL,CAAkBU,IAAlB,GAAyBH,KAAzB;AACH;;AAGkB,YAARI,QAAQ,GAAY;AAC3B,iBAAO,KAAKX,YAAL,CAAkBY,MAAzB;AACH;;AACkB,YAARD,QAAQ,CAACJ,KAAD,EAAiB;AAChC,eAAKP,YAAL,CAAkBY,MAAlB,GAA2BL,KAA3B;AACH;;AAeMC,QAAAA,MAAM,GAAG;AACZ,cAAI,CAAC,KAAKT,SAAV,EAAqB;AAErB,gBAAMO,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAO,UAAAA,MAAM,CAACC,MAAP,CAAcR,QAAd,EAAwB,KAAKP,SAAL,CAAegB,IAAvC;AACA,eAAKf,YAAL,GAAoBM,QAApB;AAEA,eAAKH,IAAL,CAAUa,iBAAV;;AACA,cAAI,KAAKhB,YAAL,IAAqB,KAAKA,YAAL,CAAkBiB,MAAlB,CAAyBC,MAAzB,GAAkC,CAA3D,EAA8D;AAC1D,iBAAKlB,YAAL,CAAkBiB,MAAlB,CAAyBE,OAAzB,CAAkCC,KAAD,IAAW;AACxC,mBAAKC,QAAL,CAAcD,KAAd;AACH,aAFD;AAGH;;AACD,eAAKE,WAAL;AACH;;AAEMC,QAAAA,IAAI,GAAW;AAClB;AACA,gBAAMC,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAKzB,YAAL,CAAkBiB,MAAlB,GAA2BO,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACA,iBAAOC,IAAI,CAACC,SAAL,CAAe,KAAK9B,YAApB,EAAkC,IAAlC,EAAwC,CAAxC,CAAP;AACH;;AAEMqB,QAAAA,QAAQ,CAACD,KAAD,EAAmB;AAC9B,gBAAMW,SAAS,GAAG,IAAIlD,IAAJ,EAAlB;AACAkD,UAAAA,SAAS,CAACC,MAAV,GAAmB,KAAK7B,IAAxB;AACA4B,UAAAA,SAAS,CAACE,WAAV,CAAsBb,KAAK,CAACc,QAAN,CAAeC,CAArC,EAAwCf,KAAK,CAACc,QAAN,CAAeE,CAAvD,EAA0D,CAA1D;AAEA,gBAAMC,WAAW,GAAGN,SAAS,CAAC1B,YAAV;AAAA;AAAA,iDAApB;AACAgC,UAAAA,WAAW,CAACT,SAAZ,GAAwBR,KAAxB;AACH;;AAEMkB,QAAAA,WAAW,CAACH,CAAD,EAAYC,CAAZ,EAAuB;AACrC,gBAAMhB,KAAK,GAAG;AAAA;AAAA,sCAAce,CAAd,EAAiBC,CAAjB,CAAd;AACA,eAAKf,QAAL,CAAcD,KAAd;AACA,eAAKE,WAAL;AACH;;AAEMA,QAAAA,WAAW,GAAG;AACjB;AACA,gBAAME,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAKzB,YAAL,CAAkBiB,MAAlB,GAA2BO,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACH;;AAEOW,QAAAA,QAAQ,GAAG;AACf,gBAAMrC,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACsC,KAAT;AAEA,cAAI,KAAKxC,YAAL,CAAkBiB,MAAlB,CAAyBC,MAAzB,GAAkC,CAAtC,EAAyC,OAJ1B,CAMf;;AACAhB,UAAAA,QAAQ,CAACuC,WAAT,GAAuB,KAAKC,UAA5B;AACAxC,UAAAA,QAAQ,CAACyC,SAAT,GAAqB,CAArB;;AAEA,gBAAMC,WAAW,GAAG,KAAK5C,YAAL,CAAkB6C,mBAAlB,EAApB;;AACA,cAAID,WAAW,CAAC1B,MAAZ,GAAqB,CAAzB,EAA4B;AACxBhB,YAAAA,QAAQ,CAAC4C,MAAT,CAAgBF,WAAW,CAAC,CAAD,CAAX,CAAeT,CAA/B,EAAkCS,WAAW,CAAC,CAAD,CAAX,CAAeR,CAAjD;;AACA,iBAAK,IAAIW,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,WAAW,CAAC1B,MAAhC,EAAwC6B,CAAC,EAAzC,EAA6C;AACzC7C,cAAAA,QAAQ,CAAC8C,MAAT,CAAgBJ,WAAW,CAACG,CAAD,CAAX,CAAeZ,CAA/B,EAAkCS,WAAW,CAACG,CAAD,CAAX,CAAeX,CAAjD;AACH;;AACDlC,YAAAA,QAAQ,CAAC+C,MAAT,GALwB,CAOxB;;AACA,gBAAI,KAAKC,kBAAT,EAA6B;AACzB,mBAAKC,sBAAL,CAA4BjD,QAA5B,EAAsC0C,WAAtC;AACH;AACJ;AACJ;;AAEMQ,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB,gBAAMC,aAAa,GAAG,KAAKnD,IAAL,CAAUoD,QAAV,CAAmBrC,MAAzC;;AACA,cAAIoC,aAAa,KAAK,KAAKrD,oBAA3B,EAAiD;AAC7C,iBAAKA,oBAAL,GAA4BqD,aAA5B;AACH;;AACD,eAAKhC,WAAL;AACA,eAAKiB,QAAL;AACH;AAED;AACJ;AACA;;;AACYY,QAAAA,sBAAsB,CAACjD,QAAD,EAAqB0C,WAArB,EAAyC;AACnE,cAAIA,WAAW,CAAC1B,MAAZ,GAAqB,CAAzB,EAA4B,OADuC,CAGnE;;AACA,cAAI,KAAKlB,YAAL,CAAkBY,MAAtB,EAA8B,OAJqC,CAMnE;;AACA,gBAAM4C,QAAQ,GAAGZ,WAAW,CAACA,WAAW,CAAC1B,MAAZ,GAAqB,CAAtB,CAA5B;AACA,cAAIuC,SAAS,GAAGb,WAAW,CAACA,WAAW,CAAC1B,MAAZ,GAAqB,CAAtB,CAA3B,CARmE,CAUnE;;AACA,cAAI0B,WAAW,CAAC1B,MAAZ,IAAsB,CAA1B,EAA6B;AACzBuC,YAAAA,SAAS,GAAGb,WAAW,CAACA,WAAW,CAAC1B,MAAZ,GAAqB,CAAtB,CAAvB;AACH,WAbkE,CAenE;;;AACA,gBAAMwC,SAAS,GAAGC,IAAI,CAACC,KAAL,CAAWJ,QAAQ,CAACpB,CAAT,GAAaqB,SAAS,CAACrB,CAAlC,EAAqCoB,QAAQ,CAACrB,CAAT,GAAasB,SAAS,CAACtB,CAA5D,CAAlB,CAhBmE,CAkBnE;;AACA,gBAAM0B,WAAW,GAAG,EAApB;AACA,gBAAMC,eAAe,GAAG,EAAxB;AACA,gBAAMC,cAAc,GAAGJ,IAAI,CAACK,EAAL,GAAU,CAAjC,CArBmE,CAqB/B;AAEpC;;AACA9D,UAAAA,QAAQ,CAACuC,WAAT,GAAuB3D,KAAK,CAACmF,GAA7B;AACA/D,UAAAA,QAAQ,CAACgE,SAAT,GAAqBpF,KAAK,CAACmF,GAA3B;AACA/D,UAAAA,QAAQ,CAACyC,SAAT,GAAqB,CAArB,CA1BmE,CA4BnE;;AACA,gBAAMwB,WAAW,GAAGX,QAAQ,CAACrB,CAA7B;AACA,gBAAMiC,WAAW,GAAGZ,QAAQ,CAACpB,CAA7B,CA9BmE,CAgCnE;;AACA,gBAAMiC,SAAS,GAAGF,WAAW,GAAGR,IAAI,CAACW,GAAL,CAASZ,SAAT,IAAsBG,WAAtD;AACA,gBAAMU,SAAS,GAAGH,WAAW,GAAGT,IAAI,CAACa,GAAL,CAASd,SAAT,IAAsBG,WAAtD,CAlCmE,CAoCnE;;AACA3D,UAAAA,QAAQ,CAAC4C,MAAT,CAAgBqB,WAAhB,EAA6BC,WAA7B;AACAlE,UAAAA,QAAQ,CAAC8C,MAAT,CAAgBqB,SAAhB,EAA2BE,SAA3B;AACArE,UAAAA,QAAQ,CAAC+C,MAAT,GAvCmE,CAyCnE;;AACA,gBAAMwB,KAAK,GAAGJ,SAAS,GAAGV,IAAI,CAACW,GAAL,CAASZ,SAAS,GAAGK,cAArB,IAAuCD,eAAjE;AACA,gBAAMY,KAAK,GAAGH,SAAS,GAAGZ,IAAI,CAACa,GAAL,CAASd,SAAS,GAAGK,cAArB,IAAuCD,eAAjE;AACA,gBAAMa,MAAM,GAAGN,SAAS,GAAGV,IAAI,CAACW,GAAL,CAASZ,SAAS,GAAGK,cAArB,IAAuCD,eAAlE;AACA,gBAAMc,MAAM,GAAGL,SAAS,GAAGZ,IAAI,CAACa,GAAL,CAASd,SAAS,GAAGK,cAArB,IAAuCD,eAAlE,CA7CmE,CA+CnE;;AACA5D,UAAAA,QAAQ,CAAC4C,MAAT,CAAgBuB,SAAhB,EAA2BE,SAA3B;AACArE,UAAAA,QAAQ,CAAC8C,MAAT,CAAgByB,KAAhB,EAAuBC,KAAvB;AACAxE,UAAAA,QAAQ,CAAC8C,MAAT,CAAgB2B,MAAhB,EAAwBC,MAAxB;AACA1E,UAAAA,QAAQ,CAAC2E,KAAT;AACA3E,UAAAA,QAAQ,CAAC4E,IAAT;AACA5E,UAAAA,QAAQ,CAAC+C,MAAT;AACH;;AAtLqC,O;;;;;iBAmCXnE,KAAK,CAACiG,K;;;;;;;iBAGAjG,KAAK,CAACkG,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Node, Color, Component, JsonAsset, Vec3, Graphics, input, Input, EventMouse } from 'cc';\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent } = _decorator;\r\nimport { EDITOR } from 'cc/env';\r\nimport { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { PathPointEditor } from './PathPointEditor';\r\n\r\n@ccclass('PathEditor')\r\n@menu(\"怪物/编辑器/路径编辑\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathEditor extends Component {\r\n    private _graphics: Graphics | null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n        return this._graphics;\r\n    }\r\n\r\n    @property({ type: JsonAsset, displayName: \"路径数据\" })\r\n    public set pathData(value: JsonAsset) {\r\n        this._pathData = value;\r\n        this.reload();\r\n    }\r\n    public get pathData(): JsonAsset | null {\r\n        return this._pathData;\r\n    }\r\n\r\n    @property({ displayName: \"路径名称\" })\r\n    public get pathName(): string {\r\n        return this._pathDataObj.name;\r\n    }\r\n    public set pathName(value: string) {\r\n        this._pathDataObj.name = value;\r\n    }\r\n\r\n    @property({ displayName: \"是否闭合\"})\r\n    public get isClosed(): boolean {\r\n        return this._pathDataObj.closed;\r\n    }\r\n    public set isClosed(value: boolean) {\r\n        this._pathDataObj.closed = value;\r\n    }\r\n\r\n    @property({ displayName: \"曲线颜色\" })\r\n    public curveColor: Color = Color.WHITE;\r\n\r\n    @property({ displayName: \"控制线颜色\" })\r\n    public controlLineColor: Color = Color.GRAY;\r\n\r\n    @property({ displayName: \"显示方向箭头\" })\r\n    public showDirectionArrow: boolean = true;\r\n\r\n    private _pathData: JsonAsset | null = null;\r\n    private _pathDataObj: PathData = new PathData();\r\n    private _cachedChildrenCount: number = 0;\r\n\r\n    public reload() {\r\n        if (!this._pathData) return;\r\n\r\n        const pathData = new PathData();\r\n        Object.assign(pathData, this._pathData.json);\r\n        this._pathDataObj = pathData;\r\n\r\n        this.node.removeAllChildren();\r\n        if (this._pathDataObj && this._pathDataObj.points.length > 0) {\r\n            this._pathDataObj.points.forEach((point) => {\r\n                this.addPoint(point);\r\n            });\r\n        }\r\n        this.updateCurve();\r\n    }\r\n\r\n    public save(): string {\r\n        // 收集所有路径点数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n        return JSON.stringify(this._pathDataObj, null, 2);\r\n    }\r\n\r\n    public addPoint(point: PathPoint) {\r\n        const pointNode = new Node();\r\n        pointNode.parent = this.node;\r\n        pointNode.setPosition(point.position.x, point.position.y, 0);\r\n\r\n        const pointEditor = pointNode.addComponent(PathPointEditor);\r\n        pointEditor.pathPoint = point;\r\n    }\r\n\r\n    public addNewPoint(x: number, y: number) {\r\n        const point = new PathPoint(x, y);\r\n        this.addPoint(point);\r\n        this.updateCurve();\r\n    }\r\n\r\n    public updateCurve() {\r\n        // 收集当前所有点的数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n    }\r\n\r\n    private drawPath() {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n\r\n        if (this._pathDataObj.points.length < 2) return;\r\n\r\n        // 绘制Catmull-Rom曲线\r\n        graphics.strokeColor = this.curveColor;\r\n        graphics.lineWidth = 5;\r\n\r\n        const curvePoints = this._pathDataObj.generateCurvePoints();\r\n        if (curvePoints.length > 1) {\r\n            graphics.moveTo(curvePoints[0].x, curvePoints[0].y);\r\n            for (let i = 1; i < curvePoints.length; i++) {\r\n                graphics.lineTo(curvePoints[i].x, curvePoints[i].y);\r\n            }\r\n            graphics.stroke();\r\n\r\n            // 绘制路径终点的方向箭头\r\n            if (this.showDirectionArrow) {\r\n                this.drawPathDirectionArrow(graphics, curvePoints);\r\n            }\r\n        }\r\n    }\r\n\r\n    public update(_dt: number) {\r\n        const childrenCount = this.node.children.length;\r\n        if (childrenCount !== this._cachedChildrenCount) {\r\n            this._cachedChildrenCount = childrenCount;\r\n        }\r\n        this.updateCurve();\r\n        this.drawPath();\r\n    }\r\n\r\n    /**\r\n     * 绘制路径方向箭头\r\n     */\r\n    private drawPathDirectionArrow(graphics: Graphics, curvePoints: any[]) {\r\n        if (curvePoints.length < 2) return;\r\n\r\n        // 如果是闭合路径，不绘制箭头（因为没有明确的终点）\r\n        if (this._pathDataObj.closed) return;\r\n\r\n        // 计算终点的方向（使用最后几个点来获得更准确的方向）\r\n        const endPoint = curvePoints[curvePoints.length - 1];\r\n        let prevPoint = curvePoints[curvePoints.length - 2];\r\n\r\n        // 如果有足够的点，使用更远的点来计算方向，获得更平滑的方向\r\n        if (curvePoints.length >= 5) {\r\n            prevPoint = curvePoints[curvePoints.length - 5];\r\n        }\r\n\r\n        // 计算方向角度\r\n        const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);\r\n\r\n        // 箭头参数\r\n        const arrowLength = 40;\r\n        const arrowHeadLength = 20;\r\n        const arrowHeadAngle = Math.PI / 5; // 36度，更尖锐的箭头\r\n\r\n        // 设置箭头样式\r\n        graphics.strokeColor = Color.RED;\r\n        graphics.fillColor = Color.RED;\r\n        graphics.lineWidth = 3;\r\n\r\n        // 计算箭头起点（从路径终点开始）\r\n        const arrowStartX = endPoint.x;\r\n        const arrowStartY = endPoint.y;\r\n\r\n        // 计算箭头终点\r\n        const arrowEndX = arrowStartX + Math.cos(direction) * arrowLength;\r\n        const arrowEndY = arrowStartY + Math.sin(direction) * arrowLength;\r\n\r\n        // 绘制箭头主线\r\n        graphics.moveTo(arrowStartX, arrowStartY);\r\n        graphics.lineTo(arrowEndX, arrowEndY);\r\n        graphics.stroke();\r\n\r\n        // 绘制箭头头部（填充三角形）\r\n        const leftX = arrowEndX - Math.cos(direction - arrowHeadAngle) * arrowHeadLength;\r\n        const leftY = arrowEndY - Math.sin(direction - arrowHeadAngle) * arrowHeadLength;\r\n        const rightX = arrowEndX - Math.cos(direction + arrowHeadAngle) * arrowHeadLength;\r\n        const rightY = arrowEndY - Math.sin(direction + arrowHeadAngle) * arrowHeadLength;\r\n\r\n        // 绘制填充的箭头头部\r\n        graphics.moveTo(arrowEndX, arrowEndY);\r\n        graphics.lineTo(leftX, leftY);\r\n        graphics.lineTo(rightX, rightY);\r\n        graphics.close();\r\n        graphics.fill();\r\n        graphics.stroke();\r\n    }\r\n}"]}