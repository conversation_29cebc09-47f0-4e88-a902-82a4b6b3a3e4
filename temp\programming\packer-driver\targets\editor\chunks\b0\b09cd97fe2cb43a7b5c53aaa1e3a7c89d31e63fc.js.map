{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts"], "names": ["_decorator", "Node", "Color", "Component", "JsonAsset", "Vec3", "Graphics", "input", "Input", "EventMouse", "EDITOR", "PathData", "PathPoint", "PathPointEditor", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "requireComponent", "PathEditor", "type", "displayName", "_graphics", "_pathData", "_pathDataObj", "_selectedPoint", "graphics", "node", "getComponent", "addComponent", "pathData", "value", "reload", "pathName", "name", "onLoad", "setupEditorEvents", "onDestroy", "removeEditorEvents", "on", "EventType", "MOUSE_DOWN", "onMouseDown", "KEY_DOWN", "onKeyDown", "off", "event", "getButton", "BUTTON_RIGHT", "screenPos", "getLocation", "worldPos", "screenToWorld", "x", "y", "localPos", "parent", "inverseTransformPoint", "addNewPoint", "z", "keyCode", "removePoint", "Object", "assign", "json", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "points", "length", "for<PERSON>ach", "point", "addPoint", "updateCurve", "save", "pointEditors", "getComponentsInChildren", "map", "editor", "pathPoint", "JSON", "stringify", "pointNode", "position", "pointEditor", "destroy", "selectPoint", "selected", "showCurve", "drawPath", "clear", "showControlPoints", "strokeColor", "controlLineColor", "lineWidth", "i", "p1", "p2", "moveTo", "lineTo", "stroke", "curveColor", "curvePoints", "generateCurvePoints", "update", "_dt", "WHITE", "GRAY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,U,OAAAA,U;;AAE7EC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;AACVC,MAAAA,e,iBAAAA,e;;;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA,IAA1D;AAAgEC,QAAAA;AAAhE,O,GAAqFnB,U;;4BAU9EoB,U,WALZN,OAAO,CAAC,YAAD,C,UACPI,IAAI,CAAC,aAAD,C,UACJC,gBAAgB,CAACb,QAAD,C,UAChBS,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAUZD,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEjB,SAAR;AAAmBkB,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UASRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAQRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,uFAxCb,MAKaF,UALb,SAKgCjB,SALhC,CAK0C;AAAA;AAAA;AAAA,eAC9BoB,SAD8B,GACD,IADC;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAsC9BC,SAtC8B,GAsCA,IAtCA;AAAA,eAuC9BC,YAvC8B,GAuCL;AAAA;AAAA,qCAvCK;AAAA,eAwC9BC,cAxC8B,GAwCW,IAxCX;AAAA;;AAEnB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKJ,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKK,IAAL,CAAUC,YAAV,CAAuBvB,QAAvB,KAAoC,KAAKsB,IAAL,CAAUE,YAAV,CAAuBxB,QAAvB,CAArD;AACH;;AACD,iBAAO,KAAKiB,SAAZ;AACH;;AAGkB,YAARQ,QAAQ,CAACC,KAAD,EAAmB;AAClC,eAAKR,SAAL,GAAiBQ,KAAjB;AACA,eAAKC,MAAL;AACH;;AACkB,YAARF,QAAQ,GAAqB;AACpC,iBAAO,KAAKP,SAAZ;AACH;;AAGkB,YAARU,QAAQ,GAAW;AAC1B,iBAAO,KAAKT,YAAL,CAAkBU,IAAzB;AACH;;AACkB,YAARD,QAAQ,CAACF,KAAD,EAAgB;AAC/B,eAAKP,YAAL,CAAkBU,IAAlB,GAAyBH,KAAzB;AACH;;AAkBSI,QAAAA,MAAM,GAAG;AACf,cAAI1B,MAAJ,EAAY;AACR,iBAAK2B,iBAAL;AACH;AACJ;;AAESC,QAAAA,SAAS,GAAG;AAClB,cAAI5B,MAAJ,EAAY;AACR,iBAAK6B,kBAAL;AACH;AACJ;;AAEOF,QAAAA,iBAAiB,GAAG;AACxB,cAAI3B,MAAJ,EAAY;AACRH,YAAAA,KAAK,CAACiC,EAAN,CAAShC,KAAK,CAACiC,SAAN,CAAgBC,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACApC,YAAAA,KAAK,CAACiC,EAAN,CAAShC,KAAK,CAACiC,SAAN,CAAgBG,QAAzB,EAAmC,KAAKC,SAAxC,EAAmD,IAAnD;AACH;AACJ;;AAEON,QAAAA,kBAAkB,GAAG;AACzB,cAAI7B,MAAJ,EAAY;AACRH,YAAAA,KAAK,CAACuC,GAAN,CAAUtC,KAAK,CAACiC,SAAN,CAAgBC,UAA1B,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACApC,YAAAA,KAAK,CAACuC,GAAN,CAAUtC,KAAK,CAACiC,SAAN,CAAgBG,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACH;AACJ;;AAEOF,QAAAA,WAAW,CAACI,KAAD,EAAoB;AACnC,cAAI,CAACrC,MAAL,EAAa,OADsB,CAGnC;;AACA,cAAIqC,KAAK,CAACC,SAAN,OAAsBvC,UAAU,CAACwC,YAArC,EAAmD;AAC/C,kBAAMC,SAAS,GAAGH,KAAK,CAACI,WAAN,EAAlB;AACA,kBAAMC,QAAQ,GAAG,KAAKC,aAAL,CAAmB,IAAIhD,IAAJ,CAAS6C,SAAS,CAACI,CAAnB,EAAsBJ,SAAS,CAACK,CAAhC,EAAmC,CAAnC,CAAnB,CAAjB,CAF+C,CAI/C;;AACA,gBAAIC,QAAQ,GAAGJ,QAAf;;AACA,gBAAI,KAAKxB,IAAL,CAAU6B,MAAd,EAAsB;AAClB,mBAAK7B,IAAL,CAAU6B,MAAV,CAAiBC,qBAAjB,CAAuCN,QAAvC,EAAiDI,QAAjD;AACH;;AAED,iBAAKG,WAAL,CAAiBH,QAAQ,CAACF,CAA1B,EAA6BE,QAAQ,CAACD,CAAtC,EAAyCC,QAAQ,CAACI,CAAlD;AACH;AACJ;;AAEOf,QAAAA,SAAS,CAACE,KAAD,EAAa;AAC1B,cAAI,CAACrC,MAAD,IAAW,CAAC,KAAKgB,cAArB,EAAqC,OADX,CAG1B;;AACA,cAAIqB,KAAK,CAACc,OAAN,KAAkB,EAAtB,EAA0B;AAAE;AACxB,iBAAKC,WAAL,CAAiB,KAAKpC,cAAtB;AACH;AACJ;;AAEO2B,QAAAA,aAAa,CAACH,SAAD,EAAwB;AACzC;AACA,iBAAOA,SAAP;AACH;;AAEMjB,QAAAA,MAAM,GAAG;AACZ,cAAI,CAAC,KAAKT,SAAV,EAAqB;AAErB,gBAAMO,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAgC,UAAAA,MAAM,CAACC,MAAP,CAAcjC,QAAd,EAAwB,KAAKP,SAAL,CAAeyC,IAAvC;AACA,eAAKxC,YAAL,GAAoBM,QAApB;AAEA,eAAKH,IAAL,CAAUsC,iBAAV;;AACA,cAAI,KAAKzC,YAAL,IAAqB,KAAKA,YAAL,CAAkB0C,MAAlB,CAAyBC,MAAzB,GAAkC,CAA3D,EAA8D;AAC1D,iBAAK3C,YAAL,CAAkB0C,MAAlB,CAAyBE,OAAzB,CAAkCC,KAAD,IAAW;AACxC,mBAAKC,QAAL,CAAcD,KAAd;AACH,aAFD;AAGH;;AACD,eAAKE,WAAL;AACH;;AAEMC,QAAAA,IAAI,GAAW;AAClB;AACA,gBAAMC,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAKlD,YAAL,CAAkB0C,MAAlB,GAA2BO,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACA,iBAAOC,IAAI,CAACC,SAAL,CAAe,KAAKvD,YAApB,EAAkC,IAAlC,EAAwC,CAAxC,CAAP;AACH;;AAEM8C,QAAAA,QAAQ,CAACD,KAAD,EAAmB;AAC9B,gBAAMW,SAAS,GAAG,IAAIhF,IAAJ,EAAlB;AACAgF,UAAAA,SAAS,CAACxB,MAAV,GAAmB,KAAK7B,IAAxB;AACAqD,UAAAA,SAAS,CAACC,QAAV,GAAqBZ,KAAK,CAACY,QAA3B;AAEA,gBAAMC,WAAW,GAAGF,SAAS,CAACnD,YAAV;AAAA;AAAA,iDAApB;AACAqD,UAAAA,WAAW,CAACL,SAAZ,GAAwBR,KAAxB;AACH;;AAEMX,QAAAA,WAAW,CAACL,CAAD,EAAYC,CAAZ,EAAuBK,CAAS,GAAG,CAAnC,EAAsC;AACpD,gBAAMU,KAAK,GAAG;AAAA;AAAA,sCAAchB,CAAd,EAAiBC,CAAjB,EAAoBK,CAApB,CAAd;AACA,eAAKW,QAAL,CAAcD,KAAd;AACA,eAAKE,WAAL;AACH;;AAEMV,QAAAA,WAAW,CAACqB,WAAD,EAA+B;AAC7C,cAAI,KAAKzD,cAAL,KAAwByD,WAA5B,EAAyC;AACrC,iBAAKzD,cAAL,GAAsB,IAAtB;AACH;;AACDyD,UAAAA,WAAW,CAACvD,IAAZ,CAAiBwD,OAAjB;AACA,eAAKZ,WAAL;AACH;;AAEMa,QAAAA,WAAW,CAACF,WAAD,EAA+B;AAC7C;AACA,cAAI,KAAKzD,cAAL,IAAuB,KAAKA,cAAL,KAAwByD,WAAnD,EAAgE;AAC5D,iBAAKzD,cAAL,CAAoB4D,QAApB,GAA+B,KAA/B;AACH;;AACD,eAAK5D,cAAL,GAAsByD,WAAtB;AACH;;AAEMX,QAAAA,WAAW,GAAG;AACjB,cAAI,CAAC,KAAKe,SAAV,EAAqB,OADJ,CAGjB;;AACA,gBAAMb,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAKlD,YAAL,CAAkB0C,MAAlB,GAA2BO,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AAEA,eAAKU,QAAL;AACH;;AAEOA,QAAAA,QAAQ,GAAG;AACf,gBAAM7D,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAAC8D,KAAT;AAEA,cAAI,KAAKhE,YAAL,CAAkB0C,MAAlB,CAAyBC,MAAzB,GAAkC,CAAtC,EAAyC,OAJ1B,CAMf;;AACA,cAAI,KAAKsB,iBAAT,EAA4B;AACxB/D,YAAAA,QAAQ,CAACgE,WAAT,GAAuB,KAAKC,gBAA5B;AACAjE,YAAAA,QAAQ,CAACkE,SAAT,GAAqB,CAArB;;AAEA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrE,YAAL,CAAkB0C,MAAlB,CAAyBC,MAAzB,GAAkC,CAAtD,EAAyD0B,CAAC,EAA1D,EAA8D;AAC1D,oBAAMC,EAAE,GAAG,KAAKtE,YAAL,CAAkB0C,MAAlB,CAAyB2B,CAAzB,EAA4BZ,QAAvC;AACA,oBAAMc,EAAE,GAAG,KAAKvE,YAAL,CAAkB0C,MAAlB,CAAyB2B,CAAC,GAAG,CAA7B,EAAgCZ,QAA3C;AAEAvD,cAAAA,QAAQ,CAACsE,MAAT,CAAgBF,EAAE,CAACzC,CAAnB,EAAsByC,EAAE,CAACxC,CAAzB;AACA5B,cAAAA,QAAQ,CAACuE,MAAT,CAAgBF,EAAE,CAAC1C,CAAnB,EAAsB0C,EAAE,CAACzC,CAAzB;AACH;;AACD5B,YAAAA,QAAQ,CAACwE,MAAT;AACH,WAnBc,CAqBf;;;AACAxE,UAAAA,QAAQ,CAACgE,WAAT,GAAuB,KAAKS,UAA5B;AACAzE,UAAAA,QAAQ,CAACkE,SAAT,GAAqB,CAArB;;AAEA,gBAAMQ,WAAW,GAAG,KAAK5E,YAAL,CAAkB6E,mBAAlB,EAApB;;AACA,cAAID,WAAW,CAACjC,MAAZ,GAAqB,CAAzB,EAA4B;AACxBzC,YAAAA,QAAQ,CAACsE,MAAT,CAAgBI,WAAW,CAAC,CAAD,CAAX,CAAe/C,CAA/B,EAAkC+C,WAAW,CAAC,CAAD,CAAX,CAAe9C,CAAjD;;AACA,iBAAK,IAAIuC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGO,WAAW,CAACjC,MAAhC,EAAwC0B,CAAC,EAAzC,EAA6C;AACzCnE,cAAAA,QAAQ,CAACuE,MAAT,CAAgBG,WAAW,CAACP,CAAD,CAAX,CAAexC,CAA/B,EAAkC+C,WAAW,CAACP,CAAD,CAAX,CAAevC,CAAjD;AACH;;AACD5B,YAAAA,QAAQ,CAACwE,MAAT;AACH;AACJ;;AAEMI,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB,cAAI9F,MAAJ,EAAY;AACR,iBAAK8E,QAAL;AACH;AACJ;;AA3MqC,O;;;;;iBA2BV,I;;;;;;;iBAGQ,I;;;;;;;iBAGTtF,KAAK,CAACuG,K;;;;;;;iBAGAvG,KAAK,CAACwG,I", "sourcesContent": ["import { _decorator, Node, Color, Component, JsonAsset, Vec3, Graphics, input, Input, EventMouse } from 'cc';\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent } = _decorator;\r\nimport { EDITOR } from 'cc/env';\r\nimport { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { PathPointEditor } from './PathPointEditor';\r\n\r\n@ccclass('PathEditor')\r\n@menu(\"怪物/编辑器/路径编辑\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathEditor extends Component {\r\n    private _graphics: Graphics | null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n        return this._graphics;\r\n    }\r\n\r\n    @property({ type: JsonAsset, displayName: \"路径数据\" })\r\n    public set pathData(value: JsonAsset) {\r\n        this._pathData = value;\r\n        this.reload();\r\n    }\r\n    public get pathData(): JsonAsset | null {\r\n        return this._pathData;\r\n    }\r\n\r\n    @property({ displayName: \"路径名称\" })\r\n    public get pathName(): string {\r\n        return this._pathDataObj.name;\r\n    }\r\n    public set pathName(value: string) {\r\n        this._pathDataObj.name = value;\r\n    }\r\n\r\n    @property({ displayName: \"显示曲线\" })\r\n    public showCurve: boolean = true;\r\n\r\n    @property({ displayName: \"显示控制点\" })\r\n    public showControlPoints: boolean = true;\r\n\r\n    @property({ displayName: \"曲线颜色\" })\r\n    public curveColor: Color = Color.WHITE;\r\n\r\n    @property({ displayName: \"控制线颜色\" })\r\n    public controlLineColor: Color = Color.GRAY;\r\n\r\n    private _pathData: JsonAsset | null = null;\r\n    private _pathDataObj: PathData = new PathData();\r\n    private _selectedPoint: PathPointEditor | null = null;\r\n\r\n    protected onLoad() {\r\n        if (EDITOR) {\r\n            this.setupEditorEvents();\r\n        }\r\n    }\r\n\r\n    protected onDestroy() {\r\n        if (EDITOR) {\r\n            this.removeEditorEvents();\r\n        }\r\n    }\r\n\r\n    private setupEditorEvents() {\r\n        if (EDITOR) {\r\n            input.on(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);\r\n            input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);\r\n        }\r\n    }\r\n\r\n    private removeEditorEvents() {\r\n        if (EDITOR) {\r\n            input.off(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);\r\n            input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);\r\n        }\r\n    }\r\n\r\n    private onMouseDown(event: EventMouse) {\r\n        if (!EDITOR) return;\r\n\r\n        // 右键添加新点\r\n        if (event.getButton() === EventMouse.BUTTON_RIGHT) {\r\n            const screenPos = event.getLocation();\r\n            const worldPos = this.screenToWorld(new Vec3(screenPos.x, screenPos.y, 0));\r\n\r\n            // 转换为本地坐标\r\n            let localPos = worldPos;\r\n            if (this.node.parent) {\r\n                this.node.parent.inverseTransformPoint(worldPos, localPos);\r\n            }\r\n\r\n            this.addNewPoint(localPos.x, localPos.y, localPos.z);\r\n        }\r\n    }\r\n\r\n    private onKeyDown(event: any) {\r\n        if (!EDITOR || !this._selectedPoint) return;\r\n\r\n        // 删除选中的点\r\n        if (event.keyCode === 46) { // Delete key\r\n            this.removePoint(this._selectedPoint);\r\n        }\r\n    }\r\n\r\n    private screenToWorld(screenPos: Vec3): Vec3 {\r\n        // 简化的屏幕到世界坐标转换，实际项目中可能需要更复杂的实现\r\n        return screenPos;\r\n    }\r\n\r\n    public reload() {\r\n        if (!this._pathData) return;\r\n\r\n        const pathData = new PathData();\r\n        Object.assign(pathData, this._pathData.json);\r\n        this._pathDataObj = pathData;\r\n\r\n        this.node.removeAllChildren();\r\n        if (this._pathDataObj && this._pathDataObj.points.length > 0) {\r\n            this._pathDataObj.points.forEach((point) => {\r\n                this.addPoint(point);\r\n            });\r\n        }\r\n        this.updateCurve();\r\n    }\r\n\r\n    public save(): string {\r\n        // 收集所有路径点数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n        return JSON.stringify(this._pathDataObj, null, 2);\r\n    }\r\n\r\n    public addPoint(point: PathPoint) {\r\n        const pointNode = new Node();\r\n        pointNode.parent = this.node;\r\n        pointNode.position = point.position;\r\n\r\n        const pointEditor = pointNode.addComponent(PathPointEditor);\r\n        pointEditor.pathPoint = point;\r\n    }\r\n\r\n    public addNewPoint(x: number, y: number, z: number = 0) {\r\n        const point = new PathPoint(x, y, z);\r\n        this.addPoint(point);\r\n        this.updateCurve();\r\n    }\r\n\r\n    public removePoint(pointEditor: PathPointEditor) {\r\n        if (this._selectedPoint === pointEditor) {\r\n            this._selectedPoint = null;\r\n        }\r\n        pointEditor.node.destroy();\r\n        this.updateCurve();\r\n    }\r\n\r\n    public selectPoint(pointEditor: PathPointEditor) {\r\n        // 取消之前选中的点\r\n        if (this._selectedPoint && this._selectedPoint !== pointEditor) {\r\n            this._selectedPoint.selected = false;\r\n        }\r\n        this._selectedPoint = pointEditor;\r\n    }\r\n\r\n    public updateCurve() {\r\n        if (!this.showCurve) return;\r\n\r\n        // 收集当前所有点的数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n\r\n        this.drawPath();\r\n    }\r\n\r\n    private drawPath() {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n\r\n        if (this._pathDataObj.points.length < 2) return;\r\n\r\n        // 绘制控制线（连接原始控制点）\r\n        if (this.showControlPoints) {\r\n            graphics.strokeColor = this.controlLineColor;\r\n            graphics.lineWidth = 1;\r\n\r\n            for (let i = 0; i < this._pathDataObj.points.length - 1; i++) {\r\n                const p1 = this._pathDataObj.points[i].position;\r\n                const p2 = this._pathDataObj.points[i + 1].position;\r\n\r\n                graphics.moveTo(p1.x, p1.y);\r\n                graphics.lineTo(p2.x, p2.y);\r\n            }\r\n            graphics.stroke();\r\n        }\r\n\r\n        // 绘制Catmull-Rom曲线\r\n        graphics.strokeColor = this.curveColor;\r\n        graphics.lineWidth = 3;\r\n\r\n        const curvePoints = this._pathDataObj.generateCurvePoints();\r\n        if (curvePoints.length > 1) {\r\n            graphics.moveTo(curvePoints[0].x, curvePoints[0].y);\r\n            for (let i = 1; i < curvePoints.length; i++) {\r\n                graphics.lineTo(curvePoints[i].x, curvePoints[i].y);\r\n            }\r\n            graphics.stroke();\r\n        }\r\n    }\r\n\r\n    public update(_dt: number) {\r\n        if (EDITOR) {\r\n            this.drawPath();\r\n        }\r\n    }\r\n}"]}