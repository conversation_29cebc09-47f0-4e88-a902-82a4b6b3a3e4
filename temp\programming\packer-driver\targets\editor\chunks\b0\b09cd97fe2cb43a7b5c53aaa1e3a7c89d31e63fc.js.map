{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts"], "names": ["_decorator", "Node", "Color", "Component", "JsonAsset", "CCInteger", "Graphics", "PathData", "PathPoint", "PathPointEditor", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "requireComponent", "PathEditor", "type", "displayName", "visible", "_pathDataObj", "points", "length", "_graphics", "_showDirectionArrow", "_pathData", "_cachedChildrenCount", "graphics", "node", "getComponent", "addComponent", "pathData", "value", "reload", "pathName", "name", "startIdx", "isClosed", "closed", "Object", "assign", "json", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "point", "addPoint", "updateCurve", "save", "pointEditors", "getComponentsInChildren", "map", "editor", "pathPoint", "JSON", "stringify", "pointNode", "parent", "setPosition", "x", "y", "pointEditor", "addNewPoint", "drawPath", "clear", "strokeColor", "curveColor", "lineWidth", "curvePoints", "generateCurvePoints", "moveTo", "i", "lineTo", "stroke", "drawPathDirectionArrow", "update", "_dt", "childrenCount", "children", "endPoint", "prevPoint", "direction", "Math", "atan2", "<PERSON><PERSON><PERSON><PERSON>", "arrowHeadLength", "arrowHeadAngle", "PI", "RED", "fillColor", "arrowStartX", "arrowStartY", "arrowEndX", "cos", "arrowEndY", "sin", "leftX", "leftY", "rightX", "rightY", "close", "fill", "WHITE"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;;AAG1DC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;AACVC,MAAAA,e,iBAAAA,e;;;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA,IAA1D;AAAgEC,QAAAA;AAAhE,O,GAAqFf,U;;4BAU9EgB,U,WALZN,OAAO,CAAC,YAAD,C,UACPI,IAAI,CAAC,aAAD,C,UACJC,gBAAgB,CAACT,QAAD,C,UAChBK,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAUZD,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEb,SAAR;AAAmBc,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UASRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAQRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEZ,SAAR;AAAmBa,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAQRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE,MAAf;;AAAuBC,QAAAA,OAAO,GAAG;AACvC;AACA,iBAAO,KAAKC,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,IAAmC,CAA1C;AACH;;AAHS,OAAD,C,WAWRV,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,uFAlDb,MAKaF,UALb,SAKgCb,SALhC,CAK0C;AAAA;AAAA;AAAA,eAC9BoB,SAD8B,GACD,IADC;;AAAA;;AAAA,eAgD9BC,mBAhD8B,GAgDC,IAhDD;AAAA,eAiD9BC,SAjD8B,GAiDA,IAjDA;AAAA,eAkD9BL,YAlD8B,GAkDL;AAAA;AAAA,qCAlDK;AAAA,eAmD9BM,oBAnD8B,GAmDC,CAnDD;AAAA;;AAEnB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKJ,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKK,IAAL,CAAUC,YAAV,CAAuBvB,QAAvB,KAAoC,KAAKsB,IAAL,CAAUE,YAAV,CAAuBxB,QAAvB,CAArD;AACH;;AACD,iBAAO,KAAKiB,SAAZ;AACH;;AAGkB,YAARQ,QAAQ,CAACC,KAAD,EAAmB;AAClC,eAAKP,SAAL,GAAiBO,KAAjB;AACA,eAAKC,MAAL;AACH;;AACkB,YAARF,QAAQ,GAAqB;AACpC,iBAAO,KAAKN,SAAZ;AACH;;AAGkB,YAARS,QAAQ,GAAW;AAC1B,iBAAO,KAAKd,YAAL,CAAkBe,IAAzB;AACH;;AACkB,YAARD,QAAQ,CAACF,KAAD,EAAgB;AAC/B,eAAKZ,YAAL,CAAkBe,IAAlB,GAAyBH,KAAzB;AACH;;AAGkB,YAARI,QAAQ,GAAG;AAClB,iBAAO,KAAKhB,YAAL,CAAkBgB,QAAzB;AACH;;AACkB,YAARA,QAAQ,CAACJ,KAAD,EAAgB;AAC/B,eAAKZ,YAAL,CAAkBgB,QAAlB,GAA6BJ,KAA7B;AACH;;AAMkB,YAARK,QAAQ,GAAY;AAC3B,iBAAO,KAAKjB,YAAL,CAAkBkB,MAAzB;AACH;;AACkB,YAARD,QAAQ,CAACL,KAAD,EAAiB;AAChC,eAAKZ,YAAL,CAAkBkB,MAAlB,GAA2BN,KAA3B;AACH;;AAUMC,QAAAA,MAAM,GAAG;AACZ,cAAI,CAAC,KAAKR,SAAV,EAAqB;AAErB,gBAAMM,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAQ,UAAAA,MAAM,CAACC,MAAP,CAAcT,QAAd,EAAwB,KAAKN,SAAL,CAAegB,IAAvC;AACA,eAAKrB,YAAL,GAAoBW,QAApB;AAEA,eAAKH,IAAL,CAAUc,iBAAV;;AACA,cAAI,KAAKtB,YAAL,IAAqB,KAAKA,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,GAAkC,CAA3D,EAA8D;AAC1D,iBAAKF,YAAL,CAAkBC,MAAlB,CAAyBsB,OAAzB,CAAkCC,KAAD,IAAW;AACxC,mBAAKC,QAAL,CAAcD,KAAd;AACH,aAFD;AAGH;;AACD,eAAKE,WAAL;AACH;;AAEMC,QAAAA,IAAI,GAAW;AAClB;AACA,gBAAMC,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAK7B,YAAL,CAAkBC,MAAlB,GAA2B2B,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACA,iBAAOC,IAAI,CAACC,SAAL,CAAe,KAAKlC,YAApB,EAAkC,IAAlC,EAAwC,CAAxC,CAAP;AACH;;AAEMyB,QAAAA,QAAQ,CAACD,KAAD,EAAmB;AAC9B,gBAAMW,SAAS,GAAG,IAAItD,IAAJ,EAAlB;AACAsD,UAAAA,SAAS,CAACC,MAAV,GAAmB,KAAK5B,IAAxB;AACA2B,UAAAA,SAAS,CAACE,WAAV,CAAsBb,KAAK,CAACc,CAA5B,EAA+Bd,KAAK,CAACe,CAArC,EAAwC,CAAxC;AAEA,gBAAMC,WAAW,GAAGL,SAAS,CAACzB,YAAV;AAAA;AAAA,iDAApB;AACA8B,UAAAA,WAAW,CAACR,SAAZ,GAAwBR,KAAxB;AACH;;AAEMiB,QAAAA,WAAW,CAACH,CAAD,EAAYC,CAAZ,EAAuB;AACrC,gBAAMf,KAAK,GAAG;AAAA;AAAA,sCAAcc,CAAd,EAAiBC,CAAjB,CAAd;AACA,eAAKd,QAAL,CAAcD,KAAd;AACA,eAAKE,WAAL;AACH;;AAEMA,QAAAA,WAAW,GAAG;AACjB;AACA,gBAAME,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAK7B,YAAL,CAAkBC,MAAlB,GAA2B2B,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACH;;AAEOU,QAAAA,QAAQ,GAAG;AACf,gBAAMnC,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACoC,KAAT;AAEA,cAAI,KAAK3C,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,GAAkC,CAAtC,EAAyC,OAJ1B,CAMf;;AACAK,UAAAA,QAAQ,CAACqC,WAAT,GAAuB,KAAKC,UAA5B;AACAtC,UAAAA,QAAQ,CAACuC,SAAT,GAAqB,CAArB;;AAEA,gBAAMC,WAAW,GAAG,KAAK/C,YAAL,CAAkBgD,mBAAlB,EAApB;;AACA,cAAID,WAAW,CAAC7C,MAAZ,GAAqB,CAAzB,EAA4B;AACxBK,YAAAA,QAAQ,CAAC0C,MAAT,CAAgBF,WAAW,CAAC,CAAD,CAAX,CAAeT,CAA/B,EAAkCS,WAAW,CAAC,CAAD,CAAX,CAAeR,CAAjD;;AACA,iBAAK,IAAIW,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,WAAW,CAAC7C,MAAhC,EAAwCgD,CAAC,EAAzC,EAA6C;AACzC3C,cAAAA,QAAQ,CAAC4C,MAAT,CAAgBJ,WAAW,CAACG,CAAD,CAAX,CAAeZ,CAA/B,EAAkCS,WAAW,CAACG,CAAD,CAAX,CAAeX,CAAjD;AACH,aAJuB,CAMxB;;;AACA,gBAAI,KAAKvC,YAAL,CAAkBkB,MAAtB,EAA8B;AAC1BX,cAAAA,QAAQ,CAAC4C,MAAT,CAAgBJ,WAAW,CAAC,CAAD,CAAX,CAAeT,CAA/B,EAAkCS,WAAW,CAAC,CAAD,CAAX,CAAeR,CAAjD;AACH;;AAEDhC,YAAAA,QAAQ,CAAC6C,MAAT,GAXwB,CAaxB;;AACA,gBAAI,KAAKhD,mBAAL,IAA4B,CAAC,KAAKJ,YAAL,CAAkBkB,MAAnD,EAA2D;AACvD,mBAAKmC,sBAAL,CAA4B9C,QAA5B,EAAsCwC,WAAtC;AACH;AACJ;AACJ;;AAEMO,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB,gBAAMC,aAAa,GAAG,KAAKhD,IAAL,CAAUiD,QAAV,CAAmBvD,MAAzC;;AACA,cAAIsD,aAAa,KAAK,KAAKlD,oBAA3B,EAAiD;AAC7C,iBAAKA,oBAAL,GAA4BkD,aAA5B;AACH;;AACD,eAAK9B,WAAL;AACA,eAAKgB,QAAL;AACH;AAED;AACJ;AACA;;;AACYW,QAAAA,sBAAsB,CAAC9C,QAAD,EAAqBwC,WAArB,EAAyC;AACnE,cAAIA,WAAW,CAAC7C,MAAZ,GAAqB,CAAzB,EAA4B,OADuC,CAGnE;;AACA,cAAI,KAAKF,YAAL,CAAkBkB,MAAtB,EAA8B,OAJqC,CAMnE;;AACA,gBAAMwC,QAAQ,GAAGX,WAAW,CAACA,WAAW,CAAC7C,MAAZ,GAAqB,CAAtB,CAA5B;AACA,cAAIyD,SAAS,GAAGZ,WAAW,CAACA,WAAW,CAAC7C,MAAZ,GAAqB,CAAtB,CAA3B,CARmE,CAUnE;;AACA,cAAI6C,WAAW,CAAC7C,MAAZ,IAAsB,CAA1B,EAA6B;AACzByD,YAAAA,SAAS,GAAGZ,WAAW,CAACA,WAAW,CAAC7C,MAAZ,GAAqB,CAAtB,CAAvB;AACH,WAbkE,CAenE;;;AACA,gBAAM0D,SAAS,GAAGC,IAAI,CAACC,KAAL,CAAWJ,QAAQ,CAACnB,CAAT,GAAaoB,SAAS,CAACpB,CAAlC,EAAqCmB,QAAQ,CAACpB,CAAT,GAAaqB,SAAS,CAACrB,CAA5D,CAAlB,CAhBmE,CAkBnE;;AACA,gBAAMyB,WAAW,GAAG,EAApB;AACA,gBAAMC,eAAe,GAAG,EAAxB;AACA,gBAAMC,cAAc,GAAGJ,IAAI,CAACK,EAAL,GAAU,CAAjC,CArBmE,CAqB/B;AAEpC;;AACA3D,UAAAA,QAAQ,CAACqC,WAAT,GAAuB9D,KAAK,CAACqF,GAA7B;AACA5D,UAAAA,QAAQ,CAAC6D,SAAT,GAAqBtF,KAAK,CAACqF,GAA3B;AACA5D,UAAAA,QAAQ,CAACuC,SAAT,GAAqB,CAArB,CA1BmE,CA4BnE;;AACA,gBAAMuB,WAAW,GAAGX,QAAQ,CAACpB,CAA7B;AACA,gBAAMgC,WAAW,GAAGZ,QAAQ,CAACnB,CAA7B,CA9BmE,CAgCnE;;AACA,gBAAMgC,SAAS,GAAGF,WAAW,GAAGR,IAAI,CAACW,GAAL,CAASZ,SAAT,IAAsBG,WAAtD;AACA,gBAAMU,SAAS,GAAGH,WAAW,GAAGT,IAAI,CAACa,GAAL,CAASd,SAAT,IAAsBG,WAAtD,CAlCmE,CAoCnE;;AACAxD,UAAAA,QAAQ,CAAC0C,MAAT,CAAgBoB,WAAhB,EAA6BC,WAA7B;AACA/D,UAAAA,QAAQ,CAAC4C,MAAT,CAAgBoB,SAAhB,EAA2BE,SAA3B;AACAlE,UAAAA,QAAQ,CAAC6C,MAAT,GAvCmE,CAyCnE;;AACA,gBAAMuB,KAAK,GAAGJ,SAAS,GAAGV,IAAI,CAACW,GAAL,CAASZ,SAAS,GAAGK,cAArB,IAAuCD,eAAjE;AACA,gBAAMY,KAAK,GAAGH,SAAS,GAAGZ,IAAI,CAACa,GAAL,CAASd,SAAS,GAAGK,cAArB,IAAuCD,eAAjE;AACA,gBAAMa,MAAM,GAAGN,SAAS,GAAGV,IAAI,CAACW,GAAL,CAASZ,SAAS,GAAGK,cAArB,IAAuCD,eAAlE;AACA,gBAAMc,MAAM,GAAGL,SAAS,GAAGZ,IAAI,CAACa,GAAL,CAASd,SAAS,GAAGK,cAArB,IAAuCD,eAAlE,CA7CmE,CA+CnE;;AACAzD,UAAAA,QAAQ,CAAC0C,MAAT,CAAgBsB,SAAhB,EAA2BE,SAA3B;AACAlE,UAAAA,QAAQ,CAAC4C,MAAT,CAAgBwB,KAAhB,EAAuBC,KAAvB;AACArE,UAAAA,QAAQ,CAAC4C,MAAT,CAAgB0B,MAAhB,EAAwBC,MAAxB;AACAvE,UAAAA,QAAQ,CAACwE,KAAT;AACAxE,UAAAA,QAAQ,CAACyE,IAAT;AACAzE,UAAAA,QAAQ,CAAC6C,MAAT;AACH;;AAlMqC,O;;;;;iBA8CXtE,KAAK,CAACmG,K", "sourcesContent": ["import { _decorator, Node, Color, Component, JsonAsset, CCInteger, Graphics } from 'cc';\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent } = _decorator;\r\nimport { EDITOR } from 'cc/env';\r\nimport { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { PathPointEditor } from './PathPointEditor';\r\n\r\n@ccclass('PathEditor')\r\n@menu(\"怪物/编辑器/路径编辑\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathEditor extends Component {\r\n    private _graphics: Graphics | null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n        return this._graphics;\r\n    }\r\n\r\n    @property({ type: JsonAsset, displayName: \"路径数据\" })\r\n    public set pathData(value: JsonAsset) {\r\n        this._pathData = value;\r\n        this.reload();\r\n    }\r\n    public get pathData(): JsonAsset | null {\r\n        return this._pathData;\r\n    }\r\n\r\n    @property({ displayName: \"路径名称\" })\r\n    public get pathName(): string {\r\n        return this._pathDataObj.name;\r\n    }\r\n    public set pathName(value: string) {\r\n        this._pathDataObj.name = value;\r\n    }\r\n\r\n    @property({ type: CCInteger, displayName: '起始点'})\r\n    public get startIdx() {\r\n        return this._pathDataObj.startIdx;\r\n    }\r\n    public set startIdx(value: number) {\r\n        this._pathDataObj.startIdx = value;\r\n    }\r\n\r\n    @property({ displayName: \"是否闭合\", visible() {\r\n        // @ts-ignore\r\n        return this._pathDataObj.points.length >= 3;\r\n    }})\r\n    public get isClosed(): boolean {\r\n        return this._pathDataObj.closed;\r\n    }\r\n    public set isClosed(value: boolean) {\r\n        this._pathDataObj.closed = value;\r\n    }\r\n\r\n    @property({ displayName: \"曲线颜色\" })\r\n    public curveColor: Color = Color.WHITE;\r\n\r\n    private _showDirectionArrow: boolean = true;\r\n    private _pathData: JsonAsset | null = null;\r\n    private _pathDataObj: PathData = new PathData();\r\n    private _cachedChildrenCount: number = 0;\r\n\r\n    public reload() {\r\n        if (!this._pathData) return;\r\n\r\n        const pathData = new PathData();\r\n        Object.assign(pathData, this._pathData.json);\r\n        this._pathDataObj = pathData;\r\n\r\n        this.node.removeAllChildren();\r\n        if (this._pathDataObj && this._pathDataObj.points.length > 0) {\r\n            this._pathDataObj.points.forEach((point) => {\r\n                this.addPoint(point);\r\n            });\r\n        }\r\n        this.updateCurve();\r\n    }\r\n\r\n    public save(): string {\r\n        // 收集所有路径点数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n        return JSON.stringify(this._pathDataObj, null, 2);\r\n    }\r\n\r\n    public addPoint(point: PathPoint) {\r\n        const pointNode = new Node();\r\n        pointNode.parent = this.node;\r\n        pointNode.setPosition(point.x, point.y, 0);\r\n\r\n        const pointEditor = pointNode.addComponent(PathPointEditor);\r\n        pointEditor.pathPoint = point;\r\n    }\r\n\r\n    public addNewPoint(x: number, y: number) {\r\n        const point = new PathPoint(x, y);\r\n        this.addPoint(point);\r\n        this.updateCurve();\r\n    }\r\n\r\n    public updateCurve() {\r\n        // 收集当前所有点的数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n    }\r\n\r\n    private drawPath() {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n\r\n        if (this._pathDataObj.points.length < 2) return;\r\n\r\n        // 绘制Catmull-Rom曲线\r\n        graphics.strokeColor = this.curveColor;\r\n        graphics.lineWidth = 5;\r\n\r\n        const curvePoints = this._pathDataObj.generateCurvePoints();\r\n        if (curvePoints.length > 1) {\r\n            graphics.moveTo(curvePoints[0].x, curvePoints[0].y);\r\n            for (let i = 1; i < curvePoints.length; i++) {\r\n                graphics.lineTo(curvePoints[i].x, curvePoints[i].y);\r\n            }\r\n\r\n            // 如果是闭合路径，连接回起点\r\n            if (this._pathDataObj.closed) {\r\n                graphics.lineTo(curvePoints[0].x, curvePoints[0].y);\r\n            }\r\n\r\n            graphics.stroke();\r\n\r\n            // 绘制路径终点的方向箭头（仅对非闭合路径）\r\n            if (this._showDirectionArrow && !this._pathDataObj.closed) {\r\n                this.drawPathDirectionArrow(graphics, curvePoints);\r\n            }\r\n        }\r\n    }\r\n\r\n    public update(_dt: number) {\r\n        const childrenCount = this.node.children.length;\r\n        if (childrenCount !== this._cachedChildrenCount) {\r\n            this._cachedChildrenCount = childrenCount;\r\n        }\r\n        this.updateCurve();\r\n        this.drawPath();\r\n    }\r\n\r\n    /**\r\n     * 绘制路径方向箭头\r\n     */\r\n    private drawPathDirectionArrow(graphics: Graphics, curvePoints: any[]) {\r\n        if (curvePoints.length < 2) return;\r\n\r\n        // 如果是闭合路径，不绘制箭头（因为没有明确的终点）\r\n        if (this._pathDataObj.closed) return;\r\n\r\n        // 计算终点的方向（使用最后几个点来获得更准确的方向）\r\n        const endPoint = curvePoints[curvePoints.length - 1];\r\n        let prevPoint = curvePoints[curvePoints.length - 2];\r\n\r\n        // 如果有足够的点，使用更远的点来计算方向，获得更平滑的方向\r\n        if (curvePoints.length >= 5) {\r\n            prevPoint = curvePoints[curvePoints.length - 5];\r\n        }\r\n\r\n        // 计算方向角度\r\n        const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);\r\n\r\n        // 箭头参数\r\n        const arrowLength = 40;\r\n        const arrowHeadLength = 20;\r\n        const arrowHeadAngle = Math.PI / 5; // 36度，更尖锐的箭头\r\n\r\n        // 设置箭头样式\r\n        graphics.strokeColor = Color.RED;\r\n        graphics.fillColor = Color.RED;\r\n        graphics.lineWidth = 3;\r\n\r\n        // 计算箭头起点（从路径终点开始）\r\n        const arrowStartX = endPoint.x;\r\n        const arrowStartY = endPoint.y;\r\n\r\n        // 计算箭头终点\r\n        const arrowEndX = arrowStartX + Math.cos(direction) * arrowLength;\r\n        const arrowEndY = arrowStartY + Math.sin(direction) * arrowLength;\r\n\r\n        // 绘制箭头主线\r\n        graphics.moveTo(arrowStartX, arrowStartY);\r\n        graphics.lineTo(arrowEndX, arrowEndY);\r\n        graphics.stroke();\r\n\r\n        // 绘制箭头头部（填充三角形）\r\n        const leftX = arrowEndX - Math.cos(direction - arrowHeadAngle) * arrowHeadLength;\r\n        const leftY = arrowEndY - Math.sin(direction - arrowHeadAngle) * arrowHeadLength;\r\n        const rightX = arrowEndX - Math.cos(direction + arrowHeadAngle) * arrowHeadLength;\r\n        const rightY = arrowEndY - Math.sin(direction + arrowHeadAngle) * arrowHeadLength;\r\n\r\n        // 绘制填充的箭头头部\r\n        graphics.moveTo(arrowEndX, arrowEndY);\r\n        graphics.lineTo(leftX, leftY);\r\n        graphics.lineTo(rightX, rightY);\r\n        graphics.close();\r\n        graphics.fill();\r\n        graphics.stroke();\r\n    }\r\n}"]}