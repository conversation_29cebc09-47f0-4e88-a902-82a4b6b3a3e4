{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts"], "names": ["_decorator", "Component", "Graphics", "ccclass", "playOnFocus", "executeInEditMode", "property", "disallowMultiple", "menu", "requireComponent", "PathEditor", "_graphics", "graphics", "node", "getComponent", "addComponent", "update", "dt", "clear", "lineWidth"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAgCC,MAAAA,S,OAAAA,S;AAAkCC,MAAAA,Q,OAAAA,Q;;;;;;;;;OACrE;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,WAAX;AAAwBC,QAAAA,iBAAxB;AAA2CC,QAAAA,QAA3C;AAAqDC,QAAAA,gBAArD;AAAuEC,QAAAA,IAAvE;AAA6EC,QAAAA;AAA7E,O,GAAmGT,U;;4BAS5FU,U,WALZP,OAAO,CAAC,YAAD,C,UACPK,IAAI,CAAC,aAAD,C,UACJC,gBAAgB,CAACP,QAAD,C,UAChBG,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,4EAJjB,MAKaG,UALb,SAKgCT,SALhC,CAK0C;AAAA;AAAA;AAAA,eAC9BU,SAD8B,GACH,IADG;AAAA;;AAEnB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKD,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKE,IAAL,CAAUC,YAAV,CAAuBZ,QAAvB,KAAoC,KAAKW,IAAL,CAAUE,YAAV,CAAuBb,QAAvB,CAArD;AACH;;AAED,iBAAO,KAAKS,SAAZ;AACH;;AAEMK,QAAAA,MAAM,CAACC,EAAD,EAAa;AACtB,gBAAML,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACM,KAAT;AACAN,UAAAA,QAAQ,CAACO,SAAT,GAAqB,EAArB;AACH;;AAdqC,O", "sourcesContent": ["import { _decorator, instantiate, Color, Component, JsonAsset, Rect, Vec3, Graphics, assetManager } from 'cc';\r\nconst { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, requireComponent  } = _decorator;\r\nimport { EDITOR } from 'cc/env';\r\nimport { FormationGroup, FormationPoint, SpawnGroup } from 'db://assets/bundles/common/script/game/data/WaveData';\r\n\r\n@ccclass('PathEditor')\r\n@menu(\"怪物/编辑器/路径编辑\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathEditor extends Component {\r\n    private _graphics: Graphics|null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n\r\n        return this._graphics;\r\n    }\r\n\r\n    public update(dt: number) {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n        graphics.lineWidth = 10;\r\n    }\r\n}"]}