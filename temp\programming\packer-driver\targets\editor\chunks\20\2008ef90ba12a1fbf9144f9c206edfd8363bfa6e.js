System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec3, Graphics, FormationPoint, SpawnGroup, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _crd, ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, requireComponent, FormationPointEditor;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfFormationPoint(extras) {
    _reporterNs.report("FormationPoint", "db://assets/bundles/common/script/game/data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSpawnGroup(extras) {
    _reporterNs.report("SpawnGroup", "db://assets/bundles/common/script/game/data/WaveData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec3 = _cc.Vec3;
      Graphics = _cc.Graphics;
    }, function (_unresolved_2) {
      FormationPoint = _unresolved_2.FormationPoint;
      SpawnGroup = _unresolved_2.SpawnGroup;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "56d8frq4PhJkqlgz2sjDxmv", "FormationPointEditor", undefined);

      __checkObsolete__(['_decorator', 'instantiate', 'Color', 'Component', 'JsonAsset', 'Rect', 'Vec3', 'Graphics', 'assetManager']);

      ({
        ccclass,
        playOnFocus,
        executeInEditMode,
        property,
        disallowMultiple,
        menu,
        requireComponent
      } = _decorator);

      _export("FormationPointEditor", FormationPointEditor = (_dec = ccclass('FormationPointEditor'), _dec2 = menu("怪物/编辑器/阵型点"), _dec3 = requireComponent(Graphics), _dec4 = executeInEditMode(true), _dec5 = disallowMultiple(true), _dec6 = property({
        type: [_crd && SpawnGroup === void 0 ? (_reportPossibleCrUseOfSpawnGroup({
          error: Error()
        }), SpawnGroup) : SpawnGroup],
        displayName: "出生组"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = _dec5(_class = (_class2 = class FormationPointEditor extends Component {
        constructor(...args) {
          super(...args);
          this._graphics = null;

          _initializerDefineProperty(this, "spawnGroup", _descriptor, this);

          this._cachedIndex = -1;
        }

        get graphics() {
          if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
          }

          return this._graphics;
        }

        get formationPoint() {
          let point = new (_crd && FormationPoint === void 0 ? (_reportPossibleCrUseOfFormationPoint({
            error: Error()
          }), FormationPoint) : FormationPoint)();
          point.spawnGroup = this.spawnGroup;
          point.x = this.node.position.x;
          point.y = this.node.position.y;
          return point;
        }

        set formationPoint(value) {
          this.spawnGroup = value.spawnGroup;
          this.node.position = new Vec3(value.x, value.y, 0);
        }

        update(dt) {
          const graphics = this.graphics;
          graphics.clear();
          graphics.lineWidth = 10;
          graphics.circle(0, 0, 10);
          graphics.stroke();
          const siblingIndex = this.node.getSiblingIndex();

          if (siblingIndex !== this._cachedIndex) {
            this._cachedIndex = siblingIndex;
            this.node.name = `Point_${siblingIndex}`;
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "spawnGroup", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class2)) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2008ef90ba12a1fbf9144f9c206edfd8363bfa6e.js.map