import { _decorator, Vec2, CCFloat, CC<PERSON><PERSON>ger, Enum } from 'cc';
import { eOrientationType } from './WaveData';
const { ccclass, property } = _decorator;

/**
 * 路径点数据
 */
@ccclass("PathPoint")
export class PathPoint {
    @property({ type: CCFloat, displayName: "X坐标" })
    public x: number = 0;

    @property({ type: CCFloat, displayName: "Y坐标" })
    public y: number = 0;

    @property({ type: CCFloat, displayName: "平滑程度", range: [0, 1], slide: true, tooltip: "0=尖锐转角，1=最大平滑" })
    public smoothness: number = 0.5;

    @property({ type: CCInteger, displayName: "速度", tooltip: "飞机在此点的速度" })
    public speed: number = 500;

    @property({ type: Enum(eOrientationType), displayName: "朝向类型", tooltip: "飞机在此点的朝向" })
    public orientationType: eOrientationType = 0;

    @property({ type: CCInteger, displayName: "朝向参数", tooltip: "根据朝向类型不同而不同" })
    public orientationParam: number = 0;

    constructor(x: number = 0, y: number = 0) {
        this.x = x;
        this.y = y;
    }

    public get position(): Vec2 {
        return new Vec2(this.x, this.y);
    }

    public set position(value: Vec2) {
        this.x = value.x;
        this.y = value.y;
    }
}

/**
 * 路径数据
 */
@ccclass("PathData")
export class PathData {
    @property({ displayName: '路径名称', editorOnly: true })
    public name: string = "";

    @property({ type: [PathPoint], displayName: '路径点' })
    public points: PathPoint[] = [];

    @property({ type: CCInteger, displayName: "曲线分段数", tooltip: "每段曲线的细分数量，影响曲线平滑度" })
    public segments: number = 20;

    @property({ displayName: "是否闭合路径", tooltip: "路径是否形成闭环" })
    public closed: boolean = false;

    /**
     * 获取Catmull-Rom曲线上的点
     * @param t 参数值 [0, 1]
     * @param p0 控制点0
     * @param p1 控制点1
     * @param p2 控制点2
     * @param p3 控制点3
     * @param smoothness 平滑程度
     */
    public static catmullRomPoint(t: number, p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number = 0.5): Vec2 {
        const t2 = t * t;
        const t3 = t2 * t;

        // Catmull-Rom基础矩阵，通过smoothness调整张力
        const tension = (1 - smoothness) * 0.5;

        const result = new Vec2();
        result.x = tension * ((-t3 + 2 * t2 - t) * p0.x + (3 * t3 - 5 * t2 + 2) * p1.x + (-3 * t3 + 4 * t2 + t) * p2.x + (t3 - t2) * p3.x);
        result.y = tension * ((-t3 + 2 * t2 - t) * p0.y + (3 * t3 - 5 * t2 + 2) * p1.y + (-3 * t3 + 4 * t2 + t) * p2.y + (t3 - t2) * p3.y);

        return result;
    }

    /**
     * 生成完整的曲线路径点
     */
    public generateCurvePoints(): Vec2[] {
        if (this.points.length < 2) {
            return this.points.map(p => p.position);
        }

        const curvePoints: Vec2[] = [];
        const pointCount = this.points.length;

        for (let i = 0; i < pointCount - 1; i++) {
            const p0 = this.getControlPoint(i - 1);
            const p1 = this.points[i].position;
            const p2 = this.points[i + 1].position;
            const p3 = this.getControlPoint(i + 2);

            // 使用当前段的平滑程度（取两个端点的平均值）
            const smoothness = (this.points[i].smoothness + this.points[i + 1].smoothness) * 0.5;

            for (let j = 0; j < this.segments; j++) {
                const t = j / this.segments;
                const point = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);
                curvePoints.push(point);
            }
        }

        // 添加最后一个点
        curvePoints.push(this.points[pointCount - 1].position);

        return curvePoints;
    }

    /**
     * 获取控制点（处理边界情况）
     */
    private getControlPoint(index: number): Vec2 {
        const pointCount = this.points.length;

        if (this.closed) {
            // 闭合路径，使用循环索引
            const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;
            return this.points[wrappedIndex].position;
        } else {
            // 开放路径，边界处理
            if (index < 0) {
                // 延伸第一个点
                const p0 = this.points[0].position;
                const p1 = this.points[1].position;
                return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));
            } else if (index >= pointCount) {
                // 延伸最后一个点
                const p0 = this.points[pointCount - 2].position;
                const p1 = this.points[pointCount - 1].position;
                return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));
            } else {
                return this.points[index].position;
            }
        }
    }
}