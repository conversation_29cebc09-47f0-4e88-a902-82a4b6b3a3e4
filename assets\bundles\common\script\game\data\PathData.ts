import { _decorator, Vec2, CCFloat, CC<PERSON><PERSON>ger, Enum } from 'cc';
import { eOrientationType } from './WaveData';
const { ccclass, property } = _decorator;

/**
 * 路径点数据
 */
@ccclass("PathPoint")
export class PathPoint {
    @property({ type: CCFloat, displayName: "X坐标" })
    public x: number = 0;

    @property({ type: CCFloat, displayName: "Y坐标" })
    public y: number = 0;

    @property({ type: CCFloat, displayName: "平滑程度", range: [0, 1], slide: true, tooltip: "0=尖锐转角，1=最大平滑" })
    public smoothness: number = 0.5;

    @property({ type: CCInteger, displayName: "速度", tooltip: "飞机在此点的速度" })
    public speed: number = 500;

    @property({ type: Enum(eOrientationType), displayName: "朝向类型", tooltip: "飞机在此点的朝向" })
    public orientationType: eOrientationType = 0;

    @property({ type: CCInteger, displayName: "朝向参数", tooltip: "根据朝向类型不同而不同" })
    public orientationParam: number = 0;

    constructor(x: number = 0, y: number = 0) {
        this.x = x;
        this.y = y;
    }

    public get position(): Vec2 {
        return new Vec2(this.x, this.y);
    }

    public set position(value: Vec2) {
        this.x = value.x;
        this.y = value.y;
    }
}

/**
 * 路径数据
 */
@ccclass("PathData")
export class PathData {
    @property({ displayName: '路径名称', editorOnly: true })
    public name: string = "";

    @property({ type: [PathPoint], displayName: '路径点' })
    public points: PathPoint[] = [];

    @property({ type: CCInteger, displayName: "曲线分段数", tooltip: "每段曲线的细分数量，影响曲线平滑度" })
    public segments: number = 20;

    @property({ displayName: "是否闭合路径", tooltip: "路径是否形成闭环" })
    public closed: boolean = false;

    /**
     * 获取Catmull-Rom曲线上的点
     * @param t 参数值 [0, 1]
     * @param p0 前一个控制点（用于计算切线）
     * @param p1 起始点（曲线经过此点）
     * @param p2 结束点（曲线经过此点）
     * @param p3 后一个控制点（用于计算切线）
     * @param smoothness 平滑程度 [0, 1]，0=尖锐，1=平滑
     */
    public static catmullRomPoint(t: number, p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number = 0.5): Vec2 {
        const t2 = t * t;
        const t3 = t2 * t;

        // 标准Catmull-Rom公式，曲线会经过p1和p2
        // 使用smoothness调整张力，0.5是标准Catmull-Rom
        const alpha = smoothness; // 0 = 尖锐，1 = 平滑

        let result = new Vec2();

        // Catmull-Rom插值公式
        result.x = 0.5 * (
            (2 * p1.x) +
            (-p0.x + p2.x) * t +
            (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 +
            (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3
        );

        result.y = 0.5 * (
            (2 * p1.y) +
            (-p0.y + p2.y) * t +
            (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 +
            (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3
        );

        // 如果smoothness不是0.5，则在标准Catmull-Rom和线性插值之间混合
        if (alpha !== 0.5) {
            const linear = Vec2.lerp(new Vec2(), p1, p2, t);
            result = Vec2.lerp(new Vec2(), linear, result, alpha * 2);
        }

        return result;
    }

    /**
     * 生成完整的曲线路径点
     */
    public generateCurvePoints(): Vec2[] {
        if (this.points.length < 2) {
            return this.points.map(p => p.position);
        }

        const curvePoints: Vec2[] = [];
        const pointCount = this.points.length;

        // 添加第一个点（确保曲线经过起点）
        curvePoints.push(this.points[0].position);

        // 为每一段生成曲线点
        for (let i = 0; i < pointCount - 1; i++) {
            const p0 = this.getControlPoint(i - 1);
            const p1 = this.points[i].position;
            const p2 = this.points[i + 1].position;
            const p3 = this.getControlPoint(i + 2);

            // 使用当前段的平滑程度（取两个端点的平均值）
            const smoothness = (this.points[i].smoothness + this.points[i + 1].smoothness) * 0.5;

            // 生成这一段的曲线点（不包括起点，因为已经添加过了）
            for (let j = 1; j <= this.segments; j++) {
                const t = j / this.segments;
                const point = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);
                curvePoints.push(point);
            }
        }

        return curvePoints;
    }

    /**
     * 获取控制点（处理边界情况）
     */
    private getControlPoint(index: number): Vec2 {
        const pointCount = this.points.length;

        if (this.closed) {
            // 闭合路径，使用循环索引
            const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;
            return this.points[wrappedIndex].position;
        } else {
            // 开放路径，边界处理
            if (index < 0) {
                // 延伸第一个点
                const p0 = this.points[0].position;
                const p1 = this.points[1].position;
                return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));
            } else if (index >= pointCount) {
                // 延伸最后一个点
                const p0 = this.points[pointCount - 2].position;
                const p1 = this.points[pointCount - 1].position;
                return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));
            } else {
                return this.points[index].position;
            }
        }
    }
}