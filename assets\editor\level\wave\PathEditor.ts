import { _decorator, Node, Color, Component, JsonAsset, Vec3, Graphics, input, Input, EventMouse } from 'cc';
const { ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent } = _decorator;
import { EDITOR } from 'cc/env';
import { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';
import { PathPointEditor } from './PathPointEditor';

@ccclass('PathEditor')
@menu("怪物/编辑器/路径编辑")
@requireComponent(Graphics)
@executeInEditMode(true)
@disallowMultiple(true)
export class PathEditor extends Component {
    private _graphics: Graphics | null = null;
    public get graphics(): Graphics {
        if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
        }
        return this._graphics;
    }

    @property({ type: JsonAsset, displayName: "路径数据" })
    public set pathData(value: JsonAsset) {
        this._pathData = value;
        this.reload();
    }
    public get pathData(): JsonAsset | null {
        return this._pathData;
    }

    @property({ displayName: "路径名称" })
    public get pathName(): string {
        return this._pathDataObj.name;
    }
    public set pathName(value: string) {
        this._pathDataObj.name = value;
    }

    @property({ displayName: "是否闭合"})
    public get isClosed(): boolean {
        return this._pathDataObj.closed;
    }
    public set isClosed(value: boolean) {
        this._pathDataObj.closed = value;
    }

    @property({ displayName: "曲线颜色" })
    public curveColor: Color = Color.WHITE;

    @property({ displayName: "控制线颜色" })
    public controlLineColor: Color = Color.GRAY;

    private _pathData: JsonAsset | null = null;
    private _pathDataObj: PathData = new PathData();
    private _cachedChildrenCount: number = 0;

    public reload() {
        if (!this._pathData) return;

        const pathData = new PathData();
        Object.assign(pathData, this._pathData.json);
        this._pathDataObj = pathData;

        this.node.removeAllChildren();
        if (this._pathDataObj && this._pathDataObj.points.length > 0) {
            this._pathDataObj.points.forEach((point) => {
                this.addPoint(point);
            });
        }
        this.updateCurve();
    }

    public save(): string {
        // 收集所有路径点数据
        const pointEditors = this.getComponentsInChildren(PathPointEditor);
        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);
        return JSON.stringify(this._pathDataObj, null, 2);
    }

    public addPoint(point: PathPoint) {
        const pointNode = new Node();
        pointNode.parent = this.node;
        pointNode.setPosition(point.position.x, point.position.y, 0);

        const pointEditor = pointNode.addComponent(PathPointEditor);
        pointEditor.pathPoint = point;
    }

    public addNewPoint(x: number, y: number) {
        const point = new PathPoint(x, y);
        this.addPoint(point);
        this.updateCurve();
    }

    public updateCurve() {
        // 收集当前所有点的数据
        const pointEditors = this.getComponentsInChildren(PathPointEditor);
        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);
    }

    private drawPath() {
        const graphics = this.graphics;
        graphics.clear();

        if (this._pathDataObj.points.length < 2) return;

        // 绘制Catmull-Rom曲线
        graphics.strokeColor = this.curveColor;
        graphics.lineWidth = 5;

        const curvePoints = this._pathDataObj.generateCurvePoints();
        if (curvePoints.length > 1) {
            graphics.moveTo(curvePoints[0].x, curvePoints[0].y);
            for (let i = 1; i < curvePoints.length; i++) {
                graphics.lineTo(curvePoints[i].x, curvePoints[i].y);
            }
            graphics.stroke();
        }
    }

    public update(_dt: number) {
        const childrenCount = this.node.children.length;
        if (childrenCount !== this._cachedChildrenCount) {
            this._cachedChildrenCount = childrenCount;
        }
        this.updateCurve();
        this.drawPath();
    }
}