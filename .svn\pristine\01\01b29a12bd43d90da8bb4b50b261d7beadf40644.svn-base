import { _decorator, Node, Color, Component, JsonAsset, CCInteger, Graphics } from 'cc';
const { ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent } = _decorator;
import { EDITOR } from 'cc/env';
import { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';
import { PathPointEditor } from './PathPointEditor';

@ccclass('PathEditor')
@menu("怪物/编辑器/路径编辑")
@requireComponent(Graphics)
@executeInEditMode(true)
@disallowMultiple(true)
export class PathEditor extends Component {
    private _graphics: Graphics | null = null;
    public get graphics(): Graphics {
        if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
        }
        return this._graphics;
    }

    @property({ type: JsonAsset, displayName: "路径数据" })
    public set pathData(value: JsonAsset) {
        this._pathData = value;
        this.reload();
    }
    public get pathData(): JsonAsset | null {
        return this._pathData;
    }

    @property({ displayName: "路径名称" })
    public get pathName(): string {
        return this._pathDataObj.name;
    }
    public set pathName(value: string) {
        this._pathDataObj.name = value;
    }

    @property({ type: CCInteger, displayName: '起始点'})
    public get startIdx() {
        return this._pathDataObj.startIdx;
    }
    public set startIdx(value: number) {
        this._pathDataObj.startIdx = value;
    }

    @property({ type: CCInteger, displayName: '结束点(-1代表默认最后个点)'})
    public get endIdx() {
        return this._pathDataObj.endIdx;
    }
    public set endIdx(value: number) {
        this._pathDataObj.endIdx = value;
    }

    @property({ displayName: "是否闭合", visible() {
        // @ts-ignore
        return this._pathDataObj.points.length >= 3;
    }})
    public get isClosed(): boolean {
        return this._pathDataObj.closed;
    }
    public set isClosed(value: boolean) {
        this._pathDataObj.closed = value;
    }

    @property({ displayName: "曲线颜色" })
    public curveColor: Color = Color.WHITE;

    private _showDirectionArrow: boolean = true;
    private _pathData: JsonAsset | null = null;
    private _pathDataObj: PathData = new PathData();
    private _cachedChildrenCount: number = 0;

    public reload() {
        if (!this._pathData) return;

        const pathData = new PathData();
        Object.assign(pathData, this._pathData.json);
        this._pathDataObj = pathData;

        this.node.removeAllChildren();
        if (this._pathDataObj && this._pathDataObj.points.length > 0) {
            this._pathDataObj.points.forEach((point) => {
                this.addPoint(point);
            });
        }
        this.updateCurve();
    }

    public save(): string {
        // 收集所有路径点数据
        const pointEditors = this.getComponentsInChildren(PathPointEditor);
        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);
        return JSON.stringify(this._pathDataObj, null, 2);
    }

    public addPoint(point: PathPoint) {
        const pointNode = new Node();
        pointNode.parent = this.node;
        pointNode.setPosition(point.x, point.y, 0);

        const pointEditor = pointNode.addComponent(PathPointEditor);
        pointEditor.pathPoint = point;
    }

    public addNewPoint(x: number, y: number) {
        const point = new PathPoint(x, y);
        this.addPoint(point);
        this.updateCurve();
    }

    public updateCurve() {
        // 收集当前所有点的数据
        const pointEditors = this.getComponentsInChildren(PathPointEditor);
        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);
    }

    private drawPath() {
        const graphics = this.graphics;
        graphics.clear();

        if (this._pathDataObj.points.length < 2) return;

        // 绘制Catmull-Rom曲线
        graphics.strokeColor = this.curveColor;
        graphics.lineWidth = 5;

        const curvePoints = this._pathDataObj.generateCurvePoints();
        if (curvePoints.length > 1) {
            graphics.moveTo(curvePoints[0].x, curvePoints[0].y);
            for (let i = 1; i < curvePoints.length; i++) {
                graphics.lineTo(curvePoints[i].x, curvePoints[i].y);
            }

            // 如果是闭合路径，连接回起点
            if (this._pathDataObj.closed) {
                graphics.lineTo(curvePoints[0].x, curvePoints[0].y);
            }

            graphics.stroke();

            // 绘制路径终点的方向箭头（仅对非闭合路径）
            if (this._showDirectionArrow && !this._pathDataObj.closed) {
                this.drawPathDirectionArrow(graphics, curvePoints);
            }
        }
    }

    public update(_dt: number) {
        const childrenCount = this.node.children.length;
        if (childrenCount !== this._cachedChildrenCount) {
            this._cachedChildrenCount = childrenCount;
        }
        this.updateCurve();
        this.drawPath();
    }

    /**
     * 绘制路径方向箭头
     */
    private drawPathDirectionArrow(graphics: Graphics, curvePoints: any[]) {
        if (curvePoints.length < 2) return;

        // 如果是闭合路径，不绘制箭头（因为没有明确的终点）
        if (this._pathDataObj.closed) return;

        // 计算终点的方向（使用最后几个点来获得更准确的方向）
        const endPoint = curvePoints[curvePoints.length - 1];
        let prevPoint = curvePoints[curvePoints.length - 2];

        // 如果有足够的点，使用更远的点来计算方向，获得更平滑的方向
        if (curvePoints.length >= 5) {
            prevPoint = curvePoints[curvePoints.length - 5];
        }

        // 计算方向角度
        const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);

        // 箭头参数
        const arrowLength = 40;
        const arrowHeadLength = 20;
        const arrowHeadAngle = Math.PI / 5; // 36度，更尖锐的箭头

        // 设置箭头样式
        graphics.strokeColor = Color.RED;
        graphics.fillColor = Color.RED;
        graphics.lineWidth = 3;

        // 计算箭头起点（从路径终点开始）
        const arrowStartX = endPoint.x;
        const arrowStartY = endPoint.y;

        // 计算箭头终点
        const arrowEndX = arrowStartX + Math.cos(direction) * arrowLength;
        const arrowEndY = arrowStartY + Math.sin(direction) * arrowLength;

        // 绘制箭头主线
        graphics.moveTo(arrowStartX, arrowStartY);
        graphics.lineTo(arrowEndX, arrowEndY);
        graphics.stroke();

        // 绘制箭头头部（填充三角形）
        const leftX = arrowEndX - Math.cos(direction - arrowHeadAngle) * arrowHeadLength;
        const leftY = arrowEndY - Math.sin(direction - arrowHeadAngle) * arrowHeadLength;
        const rightX = arrowEndX - Math.cos(direction + arrowHeadAngle) * arrowHeadLength;
        const rightY = arrowEndY - Math.sin(direction + arrowHeadAngle) * arrowHeadLength;

        // 绘制填充的箭头头部
        graphics.moveTo(arrowEndX, arrowEndY);
        graphics.lineTo(leftX, leftY);
        graphics.lineTo(rightX, rightY);
        graphics.close();
        graphics.fill();
        graphics.stroke();
    }
}