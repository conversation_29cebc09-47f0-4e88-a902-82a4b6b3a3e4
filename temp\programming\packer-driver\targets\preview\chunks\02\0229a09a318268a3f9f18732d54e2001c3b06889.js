System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Graphics, Color, CCInteger, Vec2, CCFloat, Enum, EDITOR, PathPoint, eOrientationType, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _class, _class2, _crd, ccclass, property, executeInEditMode, disallowMultiple, menu, requireComponent, PathPointEditor;

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeOrientationType(extras) {
    _reporterNs.report("eOrientationType", "db://assets/bundles/common/script/game/data/WaveData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Graphics = _cc.Graphics;
      Color = _cc.Color;
      CCInteger = _cc.CCInteger;
      Vec2 = _cc.Vec2;
      CCFloat = _cc.CCFloat;
      Enum = _cc.Enum;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      PathPoint = _unresolved_2.PathPoint;
    }, function (_unresolved_3) {
      eOrientationType = _unresolved_3.eOrientationType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "77022mnugRFv4miHgaxYrGl", "PathPointEditor", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Graphics', 'Color', 'CCInteger', 'Vec2', 'CCFloat', 'Enum']);

      ({
        ccclass,
        property,
        executeInEditMode,
        disallowMultiple,
        menu,
        requireComponent
      } = _decorator);

      _export("PathPointEditor", PathPointEditor = (_dec = ccclass('PathPointEditor'), _dec2 = menu("怪物/编辑器/路径点"), _dec3 = requireComponent(Graphics), _dec4 = executeInEditMode(true), _dec5 = disallowMultiple(true), _dec6 = property({
        type: CCFloat,
        displayName: "平滑程度",
        range: [0, 1],
        step: 0.1,
        slide: true,
        tooltip: "0=尖锐转角,1=最大平滑"
      }), _dec7 = property({
        type: CCInteger,
        displayName: "速度",
        tooltip: "飞机在此点的速度"
      }), _dec8 = property({
        type: Enum(_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
          error: Error()
        }), eOrientationType) : eOrientationType),
        displayName: "朝向类型",
        tooltip: "飞机在此点的朝向"
      }), _dec9 = property({
        type: CCInteger,
        displayName: "朝向参数",
        tooltip: "根据朝向类型不同而不同"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = _dec5(_class = (_class2 = class PathPointEditor extends Component {
        constructor() {
          super(...arguments);
          this._graphics = null;
          // @property({ type: CCFloat, displayName: "点大小" })
          this.pointSize = 20;
          this._pathPoint = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)();
          this._cachedIndex = -1;
          this.selected = false;
        }

        get graphics() {
          if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
          }

          return this._graphics;
        }

        get smoothness() {
          return this._pathPoint.smoothness;
        }

        set smoothness(value) {
          this._pathPoint.smoothness = value;
        }

        get speed() {
          return this._pathPoint.speed;
        }

        set speed(value) {
          this._pathPoint.speed = value;
        }

        get orientationType() {
          return this._pathPoint.orientationType;
        }

        set orientationType(value) {
          this._pathPoint.orientationType = value;
        }

        get orientationParam() {
          return this._pathPoint.orientationParam;
        }

        set orientationParam(value) {
          this._pathPoint.orientationParam = value;
        }

        onFocusInEditor() {
          this.selected = true;
        }

        onLostFocusInEditor() {
          this.selected = false;
        }

        get pathPoint() {
          // 同步节点位置到路径点数据
          this._pathPoint.position = new Vec2(this.node.position.x, this.node.position.y);
          return this._pathPoint;
        }

        set pathPoint(value) {
          this._pathPoint = value; // 同步路径点数据到节点位置

          this.node.setPosition(this._pathPoint.position.x, this._pathPoint.position.y, 0);
          this.updateDisplay();
        }

        onLoad() {
          this.updateDisplay();
        }

        updateDisplay() {
          var graphics = this.graphics;
          graphics.clear(); // 绘制点

          var color = this.selected ? Color.YELLOW : Color.WHITE;
          graphics.fillColor = color;
          graphics.strokeColor = Color.BLACK;
          graphics.lineWidth = 5;
          graphics.circle(0, 0, this.pointSize);
          graphics.fill();
          graphics.stroke(); // 绘制平滑程度指示器

          if (this._pathPoint.smoothness > 0) {
            graphics.strokeColor = Color.GREEN;
            graphics.lineWidth = 5;
            var radius = this.pointSize + 5 + this._pathPoint.smoothness * 10;
            graphics.circle(0, 0, radius);
            graphics.stroke();
          } // 绘制速度指示器（箭头）


          if (this._pathPoint.speed > 0) {
            graphics.strokeColor = Color.BLUE;
            graphics.lineWidth = 3;
            var arrowLength = Math.min(this._pathPoint.speed / 10, 100);
            graphics.moveTo(0, 0);
            graphics.lineTo(arrowLength, 0);
            graphics.moveTo(arrowLength - 5, -5);
            graphics.lineTo(arrowLength, 0);
            graphics.lineTo(arrowLength - 5, 5);
            graphics.stroke();
          }
        }

        update(_dt) {
          if (EDITOR) {
            this.updateDisplay();
            var siblingIndex = this.node.getSiblingIndex();

            if (siblingIndex !== this._cachedIndex) {
              this._cachedIndex = siblingIndex;
              this.node.name = "Point_" + siblingIndex;
            }
          }
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "smoothness", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "smoothness"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "speed"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "orientationType", [_dec8], Object.getOwnPropertyDescriptor(_class2.prototype, "orientationType"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "orientationParam", [_dec9], Object.getOwnPropertyDescriptor(_class2.prototype, "orientationParam"), _class2.prototype)), _class2)) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0229a09a318268a3f9f18732d54e2001c3b06889.js.map