14:44:14.020 debug: 2025/9/26 14:44:14
14:44:14.020 debug: Project: E:\M2Game\Client
14:44:14.020 debug: Targets: editor,preview
14:44:14.029 debug: Incremental file seems great.
14:44:14.030 debug: Engine path: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
14:44:14.044 debug: Initializing target [Editor]
14:44:14.044 debug: Loading cache
14:44:14.052 debug: Loading cache costs 8.030699999999797ms.
14:44:14.052 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
14:44:14.053 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
14:44:14.053 debug: Initializing target [Preview]
14:44:14.053 debug: Loading cache
14:44:14.059 debug: Loading cache costs 6.491500000000087ms.
14:44:14.060 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
14:44:14.089 debug: Sync engine features: 2d,3d,affine-transform,animation,audio,base,custom-pipeline,debug-renderer,gfx-webgl,gfx-webgl2,graphics,intersection-2d,marionette,mask,meshopt,particle-2d,physics-2d-builtin,profiler,rich-text,skeletal-animation,spine-3.8,tween,ui,video,websocket,webview,custom-pipeline
14:44:14.094 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "C:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\resources\\resources\\3d\\engine\\editor\\assets"
  },
  {
    "root": "db://i18n/",
    "physical": "E:\\M2Game\\Client\\extensions\\i18n\\assets",
    "jail": "E:\\M2Game\\Client\\extensions\\i18n\\assets"
  },
  {
    "root": "db://assets/",
    "physical": "E:\\M2Game\\Client\\assets"
  }
]
14:44:14.094 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://i18n/": "file:///E:/M2Game/Client/extensions/i18n/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/"
  }
}
14:44:14.095 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://i18n/": "file:///E:/M2Game/Client/extensions/i18n/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/"
  }
}
14:44:14.095 debug: Pulling asset-db.
14:44:14.142 debug: Fetch asset-db cost: 46.14519999999993ms.
14:44:14.142 debug: Build iteration starts.
Number of accumulated asset changes: 271
Feature changed: false
14:44:14.143 debug: Target(editor) build started.
14:44:14.145 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:04 GMT+0800 (中国标准时间)
14:44:14.145 debug: Inspect cce:/internal/x/cc
14:44:14.188 debug: transform url: 'cce:/internal/x/cc' costs: 42.50 ms
14:44:14.188 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
14:44:14.188 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
14:44:14.189 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
14:44:14.190 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 11:28:38 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:04 GMT+0800 (中国标准时间)
14:44:14.190 debug: Inspect cce:/internal/x/prerequisite-imports
14:44:14.241 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 51.40 ms
14:44:14.243 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
14:44:14.243 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
14:44:14.243 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:44:14.244 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:44:14.244 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
14:44:14.245 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
14:44:14.246 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts.
14:44:14.246 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts.
14:44:14.246 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts.
14:44:14.247 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts.
14:44:14.247 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/const/AttributeConst.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/const/AttributeConst.ts.
14:44:14.247 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts.
14:44:14.248 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts.
14:44:14.248 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/bag/Bag.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/bag/Bag.ts.
14:44:14.248 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts.
14:44:14.249 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/base/BaseInfo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/base/BaseInfo.ts.
14:44:14.249 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/base/Role.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/base/Role.ts.
14:44:14.249 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts.
14:44:14.249 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipCombine.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipCombine.ts.
14:44:14.250 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts.
14:44:14.250 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/fight/Rogue.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/fight/Rogue.ts.
14:44:14.250 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/friend/Friend.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/friend/Friend.ts.
14:44:14.250 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/game_level/GameLevel.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/game_level/GameLevel.ts.
14:44:14.251 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/game_logic/GameLogic.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/game_logic/GameLogic.ts.
14:44:14.251 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/game_mode/GameMode.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/game_mode/GameMode.ts.
14:44:14.252 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/gm/GM.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/gm/GM.ts.
14:44:14.252 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts.
14:44:14.252 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts.
14:44:14.252 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts.
14:44:14.253 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneData.ts.
14:44:14.253 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/task/Task.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/task/Task.ts.
14:44:14.253 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts.
14:44:14.253 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts.
14:44:14.254 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts.
14:44:14.254 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/PlaneUIEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/PlaneUIEvent.ts.
14:44:14.254 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/GameIns.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/GameIns.ts.
14:44:14.254 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/GameInsStart.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/GameInsStart.ts.
14:44:14.254 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Bullet.ts.
14:44:14.255 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletPerformanceMonitor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletPerformanceMonitor.ts.
14:44:14.255 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts.
14:44:14.255 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts.
14:44:14.255 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts.
14:44:14.256 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/ObjectPool.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/ObjectPool.ts.
14:44:14.256 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/PropertyContainer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/PropertyContainer.ts.
14:44:14.256 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/BulletEventActions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/BulletEventActions.ts.
14:44:14.256 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts.
14:44:14.257 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/BulletEventConditions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/BulletEventConditions.ts.
14:44:14.257 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/EmitterEventConditions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/EmitterEventConditions.ts.
14:44:14.257 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FBoxCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FBoxCollider.ts.
14:44:14.258 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCircleCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCircleCollider.ts.
14:44:14.258 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCollider.ts.
14:44:14.258 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FColliderManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FColliderManager.ts.
14:44:14.259 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FPolygonCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FPolygonCollider.ts.
14:44:14.259 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/Intersection.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/Intersection.ts.
14:44:14.259 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/QuadTree.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/QuadTree.ts.
14:44:14.259 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameConst.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameConst.ts.
14:44:14.260 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameEnum.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameEnum.ts.
14:44:14.260 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameResourceList.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameResourceList.ts.
14:44:14.260 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BossData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BossData.ts.
14:44:14.261 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BulletEventData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BulletEventData.ts.
14:44:14.261 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyData.ts.
14:44:14.261 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/PathData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/PathData.ts.
14:44:14.262 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/WaveData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/WaveData.ts.
14:44:14.262 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/BulletData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/BulletData.ts.
14:44:14.262 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EmitterData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EmitterData.ts.
14:44:14.263 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventActionType.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventActionType.ts.
14:44:14.263 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventConditionType.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventConditionType.ts.
14:44:14.264 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventGroupData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventGroupData.ts.
14:44:14.264 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/ExpressionValue.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/ExpressionValue.ts.
14:44:14.264 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/EmittierTerrain.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/EmittierTerrain.ts.
14:44:14.264 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/RandTerrain.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/RandTerrain.ts.
14:44:14.265 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/event/GameEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/event/GameEvent.ts.
14:44:14.265 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/Easing.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/Easing.ts.
14:44:14.265 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventAction.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventAction.ts.
14:44:14.265 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventCondition.ts.
14:44:14.265 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventGroup.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventGroup.ts.
14:44:14.266 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventGroupContext.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventGroupContext.ts.
14:44:14.266 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItem.ts.
14:44:14.266 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItemEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItemEvent.ts.
14:44:14.266 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BattleManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BattleManager.ts.
14:44:14.267 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BossManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BossManager.ts.
14:44:14.267 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/EnemyManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/EnemyManager.ts.
14:44:14.267 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameDataManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameDataManager.ts.
14:44:14.267 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GamePlaneManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GamePlaneManager.ts.
14:44:14.268 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameStateManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameStateManager.ts.
14:44:14.268 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts.
14:44:14.268 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/HurtEffectManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/HurtEffectManager.ts.
14:44:14.268 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/MainPlaneManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/MainPlaneManager.ts.
14:44:14.268 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/WaveManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/WaveManager.ts.
14:44:14.269 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/move/CameraMove.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/move/CameraMove.ts.
14:44:14.269 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/move/IMovable.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/move/IMovable.ts.
14:44:14.269 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/move/Movable.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/move/Movable.ts.
14:44:14.269 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/scenes/GameMain.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/scenes/GameMain.ts.
14:44:14.270 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/test/ColliderTest.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/test/ColliderTest.ts.
14:44:14.271 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/BaseComp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/BaseComp.ts.
14:44:14.271 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Controller.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Controller.ts.
14:44:14.271 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Entity.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Entity.ts.
14:44:14.271 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/UIAnimMethods.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/UIAnimMethods.ts.
14:44:14.272 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/BattleLayer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/BattleLayer.ts.
14:44:14.272 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EffectLayer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EffectLayer.ts.
14:44:14.272 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EnemyEffectLayer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EnemyEffectLayer.ts.
14:44:14.273 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/GameInUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/GameInUI.ts.
14:44:14.273 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/GameMapRun.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/GameMapRun.ts.
14:44:14.273 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelBaseUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelBaseUI.ts.
14:44:14.274 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelCondition.ts.
14:44:14.274 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelElemUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelElemUI.ts.
14:44:14.274 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelEventRun.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelEventRun.ts.
14:44:14.275 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelEventUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelEventUI.ts.
14:44:14.275 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayerUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayerUI.ts.
14:44:14.275 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelWaveUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelWaveUI.ts.
14:44:14.275 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBase.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBase.ts.
14:44:14.276 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBaseDebug.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBaseDebug.ts.
14:44:14.276 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/boss/BossPlane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/boss/BossPlane.ts.
14:44:14.277 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane.ts.
14:44:14.277 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase.ts.
14:44:14.277 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBaseDebug.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBaseDebug.ts.
14:44:14.278 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneDebug.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneDebug.ts.
14:44:14.278 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/EventGroupCom.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/EventGroupCom.ts.
14:44:14.278 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/PlaneEventComp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/PlaneEventComp.ts.
14:44:14.278 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/PlaneEventType.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/PlaneEventType.ts.
14:44:14.279 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts.
14:44:14.279 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlaneDebug.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlaneDebug.ts.
14:44:14.279 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlaneStat.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlaneStat.ts.
14:44:14.280 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/Buff.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/Buff.ts.
14:44:14.280 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/BuffComp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/BuffComp.ts.
14:44:14.280 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/ExCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/ExCondition.ts.
14:44:14.281 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/SearchTarget.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/SearchTarget.ts.
14:44:14.281 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/SkillComp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/SkillComp.ts.
14:44:14.281 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts.
14:44:14.281 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Helper.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Helper.ts.
14:44:14.282 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/RPN.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/RPN.ts.
14:44:14.282 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Rand.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Rand.ts.
14:44:14.282 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Tools.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Tools.ts.
14:44:14.282 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/UITools.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/UITools.ts.
14:44:14.283 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts.
14:44:14.283 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventActions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventActions.ts.
14:44:14.283 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventConditions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventConditions.ts.
14:44:14.283 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventGroup.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventGroup.ts.
14:44:14.284 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion.ts.
14:44:14.284 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayDistance.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayDistance.ts.
14:44:14.284 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayTime.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayTime.ts.
14:44:14.285 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave.ts.
14:44:14.285 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/newCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/newCondition.ts.
14:44:14.285 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/leveldata.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/leveldata.ts.
14:44:14.285 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger.ts.
14:44:14.285 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio.ts.
14:44:14.286 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog.ts.
14:44:14.286 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave.ts.
14:44:14.286 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/newTrigger.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/newTrigger.ts.
14:44:14.286 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts.
14:44:14.287 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts.
14:44:14.287 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts.
14:44:14.287 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateDefine.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateDefine.ts.
14:44:14.287 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts.
14:44:14.287 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts.
14:44:14.288 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLoginData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLoginData.ts.
14:44:14.288 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts.
14:44:14.288 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts.
14:44:14.288 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts.
14:44:14.289 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/AnnouncementUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/AnnouncementUI.ts.
14:44:14.289 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/MarqueeUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/MarqueeUI.ts.
14:44:14.289 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/PopupUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/PopupUI.ts.
14:44:14.290 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/RewardUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/RewardUI.ts.
14:44:14.290 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/SettlementResultUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/SettlementResultUI.ts.
14:44:14.290 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/SettlementUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/SettlementUI.ts.
14:44:14.291 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsHurtCell.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsHurtCell.ts.
14:44:14.291 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsScoreCell.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsScoreCell.ts.
14:44:14.292 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsUI.ts.
14:44:14.292 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TextUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TextUI.ts.
14:44:14.292 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/ToastUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/ToastUI.ts.
14:44:14.292 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TopBlockInputUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TopBlockInputUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelect.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelect.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelectItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelectItem.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/base/ItemQuaIcon.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/base/ItemQuaIcon.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/base/StateSprite.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/base/StateSprite.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/ButtonPlus.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/ButtonPlus.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/DragButton.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/DragButton.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/dropdown/DropDown.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/dropdown/DropDown.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/List.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/List.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/ListItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/ListItem.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendAddUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendAddUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendCellUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendCellUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendListUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendListUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendStrangerUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendStrangerUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/DevLoginUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/DevLoginUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/LoadingUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/LoadingUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/RatioScaler.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/RatioScaler.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/GamePauseUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/GamePauseUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/GameReviveUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/GameReviveUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/MBoomUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/MBoomUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/res/PlaneRes.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/res/PlaneRes.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/DialogueUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/DialogueUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueSelectIcon.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueSelectIcon.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailCellUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailCellUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/main/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/main/MainUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryCellUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryCellUI.ts.
14:44:17.539 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKRewardIcon.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKRewardIcon.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneCombineResultUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneCombineResultUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneEquipInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneEquipInfoUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneTypes.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneTypes.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagGrid.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagGrid.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagItem.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/SortTypeDropdown.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/SortTypeDropdown.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/Tabs.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/Tabs.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/CombineDisplay.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/CombineDisplay.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/EquipDisplay.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/EquipDisplay.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuidingUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuidingUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuildingInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuildingInfoUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskTipUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskTipUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/ProgressPanel.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/ProgressPanel.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/TaskItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/TaskItem.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/utils/TTFUtils.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/utils/TTFUtils.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/gm/script/GmEntry.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/gm/script/GmEntry.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts.
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/editor/enum-gen/EmitterEnum.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/enum-gen/EmitterEnum.ts.
14:44:17.540 debug: Detected change: file:///E:/M2Game/Client/assets/editor/enum-gen/EmitterEnum.ts. Last mtime: Fri Sep 26 2025 11:14:07 GMT+0800 (中国标准时间)@363a021f-d497-48c4-a2fe-f8f11b251851, Current mtime: Fri Sep 26 2025 14:44:13 GMT+0800 (中国标准时间)@363a021f-d497-48c4-a2fe-f8f11b251851
14:44:17.540 debug: Inspect file:///E:/M2Game/Client/assets/editor/enum-gen/EmitterEnum.ts
14:44:17.540 debug: transform url: 'file:///E:/M2Game/Client/assets/editor/enum-gen/EmitterEnum.ts' costs: 12.00 ms
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/editor/enum-gen/EnemyEnum.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/enum-gen/EnemyEnum.ts.
14:44:17.540 debug: Detected change: file:///E:/M2Game/Client/assets/editor/enum-gen/EnemyEnum.ts. Last mtime: Fri Sep 26 2025 11:14:07 GMT+0800 (中国标准时间)@d0640d4c-07cc-475a-859c-c45ade49f0cc, Current mtime: Fri Sep 26 2025 14:44:13 GMT+0800 (中国标准时间)@d0640d4c-07cc-475a-859c-c45ade49f0cc
14:44:17.540 debug: Inspect file:///E:/M2Game/Client/assets/editor/enum-gen/EnemyEnum.ts
14:44:17.540 debug: transform url: 'file:///E:/M2Game/Client/assets/editor/enum-gen/EnemyEnum.ts' costs: 12.20 ms
14:44:17.540 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/EmitterGizmo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/EmitterGizmo.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/GizmoDrawer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/GizmoDrawer.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/GizmoManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/GizmoManager.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/GizmoUtils.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/GizmoUtils.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/LevelEventGizmo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/LevelEventGizmo.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/preview/WavePreview.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/preview/WavePreview.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/utils.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/utils.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/wave/FormationEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/wave/FormationEditor.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/wave/FormationPointEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/wave/FormationPointEditor.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/wave/PathEditorExample.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/wave/PathEditorExample.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/wave/PathFollower.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/wave/PathFollower.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/wave/PathPointEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/wave/PathPointEditor.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/editor/planeview/PlaneView.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/planeview/PlaneView.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/resources/i18n/en.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/resources/i18n/en.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/resources/i18n/zh.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/resources/i18n/zh.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/Bundle.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/Bundle.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/MessageBox.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/MessageBox.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/UIMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/UIMgr.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/scripts/resupdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/resupdate/ResUpdate.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/extensions/i18n/assets/LanguageData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/extensions/i18n/assets/LanguageData.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/extensions/i18n/assets/LocalizedLabel.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/extensions/i18n/assets/LocalizedLabel.ts.
14:44:17.542 debug: Resolve file:///E:/M2Game/Client/extensions/i18n/assets/LocalizedSprite.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/extensions/i18n/assets/LocalizedSprite.ts.
14:44:17.542 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
14:44:17.542 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Fri Sep 26 2025 11:14:04 GMT+0800 (中国标准时间), Current mtime: Fri Sep 26 2025 14:44:09 GMT+0800 (中国标准时间)
14:44:17.542 debug: Inspect cce:/internal/code-quality/cr.mjs
14:44:17.542 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 6.60 ms
14:44:17.542 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:44:17.542 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:44:17.542 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:44:17.542 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
14:44:17.542 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:44:17.542 debug: Resolve ./builtin-pipeline-pass from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
14:44:17.542 debug: Resolve ./builtin-pipeline from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
14:44:17.542 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
14:44:17.542 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
14:44:17.543 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:44:17.543 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
14:44:17.543 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
14:44:17.543 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/code-quality/cr.mjs.
14:44:17.543 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/x/cc.
14:44:17.543 debug: Resolve db://assets/bundles/common/script/app/MyApp from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts.
14:44:17.543 debug: Resolve db://assets/bundles/common/script/const/BundleConst from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts.
14:44:17.543 debug: Resolve db://assets/scripts/core/base/Bundle from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/scripts/core/base/Bundle.ts.
14:44:17.543 debug: Resolve db://assets/scripts/core/base/ResManager from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts.
14:44:17.543 debug: Resolve db://assets/scripts/core/base/UIMgr from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/scripts/core/base/UIMgr.ts.
14:44:17.545 debug: Resolve db://assets/scripts/utils/Logger from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts.
14:44:17.545 debug: Resolve ./event/EventManager from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts.
14:44:17.545 debug: Resolve ./event/HomeUIEvent from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts.
14:44:17.545 debug: Resolve ./ui/friend/FriendUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts.
14:44:17.545 debug: Resolve ./ui/gameui/DevLoginUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/DevLoginUI.ts.
14:44:17.545 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
14:44:17.545 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
14:44:17.545 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
14:44:17.545 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
14:44:17.545 debug: Resolve ./ui/mail/MailUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts.
14:44:17.545 debug: Resolve ./ui/pk/PKUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts.
14:44:17.545 debug: Resolve ./ui/plane/PlaneUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts.
14:44:17.545 debug: Resolve ./ui/shop/ShopUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts.
14:44:17.545 debug: Resolve ./ui/skyisland/SkyIslandUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts.
14:44:17.545 debug: Resolve ./ui/story/StoryUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts.
14:44:17.545 debug: Resolve ./ui/talent/TalentUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts.
14:44:17.545 debug: Resolve ./ui/task/TaskTipUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskTipUI.ts.
14:44:17.545 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as cce:/internal/code-quality/cr.mjs.
14:44:17.545 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as cce:/internal/x/cc.
14:44:17.545 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve ../../../../scripts/core/base/ResManager from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts.
14:44:17.546 debug: Resolve ../audio/audioManager from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts.
14:44:17.546 debug: Resolve ../game/manager/GlobalDataManager from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts.
14:44:17.546 debug: Resolve ../luban/LubanMgr from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts.
14:44:17.546 debug: Resolve ../network/NetMgr from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts.
14:44:17.546 debug: Resolve ../plane/PlaneManager from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts.
14:44:17.546 debug: Resolve ../platformsdk/IPlatformSDK from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts.
14:44:17.546 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts as cce:/internal/code-quality/cr.mjs.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve ./IMgr from file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as cce:/internal/code-quality/cr.mjs.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve db://assets/bundles/common/script/app/MyApp from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts.
14:44:17.546 debug: Resolve db://assets/scripts/resupdate/RootPersist from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts.
14:44:17.546 debug: Resolve ../../../../scripts/core/base/IMgr from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts as cce:/internal/code-quality/cr.mjs.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve ../../../../../scripts/core/base/IMgr from file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
14:44:17.546 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as cce:/internal/code-quality/cr.mjs.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc/env from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as external dependency cc/env.
14:44:17.546 debug: Resolve ../../../../scripts/core/base/IMgr from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
14:44:17.546 debug: Resolve ../autogen/luban/schema from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts.
14:44:17.546 debug: Resolve ../const/BundleConst from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as cce:/internal/code-quality/cr.mjs.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve db://assets/scripts/core/base/IMgr from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
14:44:17.546 debug: Resolve db://assets/scripts/utils/Logger from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts.
14:44:17.546 debug: Resolve long from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as file:///E:/M2Game/Client/node_modules/long/index.js.
14:44:17.546 debug: Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'
14:44:17.546 debug: Resolve ../autogen/pb/cs_proto.js from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js.
14:44:17.546 debug: Resolve ../data/DataManager from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts as cce:/internal/x/cc.
14:44:17.546 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js as cce:/internal/code-quality/cr.mjs.
14:44:17.546 debug: Resolve ./cs_proto.js from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js.
14:44:17.546 debug: Detected change: file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js. Last mtime: Fri Sep 26 2025 09:17:25 GMT+0800 (中国标准时间)@3c91fcd0-2c60-4096-a1f6-a93ea5a2458d, Current mtime: Fri Sep 26 2025 09:17:25 GMT+0800 (中国标准时间)
14:44:17.546 debug: Inspect file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js
14:44:17.546 debug: transform url: 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js' costs: 2856.90 ms
14:44:17.546 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js as cce:/internal/ml/cjs-loader.mjs.
14:44:17.546 debug: Detected change: cce:/internal/ml/cjs-loader.mjs. Last mtime: Fri Sep 26 2025 11:14:04 GMT+0800 (中国标准时间), Current mtime: Fri Sep 26 2025 14:44:09 GMT+0800 (中国标准时间)
14:44:17.546 debug: Inspect cce:/internal/ml/cjs-loader.mjs
14:44:17.546 debug: transform url: 'cce:/internal/ml/cjs-loader.mjs' costs: 15.10 ms
14:44:17.546 debug: Resolve ./cs_proto.js from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js.
14:44:17.546 debug: Resolve ./cs_proto.js from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js.
14:44:17.546 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js as cce:/internal/ml/cjs-loader.mjs.
14:44:17.546 debug: Resolve protobufjs/minimal.js from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js as file:///E:/M2Game/Client/node_modules/protobufjs/minimal.js.
14:44:17.546 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/minimal.js as cce:/internal/ml/cjs-loader.mjs.
14:44:17.546 debug: Resolve ./src/index-minimal from file:///E:/M2Game/Client/node_modules/protobufjs/minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js.
14:44:17.546 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as cce:/internal/ml/cjs-loader.mjs.
14:44:17.546 debug: Resolve ./writer from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js.
14:44:17.546 debug: Resolve ./writer_buffer from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/writer_buffer.js.
14:44:17.546 debug: Resolve ./reader from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js.
14:44:17.546 debug: Resolve ./reader_buffer from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/reader_buffer.js.
14:44:17.546 debug: Resolve ./util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
