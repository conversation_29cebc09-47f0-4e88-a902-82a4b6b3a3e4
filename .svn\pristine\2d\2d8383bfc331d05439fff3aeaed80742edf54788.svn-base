/**
 * 阵型编辑器
 */
'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const { updatePropByDump, disconnectGroup } = require('./../../prop');
exports.template = `
<div class="component-container"></div>
<ui-prop>
    <ui-button class="btn-add" style="display: none;">添加阵型点</ui-button>
    <ui-button class="btn-save">保存</ui-button>
</ui-prop>
`;
exports.$ = {
    componentContainer: '.component-container',
    btnAdd: '.btn-add',
    btnSave: '.btn-save',
};
function update(dump) {
    updatePropByDump(this, dump);
    this.dump = dump;
}
async function ready() {
    disconnectGroup(this);
    this.$.btnAdd.addEventListener('confirm', async () => {
        var _a;
        console.log('add formation point', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'addFormationPoint',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid]
        });
    });
    this.$.btnSave.addEventListener('confirm', async () => {
        var _a;
        console.log('save formation', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'saveFormationGroup',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid]
        });
    });
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZm9ybWF0aW9uLWVkaXRvci5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uL3NvdXJjZS9jb250cmlidXRpb25zL2luc3BlY3Rvci9mb3JtYXRpb24tZWRpdG9yLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOztHQUVHO0FBQ0gsWUFBWSxDQUFDOzs7QUFzQmIsd0JBR0M7QUFFRCxzQkFxQkM7QUE5Q0QsTUFBTSxFQUFFLGdCQUFnQixFQUFFLGVBQWUsRUFBRSxHQUFHLE9BQU8sQ0FBQyxjQUFjLENBQUMsQ0FBQztBQUl6RCxRQUFBLFFBQVEsR0FBRzs7Ozs7O0NBTXZCLENBQUM7QUFFVyxRQUFBLENBQUMsR0FBRztJQUNiLGtCQUFrQixFQUFFLHNCQUFzQjtJQUMxQyxNQUFNLEVBQUUsVUFBVTtJQUNsQixPQUFPLEVBQUUsV0FBVztDQUN2QixDQUFDO0FBSUYsU0FBZ0IsTUFBTSxDQUFrQixJQUFTO0lBQzdDLGdCQUFnQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQztJQUM3QixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQztBQUNyQixDQUFDO0FBRU0sS0FBSyxVQUFVLEtBQUs7SUFDdkIsZUFBZSxDQUFDLElBQUksQ0FBQyxDQUFDO0lBQ3RCLElBQUksQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLFNBQVMsRUFBRSxLQUFLLElBQUksRUFBRTs7UUFDakQsT0FBTyxDQUFDLEdBQUcsQ0FBQyxxQkFBcUIsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDOUMsYUFBYTtRQUNiLE1BQU0sQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLE9BQU8sRUFBRSxzQkFBc0IsRUFBRTtZQUNwRCxJQUFJLEVBQUUsY0FBYztZQUNwQixNQUFNLEVBQUUsbUJBQW1CO1lBQzNCLElBQUksRUFBRSxDQUFDLE1BQUEsSUFBSSxDQUFDLElBQUksMENBQUUsS0FBSyxDQUFDLElBQUksQ0FBQztTQUNoQyxDQUFDLENBQUM7SUFDUCxDQUFDLENBQUMsQ0FBQztJQUVILElBQUksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLGdCQUFnQixDQUFDLFNBQVMsRUFBRSxLQUFLLElBQUksRUFBRTs7UUFDbEQsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDekMsYUFBYTtRQUNiLE1BQU0sQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLE9BQU8sRUFBRSxzQkFBc0IsRUFBRTtZQUNwRCxJQUFJLEVBQUUsY0FBYztZQUNwQixNQUFNLEVBQUUsb0JBQW9CO1lBQzVCLElBQUksRUFBRSxDQUFDLE1BQUEsSUFBSSxDQUFDLElBQUksMENBQUUsS0FBSyxDQUFDLElBQUksQ0FBQztTQUNoQyxDQUFDLENBQUM7SUFDUCxDQUFDLENBQUMsQ0FBQztBQUNQLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcclxuICog6Zi15Z6L57yW6L6R5ZmoXHJcbiAqL1xyXG4ndXNlIHN0cmljdCc7XHJcblxyXG5jb25zdCB7IHVwZGF0ZVByb3BCeUR1bXAsIGRpc2Nvbm5lY3RHcm91cCB9ID0gcmVxdWlyZSgnLi8uLi8uLi9wcm9wJyk7XHJcblxyXG50eXBlIFNlbGVjdG9yPCQ+ID0geyAkOiBSZWNvcmQ8a2V5b2YgJCwgYW55IHwgbnVsbD4gfVxyXG5cclxuZXhwb3J0IGNvbnN0IHRlbXBsYXRlID0gYFxyXG48ZGl2IGNsYXNzPVwiY29tcG9uZW50LWNvbnRhaW5lclwiPjwvZGl2PlxyXG48dWktcHJvcD5cclxuICAgIDx1aS1idXR0b24gY2xhc3M9XCJidG4tYWRkXCIgc3R5bGU9XCJkaXNwbGF5OiBub25lO1wiPua3u+WKoOmYteWei+eCuTwvdWktYnV0dG9uPlxyXG4gICAgPHVpLWJ1dHRvbiBjbGFzcz1cImJ0bi1zYXZlXCI+5L+d5a2YPC91aS1idXR0b24+XHJcbjwvdWktcHJvcD5cclxuYDtcclxuXHJcbmV4cG9ydCBjb25zdCAkID0ge1xyXG4gICAgY29tcG9uZW50Q29udGFpbmVyOiAnLmNvbXBvbmVudC1jb250YWluZXInLFxyXG4gICAgYnRuQWRkOiAnLmJ0bi1hZGQnLFxyXG4gICAgYnRuU2F2ZTogJy5idG4tc2F2ZScsXHJcbn07XHJcblxyXG50eXBlIFBhbmVsVGhpcyA9IFNlbGVjdG9yPHR5cGVvZiAkPiAmIHsgZHVtcDogYW55IH07XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXBkYXRlKHRoaXM6IFBhbmVsVGhpcywgZHVtcDogYW55KSB7XHJcbiAgICB1cGRhdGVQcm9wQnlEdW1wKHRoaXMsIGR1bXApO1xyXG4gICAgdGhpcy5kdW1wID0gZHVtcDtcclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJlYWR5KHRoaXM6IFBhbmVsVGhpcykge1xyXG4gICAgZGlzY29ubmVjdEdyb3VwKHRoaXMpO1xyXG4gICAgdGhpcy4kLmJ0bkFkZC5hZGRFdmVudExpc3RlbmVyKCdjb25maXJtJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdhZGQgZm9ybWF0aW9uIHBvaW50JywgdGhpcy5kdW1wKTtcclxuICAgICAgICAvLyBAdHMtaWdub3JlXHJcbiAgICAgICAgRWRpdG9yLk1lc3NhZ2UucmVxdWVzdCgnc2NlbmUnLCAnZXhlY3V0ZS1zY2VuZS1zY3JpcHQnLCB7XHJcbiAgICAgICAgICAgIG5hbWU6ICdsZXZlbC1lZGl0b3InLFxyXG4gICAgICAgICAgICBtZXRob2Q6ICdhZGRGb3JtYXRpb25Qb2ludCcsXHJcbiAgICAgICAgICAgIGFyZ3M6IFt0aGlzLmR1bXA/LnZhbHVlLnV1aWRdXHJcbiAgICAgICAgfSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICB0aGlzLiQuYnRuU2F2ZS5hZGRFdmVudExpc3RlbmVyKCdjb25maXJtJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdzYXZlIGZvcm1hdGlvbicsIHRoaXMuZHVtcCk7XHJcbiAgICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAgIEVkaXRvci5NZXNzYWdlLnJlcXVlc3QoJ3NjZW5lJywgJ2V4ZWN1dGUtc2NlbmUtc2NyaXB0Jywge1xyXG4gICAgICAgICAgICBuYW1lOiAnbGV2ZWwtZWRpdG9yJyxcclxuICAgICAgICAgICAgbWV0aG9kOiAnc2F2ZUZvcm1hdGlvbkdyb3VwJyxcclxuICAgICAgICAgICAgYXJnczogW3RoaXMuZHVtcD8udmFsdWUudXVpZF1cclxuICAgICAgICB9KTtcclxuICAgIH0pO1xyXG59Il19