System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec2, CC<PERSON><PERSON>, CC<PERSON><PERSON><PERSON>, Enum, eOrientationType, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _class4, _class5, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _crd, ccclass, property, PathPoint, PathData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfeOrientationType(extras) {
    _reporterNs.report("eOrientationType", "./WaveData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec2 = _cc.Vec2;
      CCFloat = _cc.CCFloat;
      CCInteger = _cc.CCInteger;
      Enum = _cc.Enum;
    }, function (_unresolved_2) {
      eOrientationType = _unresolved_2.eOrientationType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "762b8DNnsxGiYntrIbD801l", "PathData", undefined);

      __checkObsolete__(['_decorator', 'Vec2', 'CCFloat', 'CCInteger', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 路径点数据
       */

      _export("PathPoint", PathPoint = (_dec = ccclass("PathPoint"), _dec2 = property({
        type: CCFloat,
        displayName: "X坐标"
      }), _dec3 = property({
        type: CCFloat,
        displayName: "Y坐标"
      }), _dec4 = property({
        type: CCFloat,
        displayName: "平滑程度",
        range: [0, 1],
        slide: true,
        tooltip: "0=直线连接, 1=最大平滑曲线"
      }), _dec5 = property({
        type: CCInteger,
        displayName: "速度",
        tooltip: "飞机在此点的速度"
      }), _dec6 = property({
        type: Enum(_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
          error: Error()
        }), eOrientationType) : eOrientationType),
        displayName: "朝向类型",
        tooltip: "飞机在此点的朝向"
      }), _dec7 = property({
        type: CCInteger,
        displayName: "朝向参数",
        tooltip: "根据朝向类型不同而不同"
      }), _dec(_class = (_class2 = class PathPoint {
        constructor(x = 0, y = 0) {
          _initializerDefineProperty(this, "x", _descriptor, this);

          _initializerDefineProperty(this, "y", _descriptor2, this);

          _initializerDefineProperty(this, "smoothness", _descriptor3, this);

          _initializerDefineProperty(this, "speed", _descriptor4, this);

          _initializerDefineProperty(this, "orientationType", _descriptor5, this);

          _initializerDefineProperty(this, "orientationParam", _descriptor6, this);

          this.x = x;
          this.y = y;
        }

        get position() {
          return new Vec2(this.x, this.y);
        }

        set position(value) {
          this.x = value.x;
          this.y = value.y;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "x", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "y", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "smoothness", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 500;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "orientationType", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "orientationParam", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      })), _class2)) || _class));
      /**
       * 路径数据
       */


      _export("PathData", PathData = (_dec8 = ccclass("PathData"), _dec9 = property({
        displayName: '路径名称',
        editorOnly: true
      }), _dec10 = property({
        type: CCInteger,
        displayName: '起始点(默认0)'
      }), _dec11 = property({
        type: CCInteger,
        displayName: '结束点(-1代表使用路径终点)'
      }), _dec12 = property({
        type: [PathPoint],
        displayName: '路径点'
      }), _dec13 = property({
        type: CCInteger,
        displayName: "曲线分段数",
        tooltip: "每段曲线的细分数量，影响曲线平滑度"
      }), _dec14 = property({
        displayName: "是否闭合路径",
        tooltip: "路径是否形成闭环"
      }), _dec8(_class4 = (_class5 = class PathData {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor7, this);

          _initializerDefineProperty(this, "startIdx", _descriptor8, this);

          _initializerDefineProperty(this, "endIdx", _descriptor9, this);

          _initializerDefineProperty(this, "points", _descriptor10, this);

          _initializerDefineProperty(this, "segments", _descriptor11, this);

          _initializerDefineProperty(this, "closed", _descriptor12, this);

          // 缓存的路径数据（不参与序列化）
          this._cachedCurvePoints = null;
        }

        /**
         * 获取Catmull-Rom曲线上的点
         * @param t 参数值 [0, 1]
         * @param p0 前一个控制点（用于计算切线）
         * @param p1 起始点（曲线经过此点）
         * @param p2 结束点（曲线经过此点）
         * @param p3 后一个控制点（用于计算切线）
         * @param smoothness 平滑程度 [0, 1]，0=直线，1=最平滑曲线
         */
        static catmullRomPoint(t, p0, p1, p2, p3, smoothness = 0.5) {
          // 当smoothness为0时，直接返回线性插值（直线）
          if (smoothness === 0) {
            return Vec2.lerp(new Vec2(), p1, p2, t);
          }

          const t2 = t * t;
          const t3 = t2 * t; // 标准Catmull-Rom插值公式

          const catmullRom = new Vec2();
          catmullRom.x = 0.5 * (2 * p1.x + (-p0.x + p2.x) * t + (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 + (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3);
          catmullRom.y = 0.5 * (2 * p1.y + (-p0.y + p2.y) * t + (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 + (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3); // 当smoothness不为1时，在线性插值和Catmull-Rom之间混合

          if (smoothness < 1) {
            const linear = Vec2.lerp(new Vec2(), p1, p2, t);
            return Vec2.lerp(new Vec2(), linear, catmullRom, smoothness);
          }

          return catmullRom;
        }
        /**
         * 生成完整的曲线路径点（带缓存）
         */


        generateCurvePoints(regen = false) {
          // 生成并缓存曲线点
          if (!this._cachedCurvePoints || regen) {
            this._cachedCurvePoints = this.generateCurvePointsInternal();
          }

          return this._cachedCurvePoints;
        }
        /**
         * 内部方法：实际生成曲线点
         */


        generateCurvePointsInternal() {
          if (this.points.length < 2) {
            return this.points.map(p => p.position);
          }

          const curvePoints = [];
          const pointCount = this.points.length; // 添加第一个点（确保曲线经过起点）

          curvePoints.push(this.points[0].position); // 计算需要处理的段数

          const segmentCount = this.closed ? pointCount : pointCount - 1; // 为每一段生成曲线点

          for (let i = 0; i < segmentCount; i++) {
            const p0 = this.getControlPoint(i - 1);
            const p1 = this.points[i].position;
            const p2 = this.getControlPoint(i + 1);
            const p3 = this.getControlPoint(i + 2); // 检查是否有任何一个端点要求直线连接

            const startSmoothness = this.points[i].smoothness;
            const endSmoothness = this.points[(i + 1) % pointCount].smoothness; // 如果任一端点的smoothness为0，则整段使用直线

            if (startSmoothness === 0 || endSmoothness === 0) {
              // 直线连接：只需要添加终点即可（起点已经添加过了）
              curvePoints.push(p2);
            } else {
              // 使用平滑程度的最小值（更保守的方法）
              const smoothness = Math.min(startSmoothness, endSmoothness); // 生成这一段的曲线点（不包括起点，因为已经添加过了）

              for (let j = 1; j <= this.segments; j++) {
                const t = j / this.segments;
                const point = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);
                curvePoints.push(point);
              }
            }
          } // 对于闭合路径，移除可能重复的最后一个点（如果它与第一个点太接近）


          if (this.closed && curvePoints.length > 1) {
            const firstPoint = curvePoints[0];
            const lastPoint = curvePoints[curvePoints.length - 1];
            const distance = Vec2.distance(firstPoint, lastPoint); // 如果最后一个点与第一个点距离很近，移除最后一个点

            if (distance < 0.1) {
              curvePoints.pop();
            }
          }

          return curvePoints;
        }
        /**
         * 获取控制点（处理边界情况）
         */


        getControlPoint(index) {
          const pointCount = this.points.length;

          if (this.closed) {
            // 闭合路径，使用循环索引
            const wrappedIndex = (index % pointCount + pointCount) % pointCount;
            return this.points[wrappedIndex].position;
          } else {
            // 开放路径，边界处理
            if (index < 0) {
              // 延伸第一个点
              const p0 = this.points[0].position;
              const p1 = this.points[1].position;
              return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));
            } else if (index >= pointCount) {
              // 延伸最后一个点
              const p0 = this.points[pointCount - 2].position;
              const p1 = this.points[pointCount - 1].position;
              return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));
            } else {
              return this.points[index].position;
            }
          }
        }
        /**
         * 自定义序列化 - 排除缓存数据
         */


        toJSON() {
          return {
            name: this.name,
            startIdx: this.startIdx,
            endIdx: this.endIdx,
            points: this.points,
            segments: this.segments,
            closed: this.closed
          };
        }
        /**
         * 自定义反序列化 - 清除缓存确保重新计算
         */


        fromJSON(data) {
          this.name = data.name || "";
          this.startIdx = data.startIdx || 0;
          this.endIdx = data.endIdx || -1;
          this.points = data.points || [];
          this.segments = data.segments || 20;
          this.closed = data.closed || false; // 清除缓存，确保使用新数据重新计算

          this._cachedCurvePoints = null;
        }
        /**
         * 静态工厂方法 - 从JSON创建PathData实例
         */


        static fromJSON(data) {
          const pathData = new PathData();
          pathData.fromJSON(data);
          return pathData;
        }

      }, (_descriptor7 = _applyDecoratedDescriptor(_class5.prototype, "name", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "";
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class5.prototype, "startIdx", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class5.prototype, "endIdx", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return -1;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class5.prototype, "points", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class5.prototype, "segments", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 20;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class5.prototype, "closed", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      })), _class5)) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=bb9b33b2273a99d958fbdd91f2e7ce7081eb88a7.js.map