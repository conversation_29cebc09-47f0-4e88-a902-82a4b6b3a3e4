System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec2, CC<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Enum, eOrientationType, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _dec8, _dec9, _dec10, _dec11, _dec12, _class4, _class5, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _crd, ccclass, property, PathPoint, PathData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfeOrientationType(extras) {
    _reporterNs.report("eOrientationType", "./WaveData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec2 = _cc.Vec2;
      CCFloat = _cc.CCFloat;
      CCInteger = _cc.CCInteger;
      Enum = _cc.Enum;
    }, function (_unresolved_2) {
      eOrientationType = _unresolved_2.eOrientationType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "762b8DNnsxGiYntrIbD801l", "PathData", undefined);

      __checkObsolete__(['_decorator', 'Vec2', 'CCFloat', 'CCInteger', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 路径点数据
       */

      _export("PathPoint", PathPoint = (_dec = ccclass("PathPoint"), _dec2 = property({
        type: CCFloat,
        displayName: "X坐标"
      }), _dec3 = property({
        type: CCFloat,
        displayName: "Y坐标"
      }), _dec4 = property({
        type: CCFloat,
        displayName: "平滑程度",
        range: [0, 1],
        slide: true,
        tooltip: "0=尖锐转角，1=最大平滑"
      }), _dec5 = property({
        type: CCInteger,
        displayName: "速度",
        tooltip: "飞机在此点的速度"
      }), _dec6 = property({
        type: Enum(_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
          error: Error()
        }), eOrientationType) : eOrientationType),
        displayName: "朝向类型",
        tooltip: "飞机在此点的朝向"
      }), _dec7 = property({
        type: CCInteger,
        displayName: "朝向参数",
        tooltip: "根据朝向类型不同而不同"
      }), _dec(_class = (_class2 = class PathPoint {
        constructor(x = 0, y = 0) {
          _initializerDefineProperty(this, "x", _descriptor, this);

          _initializerDefineProperty(this, "y", _descriptor2, this);

          _initializerDefineProperty(this, "smoothness", _descriptor3, this);

          _initializerDefineProperty(this, "speed", _descriptor4, this);

          _initializerDefineProperty(this, "orientationType", _descriptor5, this);

          _initializerDefineProperty(this, "orientationParam", _descriptor6, this);

          this.x = x;
          this.y = y;
        }

        get position() {
          return new Vec2(this.x, this.y);
        }

        set position(value) {
          this.x = value.x;
          this.y = value.y;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "x", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "y", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "smoothness", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0.5;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 500;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "orientationType", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "orientationParam", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      })), _class2)) || _class));
      /**
       * 路径数据
       */


      _export("PathData", PathData = (_dec8 = ccclass("PathData"), _dec9 = property({
        displayName: '路径名称',
        editorOnly: true
      }), _dec10 = property({
        type: [PathPoint],
        displayName: '路径点'
      }), _dec11 = property({
        type: CCInteger,
        displayName: "曲线分段数",
        tooltip: "每段曲线的细分数量，影响曲线平滑度"
      }), _dec12 = property({
        displayName: "是否闭合路径",
        tooltip: "路径是否形成闭环"
      }), _dec8(_class4 = (_class5 = class PathData {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor7, this);

          _initializerDefineProperty(this, "points", _descriptor8, this);

          _initializerDefineProperty(this, "segments", _descriptor9, this);

          _initializerDefineProperty(this, "closed", _descriptor10, this);
        }

        /**
         * 获取Catmull-Rom曲线上的点
         * @param t 参数值 [0, 1]
         * @param p0 控制点0
         * @param p1 控制点1
         * @param p2 控制点2
         * @param p3 控制点3
         * @param smoothness 平滑程度
         */
        static catmullRomPoint(t, p0, p1, p2, p3, smoothness = 0.5) {
          const t2 = t * t;
          const t3 = t2 * t; // Catmull-Rom基础矩阵，通过smoothness调整张力

          const tension = (1 - smoothness) * 0.5;
          const result = new Vec2();
          result.x = tension * ((-t3 + 2 * t2 - t) * p0.x + (3 * t3 - 5 * t2 + 2) * p1.x + (-3 * t3 + 4 * t2 + t) * p2.x + (t3 - t2) * p3.x);
          result.y = tension * ((-t3 + 2 * t2 - t) * p0.y + (3 * t3 - 5 * t2 + 2) * p1.y + (-3 * t3 + 4 * t2 + t) * p2.y + (t3 - t2) * p3.y);
          return result;
        }
        /**
         * 生成完整的曲线路径点
         */


        generateCurvePoints() {
          if (this.points.length < 2) {
            return this.points.map(p => p.position);
          }

          const curvePoints = [];
          const pointCount = this.points.length;

          for (let i = 0; i < pointCount - 1; i++) {
            const p0 = this.getControlPoint(i - 1);
            const p1 = this.points[i].position;
            const p2 = this.points[i + 1].position;
            const p3 = this.getControlPoint(i + 2); // 使用当前段的平滑程度（取两个端点的平均值）

            const smoothness = (this.points[i].smoothness + this.points[i + 1].smoothness) * 0.5;

            for (let j = 0; j < this.segments; j++) {
              const t = j / this.segments;
              const point = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);
              curvePoints.push(point);
            }
          } // 添加最后一个点


          curvePoints.push(this.points[pointCount - 1].position);
          return curvePoints;
        }
        /**
         * 获取控制点（处理边界情况）
         */


        getControlPoint(index) {
          const pointCount = this.points.length;

          if (this.closed) {
            // 闭合路径，使用循环索引
            const wrappedIndex = (index % pointCount + pointCount) % pointCount;
            return this.points[wrappedIndex].position;
          } else {
            // 开放路径，边界处理
            if (index < 0) {
              // 延伸第一个点
              const p0 = this.points[0].position;
              const p1 = this.points[1].position;
              return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));
            } else if (index >= pointCount) {
              // 延伸最后一个点
              const p0 = this.points[pointCount - 2].position;
              const p1 = this.points[pointCount - 1].position;
              return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));
            } else {
              return this.points[index].position;
            }
          }
        }

      }, (_descriptor7 = _applyDecoratedDescriptor(_class5.prototype, "name", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "";
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class5.prototype, "points", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class5.prototype, "segments", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 20;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class5.prototype, "closed", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      })), _class5)) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=bb9b33b2273a99d958fbdd91f2e7ce7081eb88a7.js.map