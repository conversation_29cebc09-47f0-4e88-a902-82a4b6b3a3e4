{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/SmoothnessTester.ts"], "names": ["_decorator", "Component", "Vec2", "EDITOR", "PathData", "PathPoint", "ccclass", "executeInEditMode", "menu", "SmoothnessTester", "onLoad", "testSmoothness", "console", "log", "testLinearBehavior", "testSmoothBehavior", "testMixedSmoothness", "points", "for<PERSON>ach", "p", "smoothness", "pathData", "segments", "curvePoints", "generateCurvePoints", "length", "slice", "validateLinearSegments", "testMixedLinearBehavior", "validateSpecificSegment", "Math", "floor", "originalPoints", "tolerance", "isLinear", "i", "startPoint", "position", "endPoint", "segmentStart", "segmentEnd", "min", "j", "curvePoint", "t", "expectedLinearPoint", "lerp", "distance", "warn", "toFixed", "segmentIndex", "description", "testSinglePoint", "p0", "p1", "p2", "p3", "result", "catmullRomPoint", "x", "y", "linearResult", "smoothness0Result"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACvBC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA;AAA9B,O,GAAuCR,U;AAE7C;AACA;AACA;;kCAIaS,gB,WAHZH,OAAO,CAAC,kBAAD,C,UACPE,IAAI,CAAC,eAAD,C,UACJD,iBAAiB,CAAC,IAAD,C,8CAFlB,MAGaE,gBAHb,SAGsCR,SAHtC,CAGgD;AAElCS,QAAAA,MAAM,GAAG;AACf,cAAIP,MAAJ,EAAY;AACR,iBAAKQ,cAAL;AACH;AACJ;;AAEOA,QAAAA,cAAc,GAAG;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EADqB,CAGrB;;AACA,eAAKC,kBAAL,GAJqB,CAMrB;;AACA,eAAKC,kBAAL,GAPqB,CASrB;;AACA,eAAKC,mBAAL;AAEAJ,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ;AACH;;AAEOC,QAAAA,kBAAkB,GAAG;AACzBF,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ;AAEA,gBAAMI,MAAM,GAAG,CACX;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,CADW,EAEX;AAAA;AAAA,sCAAc,CAAd,EAAiB,GAAjB,CAFW,EAGX;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAHW,CAAf,CAHyB,CASzB;;AACAA,UAAAA,MAAM,CAACC,OAAP,CAAeC,CAAC,IAAIA,CAAC,CAACC,UAAF,GAAe,CAAnC;AAEA,gBAAMC,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAA,UAAAA,QAAQ,CAACJ,MAAT,GAAkBA,MAAlB;AACAI,UAAAA,QAAQ,CAACC,QAAT,GAAoB,EAApB;AAEA,gBAAMC,WAAW,GAAGF,QAAQ,CAACG,mBAAT,EAApB;AAEAZ,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyBU,WAAW,CAACE,MAArC;AACAb,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBU,WAAW,CAACG,KAAZ,CAAkB,CAAlB,EAAqB,CAArB,CAArB,EAnByB,CAqBzB;;AACA,eAAKC,sBAAL,CAA4BJ,WAA5B,EAAyCN,MAAzC,EAtByB,CAwBzB;;AACA,eAAKW,uBAAL;AACH;;AAEOA,QAAAA,uBAAuB,GAAG;AAC9BhB,UAAAA,OAAO,CAACC,GAAR,CAAY,mCAAZ;AAEA,gBAAMI,MAAM,GAAG,CACX;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,CADW,EAEX;AAAA;AAAA,sCAAc,CAAd,EAAiB,GAAjB,CAFW,EAGX;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAHW,CAAf,CAH8B,CAS9B;;AACAA,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUG,UAAV,GAAuB,CAAvB;AACAH,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUG,UAAV,GAAuB,CAAvB,CAX8B,CAWH;;AAC3BH,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUG,UAAV,GAAuB,CAAvB;AAEA,gBAAMC,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAA,UAAAA,QAAQ,CAACJ,MAAT,GAAkBA,MAAlB;AACAI,UAAAA,QAAQ,CAACC,QAAT,GAAoB,EAApB;AAEA,gBAAMC,WAAW,GAAGF,QAAQ,CAACG,mBAAT,EAApB;AAEAZ,UAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAsCU,WAAW,CAACE,MAAlD,EApB8B,CAsB9B;;AACA,eAAKI,uBAAL,CAA6BN,WAA7B,EAA0CN,MAA1C,EAAkD,CAAlD,EAAqD,UAArD,EAvB8B,CAyB9B;;AACA,eAAKY,uBAAL,CAA6BN,WAA7B,EAA0CN,MAA1C,EAAkD,CAAlD,EAAqD,UAArD;AACH;;AAEOF,QAAAA,kBAAkB,GAAG;AACzBH,UAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ;AAEA,gBAAMI,MAAM,GAAG,CACX;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,CADW,EAEX;AAAA;AAAA,sCAAc,CAAd,EAAiB,GAAjB,CAFW,EAGX;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAHW,CAAf,CAHyB,CASzB;;AACAA,UAAAA,MAAM,CAACC,OAAP,CAAeC,CAAC,IAAIA,CAAC,CAACC,UAAF,GAAe,CAAnC;AAEA,gBAAMC,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAA,UAAAA,QAAQ,CAACJ,MAAT,GAAkBA,MAAlB;AACAI,UAAAA,QAAQ,CAACC,QAAT,GAAoB,EAApB;AAEA,gBAAMC,WAAW,GAAGF,QAAQ,CAACG,mBAAT,EAApB;AAEAZ,UAAAA,OAAO,CAACC,GAAR,CAAa,OAAMU,WAAW,CAACE,MAAO,OAAtC;AACAb,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBU,WAAW,CAAC,CAAD,CAAhC;AACAX,UAAAA,OAAO,CAACC,GAAR,CAAY,MAAZ,EAAoBU,WAAW,CAACO,IAAI,CAACC,KAAL,CAAWR,WAAW,CAACE,MAAZ,GAAqB,CAAhC,CAAD,CAA/B;AACAb,UAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ,EAAsBU,WAAW,CAACA,WAAW,CAACE,MAAZ,GAAqB,CAAtB,CAAjC;AACH;;AAEOT,QAAAA,mBAAmB,GAAG;AAC1BJ,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ;AAEA,gBAAMI,MAAM,GAAG,CACX;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,CADW,EACe;AAC1B;AAAA;AAAA,sCAAc,CAAC,EAAf,EAAmB,GAAnB,CAFW,EAEe;AAC1B;AAAA;AAAA,sCAAc,EAAd,EAAkB,CAAC,GAAnB,CAHW,EAGe;AAC1B;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAJW,CAIe;AAJf,WAAf;AAOAA,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUG,UAAV,GAAuB,GAAvB;AACAH,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUG,UAAV,GAAuB,CAAvB,CAX0B,CAWE;;AAC5BH,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUG,UAAV,GAAuB,CAAvB,CAZ0B,CAYE;;AAC5BH,UAAAA,MAAM,CAAC,CAAD,CAAN,CAAUG,UAAV,GAAuB,GAAvB;AAEA,gBAAMC,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAA,UAAAA,QAAQ,CAACJ,MAAT,GAAkBA,MAAlB;AACAI,UAAAA,QAAQ,CAACC,QAAT,GAAoB,EAApB;AAEA,gBAAMC,WAAW,GAAGF,QAAQ,CAACG,mBAAT,EAApB;AACAZ,UAAAA,OAAO,CAACC,GAAR,CAAa,cAAaU,WAAW,CAACE,MAAO,KAA7C;AACH;;AAEOE,QAAAA,sBAAsB,CAACJ,WAAD,EAAsBS,cAAtB,EAAmD;AAC7E,gBAAMC,SAAS,GAAG,GAAlB,CAD6E,CACtD;;AACvB,cAAIC,QAAQ,GAAG,IAAf,CAF6E,CAI7E;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,cAAc,CAACP,MAAf,GAAwB,CAA5C,EAA+CU,CAAC,EAAhD,EAAoD;AAChD,kBAAMC,UAAU,GAAGJ,cAAc,CAACG,CAAD,CAAd,CAAkBE,QAArC;AACA,kBAAMC,QAAQ,GAAGN,cAAc,CAACG,CAAC,GAAG,CAAL,CAAd,CAAsBE,QAAvC,CAFgD,CAIhD;;AACA,kBAAME,YAAY,GAAGJ,CAAC,GAAG,EAAzB,CALgD,CAKnB;;AAC7B,kBAAMK,UAAU,GAAGV,IAAI,CAACW,GAAL,CAAS,CAACN,CAAC,GAAG,CAAL,IAAU,EAAnB,EAAuBZ,WAAW,CAACE,MAAZ,GAAqB,CAA5C,CAAnB;;AAEA,iBAAK,IAAIiB,CAAC,GAAGH,YAAb,EAA2BG,CAAC,IAAIF,UAAhC,EAA4CE,CAAC,EAA7C,EAAiD;AAC7C,oBAAMC,UAAU,GAAGpB,WAAW,CAACmB,CAAD,CAA9B;AACA,oBAAME,CAAC,GAAG,CAACF,CAAC,GAAGH,YAAL,KAAsBC,UAAU,GAAGD,YAAnC,CAAV,CAF6C,CAI7C;;AACA,oBAAMM,mBAAmB,GAAG3C,IAAI,CAAC4C,IAAL,CAAU,IAAI5C,IAAJ,EAAV,EAAsBkC,UAAtB,EAAkCE,QAAlC,EAA4CM,CAA5C,CAA5B,CAL6C,CAO7C;;AACA,oBAAMG,QAAQ,GAAG7C,IAAI,CAAC6C,QAAL,CAAcJ,UAAd,EAA0BE,mBAA1B,CAAjB;;AAEA,kBAAIE,QAAQ,GAAGd,SAAf,EAA0B;AACtBrB,gBAAAA,OAAO,CAACoC,IAAR,CAAc,KAAIN,CAAE,aAAYK,QAAQ,CAACE,OAAT,CAAiB,CAAjB,CAAoB,EAApD;AACAf,gBAAAA,QAAQ,GAAG,KAAX;AACH;AACJ;AACJ;;AAED,cAAIA,QAAJ,EAAc;AACVtB,YAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ;AACH,WAFD,MAEO;AACHD,YAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ;AACH;;AAED,iBAAOqB,QAAP;AACH;AAED;AACJ;AACA;;;AACYL,QAAAA,uBAAuB,CAACN,WAAD,EAAsBS,cAAtB,EAAmDkB,YAAnD,EAAyEC,WAAzE,EAA8F;AACzH,gBAAMlB,SAAS,GAAG,GAAlB;AACA,gBAAMM,YAAY,GAAGW,YAAY,GAAG,EAApC,CAFyH,CAEjF;;AACxC,gBAAMV,UAAU,GAAGV,IAAI,CAACW,GAAL,CAAS,CAACS,YAAY,GAAG,CAAhB,IAAqB,EAA9B,EAAkC3B,WAAW,CAACE,MAAZ,GAAqB,CAAvD,CAAnB;;AAEA,cAAIyB,YAAY,IAAIlB,cAAc,CAACP,MAAf,GAAwB,CAA5C,EAA+C;AAC3Cb,YAAAA,OAAO,CAACC,GAAR,CAAa,GAAEsC,WAAY,WAA3B;AACA;AACH;;AAED,gBAAMf,UAAU,GAAGJ,cAAc,CAACkB,YAAD,CAAd,CAA6Bb,QAAhD;AACA,gBAAMC,QAAQ,GAAGN,cAAc,CAACkB,YAAY,GAAG,CAAhB,CAAd,CAAiCb,QAAlD;AAEA,cAAIH,QAAQ,GAAG,IAAf;;AACA,eAAK,IAAIC,CAAC,GAAGI,YAAb,EAA2BJ,CAAC,IAAIK,UAAhC,EAA4CL,CAAC,EAA7C,EAAiD;AAC7C,gBAAIA,CAAC,IAAIZ,WAAW,CAACE,MAArB,EAA6B;AAE7B,kBAAMkB,UAAU,GAAGpB,WAAW,CAACY,CAAD,CAA9B;AACA,kBAAMS,CAAC,GAAG,CAACT,CAAC,GAAGI,YAAL,KAAsBC,UAAU,GAAGD,YAAnC,CAAV;AACA,kBAAMM,mBAAmB,GAAG3C,IAAI,CAAC4C,IAAL,CAAU,IAAI5C,IAAJ,EAAV,EAAsBkC,UAAtB,EAAkCE,QAAlC,EAA4CM,CAA5C,CAA5B;AACA,kBAAMG,QAAQ,GAAG7C,IAAI,CAAC6C,QAAL,CAAcJ,UAAd,EAA0BE,mBAA1B,CAAjB;;AAEA,gBAAIE,QAAQ,GAAGd,SAAf,EAA0B;AACtBrB,cAAAA,OAAO,CAACoC,IAAR,CAAc,GAAEG,WAAY,OAAMhB,CAAE,aAAYY,QAAQ,CAACE,OAAT,CAAiB,CAAjB,CAAoB,EAApE;AACAf,cAAAA,QAAQ,GAAG,KAAX;AACH;AACJ;;AAED,cAAIA,QAAJ,EAAc;AACVtB,YAAAA,OAAO,CAACC,GAAR,CAAa,KAAIsC,WAAY,QAA7B;AACH,WAFD,MAEO;AACHvC,YAAAA,OAAO,CAACC,GAAR,CAAa,KAAIsC,WAAY,QAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACWC,QAAAA,eAAe,GAAG;AACrB,gBAAMC,EAAE,GAAG,IAAInD,IAAJ,CAAS,CAAC,GAAV,EAAe,CAAf,CAAX;AACA,gBAAMoD,EAAE,GAAG,IAAIpD,IAAJ,CAAS,CAAT,EAAY,GAAZ,CAAX;AACA,gBAAMqD,EAAE,GAAG,IAAIrD,IAAJ,CAAS,GAAT,EAAc,CAAd,CAAX;AACA,gBAAMsD,EAAE,GAAG,IAAItD,IAAJ,CAAS,GAAT,EAAc,GAAd,CAAX;AAEAU,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EANqB,CAQrB;;AACA,eAAK,IAAIO,UAAU,GAAG,CAAtB,EAAyBA,UAAU,IAAI,CAAvC,EAA0CA,UAAU,IAAI,IAAxD,EAA8D;AAC1D,kBAAMqC,MAAM,GAAG;AAAA;AAAA,sCAASC,eAAT,CAAyB,GAAzB,EAA8BL,EAA9B,EAAkCC,EAAlC,EAAsCC,EAAtC,EAA0CC,EAA1C,EAA8CpC,UAA9C,CAAf;AACAR,YAAAA,OAAO,CAACC,GAAR,CAAa,cAAaO,UAAW,MAAKqC,MAAM,CAACE,CAAP,CAASV,OAAT,CAAiB,CAAjB,CAAoB,KAAIQ,MAAM,CAACG,CAAP,CAASX,OAAT,CAAiB,CAAjB,CAAoB,GAAtF;AACH,WAZoB,CAcrB;;;AACA,gBAAMY,YAAY,GAAG3D,IAAI,CAAC4C,IAAL,CAAU,IAAI5C,IAAJ,EAAV,EAAsBoD,EAAtB,EAA0BC,EAA1B,EAA8B,GAA9B,CAArB;AACA,gBAAMO,iBAAiB,GAAG;AAAA;AAAA,oCAASJ,eAAT,CAAyB,GAAzB,EAA8BL,EAA9B,EAAkCC,EAAlC,EAAsCC,EAAtC,EAA0CC,EAA1C,EAA8C,CAA9C,CAA1B;AAEA,gBAAMT,QAAQ,GAAG7C,IAAI,CAAC6C,QAAL,CAAcc,YAAd,EAA4BC,iBAA5B,CAAjB;AACAlD,UAAAA,OAAO,CAACC,GAAR,CAAa,YAAWgD,YAAY,CAACF,CAAE,KAAIE,YAAY,CAACD,CAAE,GAA1D;AACAhD,UAAAA,OAAO,CAACC,GAAR,CAAa,oBAAmBiD,iBAAiB,CAACH,CAAE,KAAIG,iBAAiB,CAACF,CAAE,GAA5E;AACAhD,UAAAA,OAAO,CAACC,GAAR,CAAa,QAAOkC,QAAS,EAA7B;;AAEA,cAAIA,QAAQ,GAAG,KAAf,EAAsB;AAClBnC,YAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ;AACH,WAFD,MAEO;AACHD,YAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ;AACH;AACJ;;AA3O2C,O", "sourcesContent": ["import { _decorator, Component, Vec2 } from 'cc';\nimport { EDITOR } from 'cc/env';\nimport { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\n\nconst { ccclass, executeInEditMode, menu } = _decorator;\n\n/**\n * 平滑度测试器 - 验证smoothness=0时是否为直线\n */\n@ccclass('SmoothnessTester')\n@menu(\"怪物/编辑器/平滑度测试器\")\n@executeInEditMode(true)\nexport class SmoothnessTester extends Component {\n\n    protected onLoad() {\n        if (EDITOR) {\n            this.testSmoothness();\n        }\n    }\n\n    private testSmoothness() {\n        console.log(\"=== 平滑度测试开始 ===\");\n        \n        // 测试1: smoothness = 0 应该产生直线\n        this.testLinearBehavior();\n        \n        // 测试2: smoothness = 1 应该产生平滑曲线\n        this.testSmoothBehavior();\n        \n        // 测试3: 混合平滑度测试\n        this.testMixedSmoothness();\n        \n        console.log(\"=== 平滑度测试结束 ===\");\n    }\n\n    private testLinearBehavior() {\n        console.log(\"测试1: smoothness = 0 (直线)\");\n\n        const points = [\n            new PathPoint(-100, 0),\n            new PathPoint(0, 100),\n            new PathPoint(100, 0),\n        ];\n\n        // 设置所有点的平滑度为0\n        points.forEach(p => p.smoothness = 0);\n\n        const pathData = new PathData();\n        pathData.points = points;\n        pathData.segments = 10;\n\n        const curvePoints = pathData.generateCurvePoints();\n\n        console.log(\"生成的曲线点数量:\", curvePoints.length);\n        console.log(\"前几个点:\", curvePoints.slice(0, 5));\n\n        // 验证是否为直线：检查中间点是否在两端点的连线上\n        this.validateLinearSegments(curvePoints, points);\n\n        // 额外测试：混合smoothness情况\n        this.testMixedLinearBehavior();\n    }\n\n    private testMixedLinearBehavior() {\n        console.log(\"测试1.5: 混合smoothness (一个点为0，一个点为1)\");\n\n        const points = [\n            new PathPoint(-100, 0),\n            new PathPoint(0, 100),\n            new PathPoint(100, 0),\n        ];\n\n        // 中间点设置为0，其他点设置为1\n        points[0].smoothness = 1;\n        points[1].smoothness = 0;  // 这个点应该形成尖锐转角\n        points[2].smoothness = 1;\n\n        const pathData = new PathData();\n        pathData.points = points;\n        pathData.segments = 10;\n\n        const curvePoints = pathData.generateCurvePoints();\n\n        console.log(\"混合smoothness测试 - 生成点数:\", curvePoints.length);\n\n        // 验证第一段（应该是直线，因为中间点smoothness=0）\n        this.validateSpecificSegment(curvePoints, points, 0, \"第一段应该是直线\");\n\n        // 验证第二段（应该是直线，因为中间点smoothness=0）\n        this.validateSpecificSegment(curvePoints, points, 1, \"第二段应该是直线\");\n    }\n\n    private testSmoothBehavior() {\n        console.log(\"测试2: smoothness = 1 (最平滑)\");\n        \n        const points = [\n            new PathPoint(-100, 0),\n            new PathPoint(0, 100),\n            new PathPoint(100, 0),\n        ];\n        \n        // 设置所有点的平滑度为1\n        points.forEach(p => p.smoothness = 1);\n        \n        const pathData = new PathData();\n        pathData.points = points;\n        pathData.segments = 10;\n        \n        const curvePoints = pathData.generateCurvePoints();\n        \n        console.log(`生成了 ${curvePoints.length} 个曲线点`);\n        console.log(\"第一个点:\", curvePoints[0]);\n        console.log(\"中间点:\", curvePoints[Math.floor(curvePoints.length / 2)]);\n        console.log(\"最后一个点:\", curvePoints[curvePoints.length - 1]);\n    }\n\n    private testMixedSmoothness() {\n        console.log(\"测试3: 混合平滑度\");\n        \n        const points = [\n            new PathPoint(-150, 0),   // smoothness = 0.5\n            new PathPoint(-50, 100),  // smoothness = 0 (直线)\n            new PathPoint(50, -100),  // smoothness = 0 (直线)\n            new PathPoint(150, 0),    // smoothness = 0.5\n        ];\n        \n        points[0].smoothness = 0.5;\n        points[1].smoothness = 0;   // 这个点应该形成直线连接\n        points[2].smoothness = 0;   // 这个点应该形成直线连接\n        points[3].smoothness = 0.5;\n        \n        const pathData = new PathData();\n        pathData.points = points;\n        pathData.segments = 20;\n        \n        const curvePoints = pathData.generateCurvePoints();\n        console.log(`混合平滑度测试生成了 ${curvePoints.length} 个点`);\n    }\n\n    private validateLinearSegments(curvePoints: Vec2[], originalPoints: PathPoint[]) {\n        const tolerance = 0.1; // 允许的误差\n        let isLinear = true;\n        \n        // 检查每一段是否为直线\n        for (let i = 0; i < originalPoints.length - 1; i++) {\n            const startPoint = originalPoints[i].position;\n            const endPoint = originalPoints[i + 1].position;\n            \n            // 找到这一段对应的曲线点\n            const segmentStart = i * 10; // 假设每段10个点\n            const segmentEnd = Math.min((i + 1) * 10, curvePoints.length - 1);\n            \n            for (let j = segmentStart; j <= segmentEnd; j++) {\n                const curvePoint = curvePoints[j];\n                const t = (j - segmentStart) / (segmentEnd - segmentStart);\n                \n                // 计算理论上的直线点\n                const expectedLinearPoint = Vec2.lerp(new Vec2(), startPoint, endPoint, t);\n                \n                // 检查距离\n                const distance = Vec2.distance(curvePoint, expectedLinearPoint);\n                \n                if (distance > tolerance) {\n                    console.warn(`点 ${j} 偏离直线，距离: ${distance.toFixed(3)}`);\n                    isLinear = false;\n                }\n            }\n        }\n        \n        if (isLinear) {\n            console.log(\"✅ 验证通过：smoothness=0时确实产生直线\");\n        } else {\n            console.log(\"❌ 验证失败：smoothness=0时没有产生直线\");\n        }\n        \n        return isLinear;\n    }\n\n    /**\n     * 验证特定段是否为直线\n     */\n    private validateSpecificSegment(curvePoints: Vec2[], originalPoints: PathPoint[], segmentIndex: number, description: string) {\n        const tolerance = 0.1;\n        const segmentStart = segmentIndex * 10; // 假设每段10个点\n        const segmentEnd = Math.min((segmentIndex + 1) * 10, curvePoints.length - 1);\n\n        if (segmentIndex >= originalPoints.length - 1) {\n            console.log(`${description}: 段索引超出范围`);\n            return;\n        }\n\n        const startPoint = originalPoints[segmentIndex].position;\n        const endPoint = originalPoints[segmentIndex + 1].position;\n\n        let isLinear = true;\n        for (let i = segmentStart; i <= segmentEnd; i++) {\n            if (i >= curvePoints.length) break;\n\n            const curvePoint = curvePoints[i];\n            const t = (i - segmentStart) / (segmentEnd - segmentStart);\n            const expectedLinearPoint = Vec2.lerp(new Vec2(), startPoint, endPoint, t);\n            const distance = Vec2.distance(curvePoint, expectedLinearPoint);\n\n            if (distance > tolerance) {\n                console.warn(`${description}: 点 ${i} 偏离直线，距离: ${distance.toFixed(3)}`);\n                isLinear = false;\n            }\n        }\n\n        if (isLinear) {\n            console.log(`✅ ${description}: 验证通过`);\n        } else {\n            console.log(`❌ ${description}: 验证失败`);\n        }\n    }\n\n    /**\n     * 手动测试单个插值点\n     */\n    public testSinglePoint() {\n        const p0 = new Vec2(-100, 0);\n        const p1 = new Vec2(0, 100);\n        const p2 = new Vec2(100, 0);\n        const p3 = new Vec2(200, 100);\n        \n        console.log(\"=== 单点插值测试 ===\");\n        \n        // 测试t=0.5时的不同smoothness值\n        for (let smoothness = 0; smoothness <= 1; smoothness += 0.25) {\n            const result = PathData.catmullRomPoint(0.5, p0, p1, p2, p3, smoothness);\n            console.log(`smoothness=${smoothness}: (${result.x.toFixed(2)}, ${result.y.toFixed(2)})`);\n        }\n        \n        // 验证smoothness=0时是否为线性插值\n        const linearResult = Vec2.lerp(new Vec2(), p1, p2, 0.5);\n        const smoothness0Result = PathData.catmullRomPoint(0.5, p0, p1, p2, p3, 0);\n        \n        const distance = Vec2.distance(linearResult, smoothness0Result);\n        console.log(`线性插值结果: (${linearResult.x}, ${linearResult.y})`);\n        console.log(`smoothness=0结果: (${smoothness0Result.x}, ${smoothness0Result.y})`);\n        console.log(`距离差: ${distance}`);\n        \n        if (distance < 0.001) {\n            console.log(\"✅ smoothness=0确实等于线性插值\");\n        } else {\n            console.log(\"❌ smoothness=0不等于线性插值\");\n        }\n    }\n}\n"]}