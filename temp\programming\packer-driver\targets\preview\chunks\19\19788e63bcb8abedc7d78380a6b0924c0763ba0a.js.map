{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathSegmentOptimizationTest.ts"], "names": ["_decorator", "Component", "Vec2", "EDITOR", "PathData", "PathPoint", "ccclass", "executeInEditMode", "menu", "PathSegmentOptimizationTest", "onLoad", "runOptimizationTests", "console", "log", "testLinearSegmentOptimization", "testMixedSegmentOptimization", "testCurveSegmentNormal", "pathData", "segments", "points", "for<PERSON>ach", "p", "smoothness", "curvePoints", "generateCurvePoints", "length", "expectedPoints", "validateLinearPoints", "expectedLinearPoints", "expectedCurvePoints", "expectedTotal", "originalPoints", "i", "originalPos", "position", "curvePos", "distance", "x", "toFixed", "y", "performanceTest", "point", "Math", "sin", "push", "startTime", "performance", "now", "endTime", "unoptimizedPoints", "optimizedPoints", "reduction"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACvBC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA;AAA9B,O,GAAuCR,U;AAE7C;AACA;AACA;;6CAIaS,2B,WAHZH,OAAO,CAAC,6BAAD,C,UACPE,IAAI,CAAC,gBAAD,C,UACJD,iBAAiB,CAAC,IAAD,C,8CAFlB,MAGaE,2BAHb,SAGiDR,SAHjD,CAG2D;AAE7CS,QAAAA,MAAM,GAAG;AACf,cAAIP,MAAJ,EAAY;AACR,iBAAKQ,oBAAL;AACH;AACJ;;AAEOA,QAAAA,oBAAoB,GAAG;AAC3BC,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AAEA,eAAKC,6BAAL;AACA,eAAKC,4BAAL;AACA,eAAKC,sBAAL;AAEAJ,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,6BAA6B,GAAG;AACpCF,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AAEA,cAAMI,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAA,UAAAA,QAAQ,CAACC,QAAT,GAAoB,EAApB,CAJoC,CAIZ;AAExB;;AACAD,UAAAA,QAAQ,CAACE,MAAT,GAAkB,CACd;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,CADc,EAEd;AAAA;AAAA,sCAAc,CAAd,EAAiB,GAAjB,CAFc,EAGd;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAHc,CAAlB;AAKAF,UAAAA,QAAQ,CAACE,MAAT,CAAgBC,OAAhB,CAAwBC,CAAC,IAAIA,CAAC,CAACC,UAAF,GAAe,CAA5C;AAEA,cAAMC,WAAW,GAAGN,QAAQ,CAACO,mBAAT,EAApB;AAEAZ,UAAAA,OAAO,CAACC,GAAR,4BAA2BI,QAAQ,CAACC,QAApC;AACAN,UAAAA,OAAO,CAACC,GAAR,sCAAsBU,WAAW,CAACE,MAAlC;AACAb,UAAAA,OAAO,CAACC,GAAR,4CAAuBI,QAAQ,CAACE,MAAT,CAAgBM,MAAvC,iDAlBoC,CAoBpC;;AACA,cAAMC,cAAc,GAAGT,QAAQ,CAACE,MAAT,CAAgBM,MAAvC;;AACA,cAAIF,WAAW,CAACE,MAAZ,KAAuBC,cAA3B,EAA2C;AACvCd,YAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ;AACH,WAFD,MAEO;AACHD,YAAAA,OAAO,CAACC,GAAR,yEAA2Ba,cAA3B,sCAAiDH,WAAW,CAACE,MAA7D;AACH,WA1BmC,CA4BpC;;;AACA,eAAKE,oBAAL,CAA0BJ,WAA1B,EAAuCN,QAAQ,CAACE,MAAhD;AACH;AAED;AACJ;AACA;;;AACYJ,QAAAA,4BAA4B,GAAG;AACnCH,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ;AAEA,cAAMI,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAA,UAAAA,QAAQ,CAACC,QAAT,GAAoB,EAApB,CAJmC,CAMnC;;AACAD,UAAAA,QAAQ,CAACE,MAAT,GAAkB,CACd;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,CADc,EACY;AAC1B;AAAA;AAAA,sCAAc,CAAC,EAAf,EAAmB,GAAnB,CAFc,EAEY;AAC1B;AAAA;AAAA,sCAAc,EAAd,EAAkB,CAAC,GAAnB,CAHc,EAGY;AAC1B;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAJc,CAIY;AAJZ,WAAlB;AAMAF,UAAAA,QAAQ,CAACE,MAAT,CAAgB,CAAhB,EAAmBG,UAAnB,GAAgC,CAAhC,CAbmC,CAaC;;AACpCL,UAAAA,QAAQ,CAACE,MAAT,CAAgB,CAAhB,EAAmBG,UAAnB,GAAgC,CAAhC,CAdmC,CAcC;;AACpCL,UAAAA,QAAQ,CAACE,MAAT,CAAgB,CAAhB,EAAmBG,UAAnB,GAAgC,CAAhC,CAfmC,CAeC;;AACpCL,UAAAA,QAAQ,CAACE,MAAT,CAAgB,CAAhB,EAAmBG,UAAnB,GAAgC,CAAhC,CAhBmC,CAgBC;;AAEpC,cAAMC,WAAW,GAAGN,QAAQ,CAACO,mBAAT,EAApB;AAEAZ,UAAAA,OAAO,CAACC,GAAR,4BAA2BI,QAAQ,CAACC,QAApC;AACAN,UAAAA,OAAO,CAACC,GAAR,sCAAsBU,WAAW,CAACE,MAAlC,EArBmC,CAuBnC;AACA;AACA;;AACA,cAAMG,oBAAoB,GAAG,CAA7B,CA1BmC,CA0BH;;AAChC,cAAMC,mBAAmB,GAAGZ,QAAQ,CAACC,QAArC,CA3BmC,CA2BY;;AAC/C,cAAMY,aAAa,GAAGF,oBAAoB,GAAGC,mBAA7C;AAEAjB,UAAAA,OAAO,CAACC,GAAR,gGAAmCgB,mBAAnC,uBAA8DC,aAA9D;;AAEA,cAAIP,WAAW,CAACE,MAAZ,KAAuBK,aAA3B,EAA0C;AACtClB,YAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ;AACH,WAFD,MAEO;AACHD,YAAAA,OAAO,CAACC,GAAR,yEAA2BiB,aAA3B,sCAAgDP,WAAW,CAACE,MAA5D;AACH;AACJ;AAED;AACJ;AACA;;;AACYT,QAAAA,sBAAsB,GAAG;AAC7BJ,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ;AAEA,cAAMI,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAA,UAAAA,QAAQ,CAACC,QAAT,GAAoB,EAApB,CAJ6B,CAM7B;;AACAD,UAAAA,QAAQ,CAACE,MAAT,GAAkB,CACd;AAAA;AAAA,sCAAc,CAAC,GAAf,EAAoB,CAApB,CADc,EAEd;AAAA;AAAA,sCAAc,CAAd,EAAiB,GAAjB,CAFc,EAGd;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAHc,CAAlB;AAKAF,UAAAA,QAAQ,CAACE,MAAT,CAAgBC,OAAhB,CAAwBC,CAAC,IAAIA,CAAC,CAACC,UAAF,GAAe,CAA5C;AAEA,cAAMC,WAAW,GAAGN,QAAQ,CAACO,mBAAT,EAApB;AAEAZ,UAAAA,OAAO,CAACC,GAAR,4BAA2BI,QAAQ,CAACC,QAApC;AACAN,UAAAA,OAAO,CAACC,GAAR,sCAAsBU,WAAW,CAACE,MAAlC,EAjB6B,CAmB7B;;AACA,cAAMC,cAAc,GAAG,IAAI,CAACT,QAAQ,CAACE,MAAT,CAAgBM,MAAhB,GAAyB,CAA1B,IAA+BR,QAAQ,CAACC,QAAnE;AACAN,UAAAA,OAAO,CAACC,GAAR,mDAA6BI,QAAQ,CAACE,MAAT,CAAgBM,MAAhB,GAAyB,CAAtD,qBAA8DR,QAAQ,CAACC,QAAvE,wBAAwFQ,cAAxF;;AAEA,cAAIH,WAAW,CAACE,MAAZ,KAAuBC,cAA3B,EAA2C;AACvCd,YAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ;AACH,WAFD,MAEO;AACHD,YAAAA,OAAO,CAACC,GAAR,yEAA2Ba,cAA3B,sCAAiDH,WAAW,CAACE,MAA7D;AACH;AACJ;AAED;AACJ;AACA;;;AACYE,QAAAA,oBAAoB,CAACJ,WAAD,EAAsBQ,cAAtB,EAAmD;AAC3EnB,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;;AAEA,eAAK,IAAImB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,cAAc,CAACN,MAAnC,EAA2CO,CAAC,EAA5C,EAAgD;AAC5C,gBAAMC,WAAW,GAAGF,cAAc,CAACC,CAAD,CAAd,CAAkBE,QAAtC;AACA,gBAAMC,QAAQ,GAAGZ,WAAW,CAACS,CAAD,CAA5B;AAEA,gBAAMI,QAAQ,GAAGlC,IAAI,CAACkC,QAAL,CAAcH,WAAd,EAA2BE,QAA3B,CAAjB;;AAEA,gBAAIC,QAAQ,GAAG,KAAf,EAAsB;AAClBxB,cAAAA,OAAO,CAACC,GAAR,cAAkBmB,CAAlB,2CAAgCG,QAAQ,CAACE,CAAT,CAAWC,OAAX,CAAmB,CAAnB,CAAhC,UAA0DH,QAAQ,CAACI,CAAT,CAAWD,OAAX,CAAmB,CAAnB,CAA1D;AACH,aAFD,MAEO;AACH1B,cAAAA,OAAO,CAACC,GAAR,cAAkBmB,CAAlB,0CAA+BI,QAAQ,CAACE,OAAT,CAAiB,CAAjB,CAA/B,wBAA2DL,WAAW,CAACI,CAAvE,UAA6EJ,WAAW,CAACM,CAAzF,wBAAmGJ,QAAQ,CAACE,CAAT,CAAWC,OAAX,CAAmB,CAAnB,CAAnG,UAA6HH,QAAQ,CAACI,CAAT,CAAWD,OAAX,CAAmB,CAAnB,CAA7H;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACWE,QAAAA,eAAe,GAAG;AACrB5B,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EADqB,CAGrB;;AACA,cAAMI,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAA,UAAAA,QAAQ,CAACC,QAAT,GAAoB,EAApB,CALqB,CAKG;AAExB;;AACAD,UAAAA,QAAQ,CAACE,MAAT,GAAkB,EAAlB;;AACA,eAAK,IAAIa,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;AACzB,gBAAMS,KAAK,GAAG;AAAA;AAAA,wCAAcT,CAAC,GAAG,GAAlB,EAAuBU,IAAI,CAACC,GAAL,CAASX,CAAT,IAAc,EAArC,CAAd;AACAS,YAAAA,KAAK,CAACnB,UAAN,GAAmB,CAAnB,CAFyB,CAEH;;AACtBL,YAAAA,QAAQ,CAACE,MAAT,CAAgByB,IAAhB,CAAqBH,KAArB;AACH;;AAED,cAAMI,SAAS,GAAGC,WAAW,CAACC,GAAZ,EAAlB;AACA,cAAMxB,WAAW,GAAGN,QAAQ,CAACO,mBAAT,EAApB;AACA,cAAMwB,OAAO,GAAGF,WAAW,CAACC,GAAZ,EAAhB;AAEAnC,UAAAA,OAAO,CAACC,GAAR,gCAAqBI,QAAQ,CAACE,MAAT,CAAgBM,MAArC;AACAb,UAAAA,OAAO,CAACC,GAAR,4BAA2BI,QAAQ,CAACC,QAApC;AACAN,UAAAA,OAAO,CAACC,GAAR,kDAAwBU,WAAW,CAACE,MAApC;AACAb,UAAAA,OAAO,CAACC,GAAR,gCAAqB,CAACmC,OAAO,GAAGH,SAAX,EAAsBP,OAAtB,CAA8B,CAA9B,CAArB,SAtBqB,CAwBrB;;AACA,cAAMW,iBAAiB,GAAG,IAAI,CAAChC,QAAQ,CAACE,MAAT,CAAgBM,MAAhB,GAAyB,CAA1B,IAA+BR,QAAQ,CAACC,QAAtE;AACA,cAAMgC,eAAe,GAAG3B,WAAW,CAACE,MAApC;AACA,cAAM0B,SAAS,GAAG,CAAC,CAACF,iBAAiB,GAAGC,eAArB,IAAwCD,iBAAxC,GAA4D,GAA7D,EAAkEX,OAAlE,CAA0E,CAA1E,CAAlB;AAEA1B,UAAAA,OAAO,CAACC,GAAR,sCAAsBoC,iBAAtB;AACArC,UAAAA,OAAO,CAACC,GAAR,sCAAsBqC,eAAtB;AACAtC,UAAAA,OAAO,CAACC,GAAR,gCAAqBsC,SAArB;AACH;;AAvLsD,O", "sourcesContent": ["import { _decorator, Component, Vec2 } from 'cc';\nimport { EDITOR } from 'cc/env';\nimport { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\n\nconst { ccclass, executeInEditMode, menu } = _decorator;\n\n/**\n * 路径段优化测试 - 验证直线段的点数优化\n */\n@ccclass('PathSegmentOptimizationTest')\n@menu(\"怪物/编辑器/路径段优化测试\")\n@executeInEditMode(true)\nexport class PathSegmentOptimizationTest extends Component {\n\n    protected onLoad() {\n        if (EDITOR) {\n            this.runOptimizationTests();\n        }\n    }\n\n    private runOptimizationTests() {\n        console.log(\"=== 路径段优化测试开始 ===\");\n        \n        this.testLinearSegmentOptimization();\n        this.testMixedSegmentOptimization();\n        this.testCurveSegmentNormal();\n        \n        console.log(\"=== 路径段优化测试结束 ===\");\n    }\n\n    /**\n     * 测试纯直线段的优化\n     */\n    private testLinearSegmentOptimization() {\n        console.log(\"\\n测试1: 纯直线段优化\");\n        \n        const pathData = new PathData();\n        pathData.segments = 20; // 设置较大的段数\n        \n        // 创建3个点，全部设置为直线\n        pathData.points = [\n            new PathPoint(-100, 0),\n            new PathPoint(0, 100),\n            new PathPoint(100, 0),\n        ];\n        pathData.points.forEach(p => p.smoothness = 0);\n        \n        const curvePoints = pathData.generateCurvePoints();\n        \n        console.log(`segments设置: ${pathData.segments}`);\n        console.log(`生成的点数: ${curvePoints.length}`);\n        console.log(`理论最少点数: ${pathData.points.length} (起点 + 各段终点)`);\n        \n        // 验证点数是否优化\n        const expectedPoints = pathData.points.length;\n        if (curvePoints.length === expectedPoints) {\n            console.log(\"✅ 直线段优化成功：点数已最小化\");\n        } else {\n            console.log(`❌ 直线段优化失败：期望${expectedPoints}个点，实际${curvePoints.length}个点`);\n        }\n        \n        // 验证点的位置是否正确\n        this.validateLinearPoints(curvePoints, pathData.points);\n    }\n\n    /**\n     * 测试混合段的优化（部分直线，部分曲线）\n     */\n    private testMixedSegmentOptimization() {\n        console.log(\"\\n测试2: 混合段优化\");\n        \n        const pathData = new PathData();\n        pathData.segments = 20;\n        \n        // 创建4个点：直线-曲线-直线\n        pathData.points = [\n            new PathPoint(-150, 0),   // smoothness = 0 (直线)\n            new PathPoint(-50, 100),  // smoothness = 0 (直线)\n            new PathPoint(50, -100),  // smoothness = 1 (曲线)\n            new PathPoint(150, 0),    // smoothness = 1 (曲线)\n        ];\n        pathData.points[0].smoothness = 0;  // 第一段：直线\n        pathData.points[1].smoothness = 0;  // 第一段：直线\n        pathData.points[2].smoothness = 1;  // 第二段：曲线\n        pathData.points[3].smoothness = 1;  // 第二段：曲线\n        \n        const curvePoints = pathData.generateCurvePoints();\n        \n        console.log(`segments设置: ${pathData.segments}`);\n        console.log(`生成的点数: ${curvePoints.length}`);\n        \n        // 分析点数分布\n        // 第一段（直线）：应该只有2个点（起点+终点）\n        // 第二段（曲线）：应该有segments+1个点\n        const expectedLinearPoints = 2; // 起点 + 第一段终点\n        const expectedCurvePoints = pathData.segments; // 第二段的segments个点\n        const expectedTotal = expectedLinearPoints + expectedCurvePoints;\n        \n        console.log(`期望点数分布: 直线段2个点 + 曲线段${expectedCurvePoints}个点 = ${expectedTotal}个点`);\n        \n        if (curvePoints.length === expectedTotal) {\n            console.log(\"✅ 混合段优化成功\");\n        } else {\n            console.log(`❌ 混合段优化异常：期望${expectedTotal}个点，实际${curvePoints.length}个点`);\n        }\n    }\n\n    /**\n     * 测试纯曲线段（确保曲线段点数正常）\n     */\n    private testCurveSegmentNormal() {\n        console.log(\"\\n测试3: 纯曲线段正常生成\");\n        \n        const pathData = new PathData();\n        pathData.segments = 10;\n        \n        // 创建3个点，全部设置为曲线\n        pathData.points = [\n            new PathPoint(-100, 0),\n            new PathPoint(0, 100),\n            new PathPoint(100, 0),\n        ];\n        pathData.points.forEach(p => p.smoothness = 1);\n        \n        const curvePoints = pathData.generateCurvePoints();\n        \n        console.log(`segments设置: ${pathData.segments}`);\n        console.log(`生成的点数: ${curvePoints.length}`);\n        \n        // 对于曲线，期望点数 = 起点 + 每段的segments个点\n        const expectedPoints = 1 + (pathData.points.length - 1) * pathData.segments;\n        console.log(`期望点数: 1(起点) + ${pathData.points.length - 1}段 × ${pathData.segments}点/段 = ${expectedPoints}个点`);\n        \n        if (curvePoints.length === expectedPoints) {\n            console.log(\"✅ 曲线段点数正常\");\n        } else {\n            console.log(`❌ 曲线段点数异常：期望${expectedPoints}个点，实际${curvePoints.length}个点`);\n        }\n    }\n\n    /**\n     * 验证直线点的位置是否正确\n     */\n    private validateLinearPoints(curvePoints: Vec2[], originalPoints: PathPoint[]) {\n        console.log(\"验证直线点位置:\");\n        \n        for (let i = 0; i < originalPoints.length; i++) {\n            const originalPos = originalPoints[i].position;\n            const curvePos = curvePoints[i];\n            \n            const distance = Vec2.distance(originalPos, curvePos);\n            \n            if (distance < 0.001) {\n                console.log(`  点${i}: ✅ 位置正确 (${curvePos.x.toFixed(1)}, ${curvePos.y.toFixed(1)})`);\n            } else {\n                console.log(`  点${i}: ❌ 位置偏差 ${distance.toFixed(3)} - 期望(${originalPos.x}, ${originalPos.y}), 实际(${curvePos.x.toFixed(1)}, ${curvePos.y.toFixed(1)})`);\n            }\n        }\n    }\n\n    /**\n     * 性能对比测试\n     */\n    public performanceTest() {\n        console.log(\"\\n=== 性能对比测试 ===\");\n        \n        // 创建一个包含多个直线段的路径\n        const pathData = new PathData();\n        pathData.segments = 50; // 大段数\n        \n        // 创建10个点的直线路径\n        pathData.points = [];\n        for (let i = 0; i < 10; i++) {\n            const point = new PathPoint(i * 100, Math.sin(i) * 50);\n            point.smoothness = 0; // 全部设为直线\n            pathData.points.push(point);\n        }\n        \n        const startTime = performance.now();\n        const curvePoints = pathData.generateCurvePoints();\n        const endTime = performance.now();\n        \n        console.log(`路径点数: ${pathData.points.length}`);\n        console.log(`segments设置: ${pathData.segments}`);\n        console.log(`生成的曲线点数: ${curvePoints.length}`);\n        console.log(`生成时间: ${(endTime - startTime).toFixed(2)}ms`);\n        \n        // 计算优化效果\n        const unoptimizedPoints = 1 + (pathData.points.length - 1) * pathData.segments;\n        const optimizedPoints = curvePoints.length;\n        const reduction = ((unoptimizedPoints - optimizedPoints) / unoptimizedPoints * 100).toFixed(1);\n        \n        console.log(`未优化点数: ${unoptimizedPoints}`);\n        console.log(`优化后点数: ${optimizedPoints}`);\n        console.log(`点数减少: ${reduction}%`);\n    }\n}\n"]}