System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Graphics, Color, CCInteger, Vec2, CCFloat, Enum, PathPoint, eOrientationType, PathEditor, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _class, _class2, _crd, ccclass, property, executeInEditMode, disallowMultiple, menu, requireComponent, PathPointEditor;

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeOrientationType(extras) {
    _reporterNs.report("eOrientationType", "db://assets/bundles/common/script/game/data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathEditor(extras) {
    _reporterNs.report("PathEditor", "./PathEditor", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Graphics = _cc.Graphics;
      Color = _cc.Color;
      CCInteger = _cc.CCInteger;
      Vec2 = _cc.Vec2;
      CCFloat = _cc.CCFloat;
      Enum = _cc.Enum;
    }, function (_unresolved_2) {
      PathPoint = _unresolved_2.PathPoint;
    }, function (_unresolved_3) {
      eOrientationType = _unresolved_3.eOrientationType;
    }, function (_unresolved_4) {
      PathEditor = _unresolved_4.PathEditor;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "77022mnugRFv4miHgaxYrGl", "PathPointEditor", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Graphics', 'Color', 'CCInteger', 'Vec2', 'CCFloat', 'Enum']);

      ({
        ccclass,
        property,
        executeInEditMode,
        disallowMultiple,
        menu,
        requireComponent
      } = _decorator);

      _export("PathPointEditor", PathPointEditor = (_dec = ccclass('PathPointEditor'), _dec2 = menu("怪物/编辑器/路径点"), _dec3 = requireComponent(Graphics), _dec4 = executeInEditMode(true), _dec5 = disallowMultiple(true), _dec6 = property({
        type: CCFloat,
        displayName: "平滑程度",
        range: [0, 1],
        step: 0.1,
        slide: true,
        tooltip: "0=尖锐转角,1=最大平滑"
      }), _dec7 = property({
        type: CCInteger,
        displayName: "速度",
        tooltip: "飞机在此点的速度"
      }), _dec8 = property({
        type: Enum(_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
          error: Error()
        }), eOrientationType) : eOrientationType),
        displayName: "朝向类型",
        tooltip: "飞机在此点的朝向"
      }), _dec9 = property({
        type: CCInteger,
        displayName: "朝向参数",
        tooltip: "固定朝向时：角度值(0-360度)；其他类型暂未使用"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = _dec5(_class = (_class2 = class PathPointEditor extends Component {
        constructor(...args) {
          super(...args);
          // @property({ type: CCFloat, displayName: "点大小" })
          this.pointSize = 20;
          this._pathEditor = null;
          this._pathPoint = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)();
          this._cachedIndex = -1;
          this.selected = false;
        }

        get smoothness() {
          return this._pathPoint.smoothness;
        }

        set smoothness(value) {
          this._pathPoint.smoothness = value;
        }

        get speed() {
          return this._pathPoint.speed;
        }

        set speed(value) {
          this._pathPoint.speed = value;
        }

        get orientationType() {
          return this._pathPoint.orientationType;
        }

        set orientationType(value) {
          this._pathPoint.orientationType = value;
        }

        get orientationParam() {
          return this._pathPoint.orientationParam;
        }

        set orientationParam(value) {
          this._pathPoint.orientationParam = value;
        }

        get pathEditor() {
          if (this._pathEditor === null && this.node.parent) {
            this._pathEditor = this.node.parent.getComponent(_crd && PathEditor === void 0 ? (_reportPossibleCrUseOfPathEditor({
              error: Error()
            }), PathEditor) : PathEditor);
          }

          return this._pathEditor;
        }

        onFocusInEditor() {
          this.selected = true;
        }

        onLostFocusInEditor() {
          this.selected = false;
        }

        get pathPoint() {
          // 同步节点位置到路径点数据
          this._pathPoint.position = new Vec2(this.node.position.x, this.node.position.y);
          return this._pathPoint;
        }

        set pathPoint(value) {
          this._pathPoint = value; // 同步路径点数据到节点位置

          this.node.setPosition(this._pathPoint.x, this._pathPoint.y, 0);
        }

        updateDisplay() {
          let graphics = this.getComponent(Graphics) || this.addComponent(Graphics);
          if (graphics === null) return;
          graphics.clear(); // 绘制点

          const color = this.selected ? Color.YELLOW : this.getDefaultColorByIndex(this._cachedIndex, this.node.parent.children.length);
          graphics.fillColor = color;
          graphics.strokeColor = Color.BLACK;
          graphics.lineWidth = 5;
          graphics.circle(0, 0, this.pointSize);
          graphics.fill();
          graphics.stroke(); // 绘制平滑程度指示器

          if (this._pathPoint.smoothness > 0) {
            graphics.strokeColor = Color.GREEN;
            graphics.lineWidth = 5;
            const radius = this.pointSize + 5 + this._pathPoint.smoothness * 10;
            graphics.circle(0, 0, radius);
            graphics.stroke();
          } // 绘制朝向指示器（箭头）


          if (this._pathPoint.speed > 0) {
            this.drawOrientationArrow(graphics);
          }
        }

        update(_dt) {
          // 检查是否需要重绘
          const currentPosition = new Vec2(this.node.position.x, this.node.position.y);
          const siblingIndex = this.node.getSiblingIndex(); // 检查索引是否改变

          if (siblingIndex !== this._cachedIndex) {
            this._cachedIndex = siblingIndex;
            this.node.name = `Point_${siblingIndex}`;
          }

          this.updateDisplay();
        }

        getDefaultColorByIndex(index, count) {
          var _this$pathEditor;

          const startIdx = ((_this$pathEditor = this.pathEditor) == null ? void 0 : _this$pathEditor.startIdx) || 0; // 起点

          if (index === startIdx) return Color.GREEN; // 终点
          else if (index === count - 1) return Color.RED; // 中间点
          else return Color.WHITE;
        }
        /**
         * 绘制朝向箭头
         */


        drawOrientationArrow(graphics) {
          const arrowLength = Math.min(this._pathPoint.speed / 10, 100);
          const arrowAngle = this.calculateArrowAngle(); // 根据朝向类型设置不同颜色

          graphics.strokeColor = this.getArrowColorByOrientationType();
          graphics.lineWidth = 3; // 计算箭头终点

          const endX = Math.cos(arrowAngle) * arrowLength;
          const endY = Math.sin(arrowAngle) * arrowLength; // 绘制箭头主线

          graphics.moveTo(0, 0);
          graphics.lineTo(endX, endY); // 绘制箭头头部

          const arrowHeadLength = 8;
          const arrowHeadAngle = Math.PI / 6; // 30度

          const leftX = endX - Math.cos(arrowAngle - arrowHeadAngle) * arrowHeadLength;
          const leftY = endY - Math.sin(arrowAngle - arrowHeadAngle) * arrowHeadLength;
          const rightX = endX - Math.cos(arrowAngle + arrowHeadAngle) * arrowHeadLength;
          const rightY = endY - Math.sin(arrowAngle + arrowHeadAngle) * arrowHeadLength;
          graphics.moveTo(leftX, leftY);
          graphics.lineTo(endX, endY);
          graphics.lineTo(rightX, rightY);
          graphics.stroke();
        }
        /**
         * 根据朝向类型计算箭头角度
         */


        calculateArrowAngle() {
          switch (this._pathPoint.orientationType) {
            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).FacingMoveDir:
              return this.calculateMovementDirection();

            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).FacingPlayer:
              return this.calculatePlayerDirection();

            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).Fixed:
              // orientationParam作为固定角度（度）
              return this._pathPoint.orientationParam * Math.PI / 180;

            default:
              return 0;
            // 默认向右
          }
        }
        /**
         * 计算移动方向
         */


        calculateMovementDirection() {
          if (!this.node.parent) return 0;
          const siblings = this.node.parent.children;
          const currentIndex = this.node.getSiblingIndex(); // 如果是第一个点，使用到下一个点的方向

          if (currentIndex === 0 && siblings.length > 1) {
            const nextPoint = siblings[1].position;
            const currentPoint = this.node.position;
            return Math.atan2(nextPoint.y - currentPoint.y, nextPoint.x - currentPoint.x);
          } // 如果是最后一个点，使用从上一个点的方向
          else if (currentIndex === siblings.length - 1 && siblings.length > 1) {
            const prevPoint = siblings[currentIndex - 1].position;
            const currentPoint = this.node.position;
            return Math.atan2(currentPoint.y - prevPoint.y, currentPoint.x - prevPoint.x);
          } // 中间点，使用前后两点的平均方向
          else if (currentIndex > 0 && currentIndex < siblings.length - 1) {
            const prevPoint = siblings[currentIndex - 1].position;
            const nextPoint = siblings[currentIndex + 1].position;
            return Math.atan2(nextPoint.y - prevPoint.y, nextPoint.x - prevPoint.x);
          }

          return 0; // 默认向右
        }
        /**
         * 计算朝向玩家的方向
         */


        calculatePlayerDirection() {
          // 假设玩家在屏幕底部中央 (0, -400)
          // 在实际游戏中，这应该从游戏状态获取玩家位置
          const playerX = 0;
          const playerY = -400;
          const currentPoint = this.node.position;
          return Math.atan2(playerY - currentPoint.y, playerX - currentPoint.x);
        }
        /**
         * 根据朝向类型获取箭头颜色
         */


        getArrowColorByOrientationType() {
          switch (this._pathPoint.orientationType) {
            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).FacingMoveDir:
              return Color.BLUE;
            // 蓝色：跟随移动方向

            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).FacingPlayer:
              return Color.MAGENTA;
            // 紫色：朝向玩家

            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).Fixed:
              return new Color(255, 165, 0, 255);
            // 橙色：固定朝向

            default:
              return Color.BLUE;
            // 默认蓝色
          }
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "smoothness", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "smoothness"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "speed"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "orientationType", [_dec8], Object.getOwnPropertyDescriptor(_class2.prototype, "orientationType"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "orientationParam", [_dec9], Object.getOwnPropertyDescriptor(_class2.prototype, "orientationParam"), _class2.prototype)), _class2)) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0229a09a318268a3f9f18732d54e2001c3b06889.js.map