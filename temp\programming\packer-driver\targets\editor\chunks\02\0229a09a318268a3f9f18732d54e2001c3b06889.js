System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Graphics, Color, Vec3, CCFloat, input, Input, Camera, find, EDITOR, PathPoint, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, executeInEditMode, disallowMultiple, menu, requireComponent, PathPointEditor;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Graphics = _cc.Graphics;
      Color = _cc.Color;
      Vec3 = _cc.Vec3;
      CCFloat = _cc.CCFloat;
      input = _cc.input;
      Input = _cc.Input;
      Camera = _cc.Camera;
      find = _cc.find;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      PathPoint = _unresolved_2.PathPoint;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "77022mnugRFv4miHgaxYrGl", "PathPointEditor", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Graphics', 'Color', 'Vec3', 'CCFloat', 'input', 'Input', 'EventMouse', 'Camera', 'find']);

      ({
        ccclass,
        property,
        executeInEditMode,
        disallowMultiple,
        menu,
        requireComponent
      } = _decorator);

      _export("PathPointEditor", PathPointEditor = (_dec = ccclass('PathPointEditor'), _dec2 = menu("怪物/编辑器/路径点"), _dec3 = requireComponent(Graphics), _dec4 = executeInEditMode(true), _dec5 = disallowMultiple(true), _dec6 = property({
        type: _crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
          error: Error()
        }), PathPoint) : PathPoint,
        displayName: "路径点数据"
      }), _dec7 = property({
        type: CCFloat,
        displayName: "点大小"
      }), _dec8 = property({
        displayName: "是否选中"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = _dec5(_class = (_class2 = class PathPointEditor extends Component {
        constructor(...args) {
          super(...args);
          this._graphics = null;

          _initializerDefineProperty(this, "_pathPoint", _descriptor, this);

          _initializerDefineProperty(this, "pointSize", _descriptor2, this);

          _initializerDefineProperty(this, "selected", _descriptor3, this);

          this._isDragging = false;
          this._dragOffset = new Vec3();
        }

        get graphics() {
          if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
          }

          return this._graphics;
        }

        get pathPoint() {
          // 同步节点位置到路径点数据
          this._pathPoint.position = this.node.position;
          return this._pathPoint;
        }

        set pathPoint(value) {
          this._pathPoint = value; // 同步路径点数据到节点位置

          this.node.position = this._pathPoint.position;
          this.updateDisplay();
        }

        onLoad() {
          if (EDITOR) {
            this.setupEditorEvents();
          }

          this.updateDisplay();
        }

        onDestroy() {
          if (EDITOR) {
            this.removeEditorEvents();
          }
        }

        setupEditorEvents() {
          if (EDITOR) {
            input.on(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);
            input.on(Input.EventType.MOUSE_MOVE, this.onMouseMove, this);
            input.on(Input.EventType.MOUSE_UP, this.onMouseUp, this);
          }
        }

        removeEditorEvents() {
          if (EDITOR) {
            input.off(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);
            input.off(Input.EventType.MOUSE_MOVE, this.onMouseMove, this);
            input.off(Input.EventType.MOUSE_UP, this.onMouseUp, this);
          }
        }

        onMouseDown(event) {
          if (!EDITOR) return;
          const screenPos = event.getLocation();
          const worldPos = this.screenToWorld(new Vec3(screenPos.x, screenPos.y, 0));
          const distance = Vec3.distance(worldPos, this.node.worldPosition);

          if (distance <= this.pointSize) {
            var _this$node$parent;

            this._isDragging = true;
            this._dragOffset = Vec3.subtract(new Vec3(), this.node.worldPosition, worldPos);
            this.selected = true;
            this.updateDisplay(); // 通知父编辑器选中了这个点

            const pathEditor = (_this$node$parent = this.node.parent) == null ? void 0 : _this$node$parent.getComponent('PathEditor');

            if (pathEditor) {
              // @ts-ignore
              pathEditor.selectPoint(this);
            }
          }
        }

        onMouseMove(event) {
          var _this$node$parent2;

          if (!EDITOR || !this._isDragging) return;
          const screenPos = event.getLocation();
          const worldPos = this.screenToWorld(new Vec3(screenPos.x, screenPos.y, 0));
          const newPos = Vec3.add(new Vec3(), worldPos, this._dragOffset); // 转换为本地坐标

          if (this.node.parent) {
            this.node.parent.inverseTransformPoint(newPos, newPos);
          }

          this.node.position = newPos;
          this._pathPoint.position = newPos; // 通知父编辑器更新曲线

          const pathEditor = (_this$node$parent2 = this.node.parent) == null ? void 0 : _this$node$parent2.getComponent('PathEditor');

          if (pathEditor) {
            // @ts-ignore
            pathEditor.updateCurve();
          }
        }

        onMouseUp(_event) {
          if (!EDITOR) return;
          this._isDragging = false;
        }

        screenToWorld(screenPos) {
          const cameraNode = find('Main Camera');
          const camera = cameraNode == null ? void 0 : cameraNode.getComponent(Camera);
          if (!camera) return new Vec3();
          const worldPos = new Vec3();
          camera.screenToWorld(screenPos, worldPos);
          return worldPos;
        }

        updateDisplay() {
          const graphics = this.graphics;
          graphics.clear(); // 绘制点

          const color = this.selected ? Color.YELLOW : Color.WHITE;
          graphics.fillColor = color;
          graphics.strokeColor = Color.BLACK;
          graphics.lineWidth = 2;
          graphics.circle(0, 0, this.pointSize);
          graphics.fill();
          graphics.stroke(); // 绘制平滑程度指示器

          if (this._pathPoint.smoothness > 0) {
            graphics.strokeColor = Color.GREEN;
            graphics.lineWidth = 1;
            const radius = this.pointSize + 5 + this._pathPoint.smoothness * 10;
            graphics.circle(0, 0, radius);
            graphics.stroke();
          } // 绘制速度指示器（箭头）


          if (this._pathPoint.speed > 0) {
            graphics.strokeColor = Color.BLUE;
            graphics.lineWidth = 2;
            const arrowLength = Math.min(this._pathPoint.speed / 10, 50);
            graphics.moveTo(0, 0);
            graphics.lineTo(arrowLength, 0);
            graphics.moveTo(arrowLength - 5, -3);
            graphics.lineTo(arrowLength, 0);
            graphics.lineTo(arrowLength - 5, 3);
            graphics.stroke();
          }
        }

        update(_dt) {
          if (EDITOR) {
            this.updateDisplay();
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "_pathPoint", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)();
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "pointSize", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 20;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "selected", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      })), _class2)) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0229a09a318268a3f9f18732d54e2001c3b06889.js.map